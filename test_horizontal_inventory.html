<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بطاقة المخزون الأفقية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-gradient" style="background: linear-gradient(45deg, #6f42c1, #e83e8c);">
                        <h4 class="mb-0 text-white">
                            <i class="fas fa-clipboard-list me-2"></i>
                            اختبار بطاقة المخزون - التصميم الأفقي الجديد
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-star me-2"></i>الميزات الجديدة والمحسنة في التصميم الأفقي:</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ <strong>تخطيط أفقي منظم:</strong> المعلومات مرتبة في صفوف أفقية</li>
                                        <li>✅ <strong>جدول الدخول والخروج:</strong> عرض مقارن في جدول واحد</li>
                                        <li>✅ <strong>ألوان تفاعلية:</strong> تغيير الألوان حسب مستوى المخزون</li>
                                        <li>✅ <strong>إحصائيات سريعة:</strong> عرض ملخص للمواد</li>
                                        <li>✅ <strong>رقم الوصل محسن:</strong> يظهر بوضوح في الطباعة</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ <strong>إدخال يدوي:</strong> الكمية الموجودة في المخزن تُدخل يدوياً بدون فواصل</li>
                                        <li>✅ <strong>طباعة جميع البطاقات:</strong> في ورقة واحدة مع خط مصغر</li>
                                        <li>✅ <strong>طباعة محسنة:</strong> تصميم خاص للطباعة مع تصغير تلقائي</li>
                                        <li>✅ <strong>تجربة مستخدم أفضل:</strong> واجهة سهلة ومنظمة</li>
                                        <li>✅ <strong>زر طباعة جماعية:</strong> طباعة جميع البطاقات بضغطة واحدة</li>
                                        <li>✅ <strong>أرقام صحيحة:</strong> منع إدخال النقاط والفواصل في الكميات</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- نموذج اختبار -->
                        <form id="testForm">
                            <!-- المعلومات الأساسية -->
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        المعلومات الأساسية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label for="card_number" class="form-label">رقم بطاقة المخزون *</label>
                                            <input type="text" class="form-control" id="card_number" name="card_number" 
                                                   value="INV-TEST-001" required>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="item_name" class="form-label">اسم المادة *</label>
                                            <input type="text" class="form-control" id="item_name" name="item_name" 
                                                   value="أوراق A4 بيضاء عالية الجودة" required>
                                        </div>
                                        
                                        <div class="col-md-3 mb-3">
                                            <label for="unit_of_measure" class="form-label">وحدة القياس *</label>
                                            <select class="form-select" id="unit_of_measure" name="unit_of_measure" required>
                                                <option value="">اختر الوحدة</option>
                                                <option value="قطعة" selected>قطعة</option>
                                                <option value="علبة">علبة</option>
                                                <option value="كيلوغرام">كيلوغرام</option>
                                                <option value="لتر">لتر</option>
                                                <option value="متر">متر</option>
                                                <option value="رزمة">رزمة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات الدخول والخروج -->
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-exchange-alt me-2"></i>
                                        معلومات الدخول والخروج
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="entry_exit_receipt" class="form-label">رقم وصل الدخول/الخروج</label>
                                            <input type="text" class="form-control" id="entry_exit_receipt" name="entry_exit_receipt" 
                                                   value="REC-2024-001">
                                        </div>
                                        
                                        <div class="col-md-8 mb-3">
                                            <label for="supplier_beneficiary" class="form-label">الممون/المستفيد</label>
                                            <input type="text" class="form-control" id="supplier_beneficiary" name="supplier_beneficiary" 
                                                   value="شركة المكتبيات الجزائرية">
                                        </div>
                                    </div>
                                    
                                    <!-- جدول الدخول والخروج الأفقي -->
                                    <div class="alert alert-warning">
                                        <strong>الجدول الأفقي الجديد:</strong> يعرض الدخول والخروج جنباً إلى جنب مع الحساب التلقائي للكمية الحالية
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th colspan="2" class="text-center bg-success text-white">الدخول</th>
                                                    <th colspan="2" class="text-center bg-danger text-white">الخروج</th>
                                                    <th rowspan="2" class="text-center bg-warning text-dark align-middle">الكمية الموجودة في المخزن</th>
                                                </tr>
                                                <tr>
                                                    <th class="text-center">التاريخ</th>
                                                    <th class="text-center">الكمية</th>
                                                    <th class="text-center">التاريخ</th>
                                                    <th class="text-center">الكمية</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input type="date" class="form-control" id="entry_date" name="entry_date" 
                                                               value="2024-01-15">
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control" id="entry_quantity" name="entry_quantity" 
                                                               value="1000" step="0.01" min="0">
                                                    </td>
                                                    <td>
                                                        <input type="date" class="form-control" id="exit_date" name="exit_date" 
                                                               value="2024-01-20">
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control" id="exit_quantity" name="exit_quantity" 
                                                               value="250" step="0.01" min="0">
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control bg-success text-white" id="current_stock" name="current_stock"
                                                               value="750" min="0" step="1" title="الكمية الموجودة في المخزن - أدخل العدد يدوياً"
                                                               placeholder="أدخل الكمية">
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- الملاحظات والإجراءات -->
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-sticky-note me-2"></i>
                                        الملاحظات والإجراءات
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8 mb-3">
                                            <label for="notes" class="form-label">الملاحظات</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3">مادة عالية الجودة مستوردة من أوروبا - يُحفظ في مكان جاف بعيداً عن الرطوبة - صالح للاستعمال لمدة 5 سنوات</textarea>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3 d-flex align-items-end">
                                            <div class="w-100">
                                                <button type="button" class="btn btn-success btn-lg w-100 mb-2" onclick="testSave()">
                                                    <i class="fas fa-save me-2"></i> اختبار الحفظ
                                                </button>
                                                <button type="button" class="btn btn-info w-100" onclick="previewHorizontal()">
                                                    <i class="fas fa-eye me-2"></i> معاينة التصميم
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        
                        <!-- أزرار الاختبار -->
                        <div class="text-center">
                            <h5 class="mb-3">اختبار التصميم الأفقي:</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary btn-lg" onclick="openHorizontalSystem()">
                                    <i class="fas fa-external-link-alt me-2"></i> فتح النظام الأفقي
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg" onclick="compareDesigns()">
                                    <i class="fas fa-balance-scale me-2"></i> مقارنة التصميمين
                                </button>
                                <button type="button" class="btn btn-info btn-lg" onclick="testPrint()">
                                    <i class="fas fa-print me-2"></i> اختبار الطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- مقارنة التصميمين -->
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-comparison me-2"></i>
                            مقارنة التصميم القديم والجديد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">التصميم القديم (العمودي):</h6>
                                <ul class="list-unstyled">
                                    <li>❌ حقول منفصلة عمودياً</li>
                                    <li>❌ صعوبة مقارنة الدخول والخروج</li>
                                    <li>❌ عرض تقليدي للبيانات</li>
                                    <li>❌ حساب يدوي للكمية</li>
                                    <li>❌ لا توجد إحصائيات سريعة</li>
                                    <li>❌ طباعة منفصلة لكل بطاقة</li>
                                    <li>❌ رقم الوصل قد لا يظهر</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">التصميم الجديد (الأفقي المحسن):</h6>
                                <ul class="list-unstyled">
                                    <li>✅ تخطيط أفقي منظم ومرتب</li>
                                    <li>✅ جدول مقارن للدخول والخروج</li>
                                    <li>✅ ألوان تفاعلية حسب المخزون</li>
                                    <li>✅ إدخال يدوي للكمية الموجودة في المخزن (بدون فواصل)</li>
                                    <li>✅ إحصائيات سريعة ومفيدة</li>
                                    <li>✅ طباعة جميع البطاقات في ورقة واحدة</li>
                                    <li>✅ رقم الوصل يظهر بوضوح مع تمييز خاص</li>
                                    <li>✅ تصغير الخط تلقائياً حسب عدد البطاقات</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسين حقل الكمية الموجودة في المخزن (إدخال يدوي)
        document.addEventListener('DOMContentLoaded', function() {
            const currentStock = document.getElementById('current_stock');

            // تحديث لون الخلفية حسب الكمية المدخلة يدوياً
            function updateStockColor() {
                const value = parseInt(currentStock.value) || 0;

                // إزالة جميع الكلاسات السابقة
                currentStock.className = 'form-control ';

                if (value <= 0) {
                    currentStock.className += 'bg-danger text-white';
                } else if (value <= 10) {
                    currentStock.className += 'bg-warning';
                } else {
                    currentStock.className += 'bg-success text-white';
                }
            }

            // منع إدخال النقاط والفواصل
            currentStock.addEventListener('input', function(e) {
                // إزالة أي نقاط أو فواصل
                let value = e.target.value.replace(/[.,]/g, '');

                // التأكد من أن القيمة رقم صحيح موجب
                if (value && !isNaN(value)) {
                    value = Math.max(0, parseInt(value));
                } else {
                    value = '';
                }

                e.target.value = value;
                updateStockColor();
            });

            // منع إدخال النقاط والفواصل من لوحة المفاتيح
            currentStock.addEventListener('keypress', function(e) {
                // منع النقطة والفاصلة والعلامات الأخرى
                if (e.key === '.' || e.key === ',' || e.key === '-' || e.key === '+' || e.key === 'e' || e.key === 'E') {
                    e.preventDefault();
                }
            });

            // تحديث اللون عند التحميل
            updateStockColor();
        });

        // اختبار الحفظ
        function testSave() {
            Swal.fire({
                icon: 'success',
                title: 'تم الاختبار بنجاح!',
                html: `
                    <div class="text-start">
                        <p>✅ التصميم الأفقي يعمل بشكل مثالي</p>
                        <p>✅ الحساب التلقائي للكمية يعمل</p>
                        <p>✅ الألوان التفاعلية تعمل</p>
                        <p>✅ التخطيط الأفقي منظم</p>
                    </div>
                `,
                timer: 3000,
                timerProgressBar: true
            });
        }

        // معاينة التصميم الأفقي
        function previewHorizontal() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            const previewContent = `
                <div class="text-center mb-4">
                    <h3>الجمهورية الجزائرية الديمقراطية الشعبية</h3>
                    <h4>وزارة التربية الوطنية</h4>
                    <h5>الديوان الوطني للامتحانات والمسابقات</h5>
                    <hr>
                    <h4 style="color: #2c5530; border: 2px solid #2c5530; padding: 15px; background-color: #f8f9fa;">بطاقة المخزون</h4>
                </div>
                
                <div class="row mb-3">
                    <div class="col-4"><strong>رقم البطاقة:</strong> ${formData.get('card_number')}</div>
                    <div class="col-8"><strong>اسم المادة:</strong> ${formData.get('item_name')}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-4"><strong>وحدة القياس:</strong> ${formData.get('unit_of_measure')}</div>
                    <div class="col-8"><strong>الممون/المستفيد:</strong> ${formData.get('supplier_beneficiary')}</div>
                </div>
                
                <h6 style="color: #2c5530; margin-top: 30px;">جدول الحركات الأفقي:</h6>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th colspan="2" class="text-center bg-success text-white">الدخول</th>
                            <th colspan="2" class="text-center bg-danger text-white">الخروج</th>
                            <th class="text-center bg-warning text-dark">الكمية الحالية</th>
                        </tr>
                        <tr>
                            <th class="text-center">التاريخ</th>
                            <th class="text-center">الكمية</th>
                            <th class="text-center">التاريخ</th>
                            <th class="text-center">الكمية</th>
                            <th class="text-center">الرصيد</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>${formData.get('entry_date')}</td>
                            <td><strong>${formData.get('entry_quantity')}</strong></td>
                            <td>${formData.get('exit_date')}</td>
                            <td><strong>${formData.get('exit_quantity')}</strong></td>
                            <td style="background-color: #fff3cd;"><strong style="font-size: 18px;">${formData.get('current_stock')}</strong></td>
                        </tr>
                    </tbody>
                </table>
                
                ${formData.get('notes') ? `<div class="mb-3"><strong>الملاحظات:</strong><br>${formData.get('notes')}</div>` : ''}
            `;
            
            Swal.fire({
                title: 'معاينة التصميم الأفقي',
                html: previewContent,
                width: '1000px',
                showConfirmButton: true,
                confirmButtonText: 'ممتاز!',
                customClass: {
                    htmlContainer: 'text-start'
                }
            });
        }

        // فتح النظام الأفقي
        function openHorizontalSystem() {
            window.open('inventory_cards_horizontal.php', '_blank');
        }

        // مقارنة التصميمين
        function compareDesigns() {
            Swal.fire({
                title: 'مقارنة التصميمين',
                html: `
                    <div class="row">
                        <div class="col-6">
                            <h6 class="text-danger">التصميم القديم</h6>
                            <ul class="text-start">
                                <li>حقول عمودية منفصلة</li>
                                <li>صعوبة المقارنة</li>
                                <li>عرض تقليدي</li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <h6 class="text-success">التصميم الجديد</h6>
                            <ul class="text-start">
                                <li>تخطيط أفقي منظم</li>
                                <li>جدول مقارن</li>
                                <li>ألوان تفاعلية</li>
                                <li>حساب تلقائي</li>
                            </ul>
                        </div>
                    </div>
                `,
                width: '800px',
                confirmButtonText: 'فهمت'
            });
        }

        // اختبار الطباعة
        function testPrint() {
            Swal.fire({
                title: 'اختبار الطباعة المحسنة',
                html: `
                    <div class="text-start">
                        <h6>اختر نوع الطباعة:</h6>
                        <div class="btn-group-vertical w-100" role="group">
                            <button type="button" class="btn btn-outline-primary mb-2" onclick="testSinglePrint()">
                                <i class="fas fa-file me-2"></i>طباعة بطاقة واحدة
                            </button>
                            <button type="button" class="btn btn-outline-warning mb-2" onclick="testMultiplePrint()">
                                <i class="fas fa-files me-2"></i>طباعة جميع البطاقات (خط مصغر)
                            </button>
                        </div>
                        <div class="alert alert-info mt-3">
                            <small>
                                <strong>الجديد:</strong> رقم الوصل يظهر بوضوح، والنص تغير إلى "الكمية الموجودة في المخزن"
                            </small>
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إغلاق',
                width: '500px'
            });
        }

        function testSinglePrint() {
            Swal.close();
            window.open('print_inventory_card.php?id=1', '_blank');
        }

        function testMultiplePrint() {
            Swal.close();
            Swal.fire({
                title: 'طباعة جميع البطاقات',
                html: `
                    <div class="text-start">
                        <p>✅ سيتم طباعة جميع البطاقات في ورقة واحدة</p>
                        <p>✅ الخط سيتم تصغيره تلقائياً حسب عدد البطاقات</p>
                        <p>✅ رقم الوصل سيظهر بوضوح مع تمييز خاص</p>
                        <p>✅ النص محدث إلى "الكمية الموجودة في المخزن"</p>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'فتح معاينة الطباعة الجماعية',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.open('print_inventory_card.php?print_all=1', '_blank');
                }
            });
        }
    </script>
</body>
</html>
