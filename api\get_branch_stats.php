<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->query("SELECT 
                           branch_name, 
                           COUNT(*) as total_transfers,
                           SUM(quantity) as total_quantity
                       FROM transfer_vouchers 
                       GROUP BY branch_name 
                       ORDER BY total_transfers DESC");
    
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($stats, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم'], JSON_UNESCAPED_UNICODE);
}
?>
