<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $id = $_GET['id'] ?? '';
    
    if (empty($id)) {
        echo json_encode(['success' => false, 'message' => 'معرف الاستشارة مطلوب'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("SELECT * FROM consultations WHERE id = ?");
    $stmt->execute([$id]);
    $consultation = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($consultation) {
        // تنسيق التاريخ
        if ($consultation['consultation_date']) {
            $consultation['consultation_date'] = date('d/m/Y', strtotime($consultation['consultation_date']));
        }
        
        echo json_encode([
            'success' => true,
            'consultation' => $consultation
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'الاستشارة غير موجودة'
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم'
    ], JSON_UNESCAPED_UNICODE);
}
?>
