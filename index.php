<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'الصفحة الرئيسية';
include 'includes/header.php';

// إحصائيات سريعة
$database = new Database();
$db = $database->getConnection();

// عدد العناصر في المخزون
$stmt = $db->query("SELECT COUNT(*) as total_items FROM inventory_cards");
$total_items = $stmt->fetch()['total_items'];

// عدد الممونين
$stmt = $db->query("SELECT COUNT(*) as total_suppliers FROM suppliers");
$total_suppliers = $stmt->fetch()['total_suppliers'];

// عدد العناصر منخفضة المخزون
$stmt = $db->query("SELECT COUNT(*) as low_stock_items FROM low_stock_items");
$low_stock_items = $stmt->fetch()['low_stock_items'];

// إجمالي قيمة المخزون
$stmt = $db->query("SELECT SUM(current_stock * 1) as total_value FROM inventory_cards WHERE current_stock > 0");
$total_value = $stmt->fetch()['total_value'] ?? 0;
?>

<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم الرئيسية
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- إحصائية العناصر -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي العناصر
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_items); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-boxes fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائية الممونين -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            عدد الممونين
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_suppliers); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-truck fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تنبيهات المخزون المنخفض -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            تنبيهات المخزون
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($low_stock_items); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إجمالي قيمة المخزون -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            قيمة المخزون (تقديرية)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_value, 2); ?> دج
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الوصول السريع -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    الوصول السريع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="inventory_cards.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center py-4">
                            <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                            <h6>بطاقة المخزون</h6>
                            <small class="text-muted">إدارة بطاقات المخزون</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="warehouse_supplies.php" class="btn btn-outline-purple w-100 h-100 d-flex flex-column justify-content-center align-items-center py-4">
                            <i class="fas fa-warehouse fa-3x mb-3"></i>
                            <h6>اللوازم الموجودة في المخزن</h6>
                            <small class="text-muted">إدارة قائمة اللوازم والمواد</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="goods_entry.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center align-items-center py-4">
                            <i class="fas fa-arrow-down fa-3x mb-3"></i>
                            <h6>وصل دخول السلع</h6>
                            <small class="text-muted">تسجيل دخول البضائع</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="office_supplies.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center align-items-center py-4">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <h6>اللوازم المكتبية</h6>
                            <small class="text-muted">طلب واستلام اللوازم</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="suppliers.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center align-items-center py-4">
                            <i class="fas fa-truck fa-3x mb-3"></i>
                            <h6>الممونين</h6>
                            <small class="text-muted">إدارة بيانات الممونين</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="transfer_vouchers.php" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center align-items-center py-4">
                            <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                            <h6>سند التحويل</h6>
                            <small class="text-muted">تحويل البضائع للفروع</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="consultations.php" class="btn btn-outline-dark w-100 h-100 d-flex flex-column justify-content-center align-items-center py-4">
                            <i class="fas fa-file-contract fa-3x mb-3"></i>
                            <h6>الاستشارات</h6>
                            <small class="text-muted">إدارة الاستشارات</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التنبيهات والإشعارات -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>
                    التنبيهات والإشعارات
                </h5>
            </div>
            <div class="card-body">
                <?php if ($low_stock_items > 0): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه!</strong> يوجد <?php echo $low_stock_items; ?> عنصر بمخزون منخفض
                        <br>
                        <a href="reports/low_stock_alert.php" class="btn btn-sm btn-warning mt-2">
                            عرض التفاصيل
                        </a>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        جميع العناصر في المخزون ضمن الحد الآمن
                    </div>
                <?php endif; ?>

                <div class="mt-4">
                    <h6>الإجراءات السريعة:</h6>
                    <div class="d-grid gap-2">
                        <a href="reports/stock_report.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-chart-bar me-1"></i>
                            تقرير المخزون
                        </a>
                        <a href="settings/backup.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download me-1"></i>
                            نسخة احتياطية
                        </a>
                        <button onclick="printPage()" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-print me-1"></i>
                            طباعة التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <p><strong>الإصدار:</strong> 1.0.0</p>
                <p><strong>آخر تحديث:</strong> <?php echo formatArabicDate(date('Y-m-d')); ?></p>
                <p><strong>قاعدة البيانات:</strong> MySQL</p>
                <p><strong>الخادم:</strong> PHP <?php echo phpversion(); ?></p>
                <p id="current-time" class="mb-0"><strong>الوقت الحالي:</strong> جاري التحميل...</p>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.btn:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}
</style>

<?php include 'includes/footer.php'; ?>
