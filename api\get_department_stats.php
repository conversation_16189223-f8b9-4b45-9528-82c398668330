<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // إحصائيات عامة
    $stmt = $db->query("SELECT 
                           COUNT(*) as total,
                           SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                           SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive
                       FROM departments_services_offices");
    $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات حسب النوع
    $stmt = $db->query("SELECT type, COUNT(*) as count 
                       FROM departments_services_offices 
                       WHERE is_active = 1 
                       GROUP BY type 
                       ORDER BY count DESC");
    $type_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $by_type = [];
    foreach ($type_stats as $stat) {
        $by_type[$stat['type']] = $stat['count'];
    }
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'total' => $general_stats['total'],
            'active' => $general_stats['active'],
            'inactive' => $general_stats['inactive'],
            'by_type' => $by_type
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم'
    ], JSON_UNESCAPED_UNICODE);
}
?>
