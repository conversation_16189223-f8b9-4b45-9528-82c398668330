<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'طلب واستلام اللوازم المكتبية - التصميم الجديد';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $receipt_number = sanitizeInput($_POST['receipt_number']);
    $request_date = $_POST['request_date'];
    $beneficiary_directorate = sanitizeInput($_POST['beneficiary_directorate']);
    $recipient_name = sanitizeInput($_POST['recipient_name']);
    $notes = sanitizeInput($_POST['notes']);
    
    // معالجة المواد
    $materials = [];
    if (isset($_POST['materials'])) {
        foreach ($_POST['materials'] as $material) {
            if (!empty($material['item_name'])) {
                $materials[] = [
                    'item_name' => sanitizeInput($material['item_name']),
                    'quantity' => intval($material['quantity']),
                    'unit' => sanitizeInput($material['unit']),
                    'supply_number' => sanitizeInput($material['supply_number'])
                ];
            }
        }
    }
    
    // التوقيعات
    $signature_warehouse_manager = isset($_POST['signature_warehouse_manager']);
    $signature_means_chief = isset($_POST['signature_means_chief']);
    $signature_sub_director = isset($_POST['signature_sub_director']);
    $signature_beneficiary = isset($_POST['signature_beneficiary']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE office_supplies_requests SET 
                    receipt_number = ?, beneficiary_directorate = ?, recipient_name = ?, 
                    request_date = ?, materials = ?, notes = ?,
                    signature_means_chief = ?, signature_sub_director = ?, 
                    signature_warehouse_manager = ?, signature_beneficiary = ?
                    WHERE id = ?");
                
                $stmt->execute([
                    $receipt_number, $beneficiary_directorate, $recipient_name,
                    $request_date, json_encode($materials, JSON_UNESCAPED_UNICODE), $notes,
                    $signature_means_chief, $signature_sub_director,
                    $signature_warehouse_manager, $signature_beneficiary, $_POST['id']
                ]);
                
                $success_message = 'تم تحديث طلب اللوازم المكتبية بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO office_supplies_requests 
                    (receipt_number, beneficiary_directorate, recipient_name, request_date, 
                     materials, notes, signature_means_chief, 
                     signature_sub_director, signature_warehouse_manager, signature_beneficiary) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $receipt_number, $beneficiary_directorate, $recipient_name, $request_date,
                    json_encode($materials, JSON_UNESCAPED_UNICODE), $notes, $signature_means_chief,
                    $signature_sub_director, $signature_warehouse_manager, $signature_beneficiary
                ]);
                
                $success_message = 'تم إضافة طلب اللوازم المكتبية بنجاح';
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        $stmt = $db->prepare("DELETE FROM office_supplies_requests WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف الطلب بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT * FROM office_supplies_requests WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
    if ($edit_data && isset($edit_data['materials']) && !empty($edit_data['materials'])) {
        $edit_data['materials'] = json_decode($edit_data['materials'], true) ?: [];
    } else {
        $edit_data['materials'] = [];
    }
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE receipt_number LIKE ? OR beneficiary_directorate LIKE ? OR recipient_name LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT * FROM office_supplies_requests $where_clause ORDER BY request_date DESC, created_at DESC");
$stmt->execute($params);
$requests = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>
                    طلب واستلام اللوازم المكتبية - التصميم الجديد
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- رأس المؤسسة للطباعة -->
                <div class="institution-header d-none d-print-block">
                    <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
                    <h2><?php echo MINISTRY_NAME_AR; ?></h2>
                    <h3><?php echo OFFICE_NAME_AR; ?></h3>
                </div>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate id="officeSuppliesForm">
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="receipt_number" class="form-label">رقم الوصل *</label>
                            <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                                   value="<?php echo $edit_data['receipt_number'] ?? generateReferenceNumber('OSR-'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال رقم الوصل</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="request_date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="request_date" name="request_date" 
                                   value="<?php echo $edit_data['request_date'] ?? date('Y-m-d'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال التاريخ</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="beneficiary_directorate" class="form-label">المديرية الفرعية أو المصلحة المستفيدة *</label>
                            <select class="form-select" id="beneficiary_directorate" name="beneficiary_directorate" required>
                                <option value="">اختر المديرية أو المصلحة</option>
                                <?php
                                // جلب المديريات والمصالح من قاعدة البيانات
                                try {
                                    $stmt = $db->query("SELECT directorate_name FROM directorates_services WHERE is_active = 1 ORDER BY directorate_name");
                                    $directorates = $stmt->fetchAll();

                                    foreach ($directorates as $directorate) {
                                        $selected = ($edit_data && $edit_data['beneficiary_directorate'] == $directorate['directorate_name']) ? 'selected' : '';
                                        echo '<option value="' . htmlspecialchars($directorate['directorate_name']) . '" ' . $selected . '>';
                                        echo htmlspecialchars($directorate['directorate_name']);
                                        echo '</option>';
                                    }
                                } catch (PDOException $e) {
                                    // في حالة عدم وجود الجدول، استخدم قائمة افتراضية
                                    $default_directorates = [
                                        'مديرية الامتحانات',
                                        'مديرية المسابقات',
                                        'مديرية الإدارة العامة',
                                        'مديرية الوسائل',
                                        'مصلحة المحاسبة',
                                        'مصلحة الموارد البشرية',
                                        'مصلحة الصيانة',
                                        'مصلحة الأمن'
                                    ];

                                    foreach ($default_directorates as $directorate) {
                                        $selected = ($edit_data && $edit_data['beneficiary_directorate'] == $directorate) ? 'selected' : '';
                                        echo '<option value="' . htmlspecialchars($directorate) . '" ' . $selected . '>';
                                        echo htmlspecialchars($directorate);
                                        echo '</option>';
                                    }
                                }
                                ?>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار المديرية المستفيدة</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="recipient_name" class="form-label">اسم ولقب المستلم *</label>
                            <input type="text" class="form-control" id="recipient_name" name="recipient_name" 
                                   value="<?php echo $edit_data['recipient_name'] ?? ''; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم ولقب المستلم</div>
                        </div>
                    </div>
                    
                    <!-- جدول المواد -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">جدول المواد المطلوبة</h6>
                            <button type="button" class="btn btn-sm btn-success" onclick="addMaterialRow()">
                                <i class="fas fa-plus me-1"></i> إضافة مادة
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="materialsTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="5%">الرقم</th>
                                            <th width="35%">اسم المادة</th>
                                            <th width="15%">الكمية</th>
                                            <th width="15%">الوحدة</th>
                                            <th width="20%">رقم اللوازم</th>
                                            <th width="10%">حذف</th>
                                        </tr>
                                    </thead>
                                    <tbody id="materialsTableBody">
                                        <?php if ($edit_data && !empty($edit_data['materials'])): ?>
                                            <?php foreach ($edit_data['materials'] as $index => $material): ?>
                                                <tr>
                                                    <td><?php echo $index + 1; ?></td>
                                                    <td><input type="text" class="form-control" name="materials[<?php echo $index; ?>][item_name]" value="<?php echo htmlspecialchars($material['item_name']); ?>" required></td>
                                                    <td><input type="number" class="form-control" name="materials[<?php echo $index; ?>][quantity]" value="<?php echo $material['quantity']; ?>" min="1" required></td>
                                                    <td><input type="text" class="form-control" name="materials[<?php echo $index; ?>][unit]" value="<?php echo htmlspecialchars($material['unit']); ?>" placeholder="قطعة، علبة، رزمة..."></td>
                                                    <td><input type="text" class="form-control" name="materials[<?php echo $index; ?>][supply_number]" value="<?php echo htmlspecialchars($material['supply_number']); ?>"></td>
                                                    <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td>1</td>
                                                <td><input type="text" class="form-control" name="materials[0][item_name]" placeholder="أدخل اسم المادة" required></td>
                                                <td><input type="number" class="form-control" name="materials[0][quantity]" value="1" min="1" required></td>
                                                <td><input type="text" class="form-control" name="materials[0][unit]" placeholder="قطعة، علبة، رزمة..."></td>
                                                <td><input type="text" class="form-control" name="materials[0][supply_number]" placeholder="رقم اللوازم (اختياري)"></td>
                                                <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">الملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $edit_data['notes'] ?? ''; ?></textarea>
                    </div>
                    
                    <!-- التوقيعات الجديدة 2x2 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">التوقيعات (2×2)</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- الصف الأول -->
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_warehouse_manager" 
                                               name="signature_warehouse_manager" 
                                               <?php echo ($edit_data['signature_warehouse_manager'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_warehouse_manager">
                                            إمضاء وختم المكلف بتسيير المخزن
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_means_chief" 
                                               name="signature_means_chief" 
                                               <?php echo ($edit_data['signature_means_chief'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_means_chief">
                                            إمضاء وختم رئيس مصلحة الوسائل
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <!-- الصف الثاني -->
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_sub_director" 
                                               name="signature_sub_director" 
                                               <?php echo ($edit_data['signature_sub_director'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_sub_director">
                                            إمضاء وختم المدير الفرعي للإدارة العامة
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_beneficiary" 
                                               name="signature_beneficiary" 
                                               <?php echo ($edit_data['signature_beneficiary'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_beneficiary">
                                            إمضاء المستفيد
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="office_supplies_new.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary" onclick="clearForm()">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>

                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-info" onclick="previewPrint()">
                                    <i class="fas fa-eye me-1"></i> معاينة قبل الطباعة
                                </button>
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-info dropdown-toggle"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-download me-1"></i> تنزيل
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="downloadFile('pdf')">
                                            <i class="fas fa-file-pdf me-2 text-danger"></i> PDF
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="downloadFile('docx')">
                                            <i class="fas fa-file-word me-2 text-primary"></i> DOCX
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="downloadFile('doc')">
                                            <i class="fas fa-file-word me-2 text-info"></i> DOC
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الطلبات
                </h5>

                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2"
                           placeholder="بحث برقم الوصل أو المديرية..."
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="office_supplies_new.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الوصل</th>
                                <th>التاريخ</th>
                                <th>المديرية المستفيدة</th>
                                <th>المستلم</th>
                                <th>عدد المواد</th>
                                <th>التوقيعات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($requests)): ?>
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($requests as $request): ?>
                                    <?php
                                    $materials = [];
                                    if (isset($request['materials']) && !empty($request['materials'])) {
                                        $materials = json_decode($request['materials'], true) ?: [];
                                    }
                                    $materials_count = count($materials);
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($request['receipt_number']); ?></strong>
                                        </td>
                                        <td><?php echo formatArabicDate($request['request_date']); ?></td>
                                        <td><?php echo htmlspecialchars($request['beneficiary_directorate']); ?></td>
                                        <td><?php echo htmlspecialchars($request['recipient_name']); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $materials_count; ?> مادة</span>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <?php if ($request['signature_warehouse_manager']): ?>
                                                    <span class="badge bg-warning" title="إمضاء وختم المكلف بتسيير المخزن">م</span>
                                                <?php endif; ?>
                                                <?php if ($request['signature_means_chief']): ?>
                                                    <span class="badge bg-primary" title="إمضاء وختم رئيس مصلحة الوسائل">ر</span>
                                                <?php endif; ?>
                                                <?php if ($request['signature_sub_director']): ?>
                                                    <span class="badge bg-success" title="إمضاء وختم المدير الفرعي للإدارة العامة">د</span>
                                                <?php endif; ?>
                                                <?php if ($request['signature_beneficiary']): ?>
                                                    <span class="badge bg-info" title="إمضاء المستفيد">س</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $request['id']; ?>"
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteRequest(<?php echo $request['id']; ?>)"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button onclick="viewRequest(<?php echo $request['id']; ?>)"
                                                        class="btn btn-outline-info" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let materialCounter = <?php echo $edit_data && $edit_data['materials'] ? count($edit_data['materials']) : 1; ?>;

// إضافة صف مادة جديد
function addMaterialRow() {
    const tbody = document.getElementById('materialsTableBody');
    const row = document.createElement('tr');

    row.innerHTML = `
        <td>${materialCounter + 1}</td>
        <td><input type="text" class="form-control" name="materials[${materialCounter}][item_name]" placeholder="أدخل اسم المادة" required></td>
        <td><input type="number" class="form-control" name="materials[${materialCounter}][quantity]" value="1" min="1" required></td>
        <td><input type="text" class="form-control" name="materials[${materialCounter}][unit]" placeholder="قطعة، علبة، رزمة..."></td>
        <td><input type="text" class="form-control" name="materials[${materialCounter}][supply_number]" placeholder="رقم اللوازم (اختياري)"></td>
        <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
    `;

    tbody.appendChild(row);
    materialCounter++;
    updateRowNumbers();
}

// حذف صف مادة
function removeMaterialRow(button) {
    const row = button.closest('tr');
    const tbody = document.getElementById('materialsTableBody');

    // التأكد من وجود صف واحد على الأقل
    if (tbody.children.length > 1) {
        row.remove();
        updateRowNumbers();
    } else {
        showWarning('يجب أن يحتوي الجدول على مادة واحدة على الأقل');
    }
}

// تحديث أرقام الصفوف
function updateRowNumbers() {
    const rows = document.querySelectorAll('#materialsTableBody tr');
    rows.forEach((row, index) => {
        row.cells[0].textContent = index + 1;
    });
}

// مسح النموذج
function clearForm() {
    const tbody = document.getElementById('materialsTableBody');
    tbody.innerHTML = `
        <tr>
            <td>1</td>
            <td><input type="text" class="form-control" name="materials[0][item_name]" placeholder="أدخل اسم المادة" required></td>
            <td><input type="number" class="form-control" name="materials[0][quantity]" value="1" min="1" required></td>
            <td><input type="text" class="form-control" name="materials[0][unit]" placeholder="قطعة، علبة، رزمة..."></td>
            <td><input type="text" class="form-control" name="materials[0][supply_number]" placeholder="رقم اللوازم (اختياري)"></td>
            <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
        </tr>
    `;
    materialCounter = 1;
}

// تنزيل الملف
function downloadFile(format) {
    const form = document.getElementById('officeSuppliesForm');
    const formData = new FormData(form);

    // التحقق من وجود البيانات المطلوبة
    if (!formData.get('receipt_number')) {
        showWarning('يرجى ملء البيانات الأساسية قبل التنزيل');
        return;
    }

    showLoading('جاري إنشاء الملف...');

    // إنشاء نموذج مخفي للإرسال
    const hiddenForm = document.createElement('form');
    hiddenForm.method = 'POST';
    hiddenForm.action = `export/office_supplies_new_export.php?format=${format}`;
    hiddenForm.style.display = 'none';

    // إضافة البيانات
    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        hiddenForm.appendChild(input);
    }

    document.body.appendChild(hiddenForm);
    hiddenForm.submit();
    document.body.removeChild(hiddenForm);

    setTimeout(() => {
        hideLoading();
        showSuccess(`تم إنشاء ملف ${format.toUpperCase()} بنجاح`);
    }, 2000);
}

// حذف الطلب
function deleteRequest(id) {
    confirmDelete('هل أنت متأكد من حذف هذا الطلب؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// عرض تفاصيل الطلب
function viewRequest(id) {
    window.location.href = `?action=edit&id=${id}`;
}

// طباعة الصفحة
function printPage() {
    window.print();
}
</script>

<?php include 'includes/footer.php'; ?>
