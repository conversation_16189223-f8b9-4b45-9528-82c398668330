/**
 * الملف الرئيسي للجافا سكريبت
 * نظام تسيير المخزن - الديوان الوطني للامتحانات والمسابقات
 */

// المتغيرات العامة
let currentLanguage = 'ar';
let isLoading = false;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * تهيئة النظام
 */
function initializeSystem() {
    // تفعيل tooltips
    initializeTooltips();
    
    // تفعيل popovers
    initializePopovers();
    
    // تفعيل التحقق من صحة النماذج
    initializeFormValidation();
    
    // تفعيل تأثيرات الأزرار
    initializeButtonEffects();
    
    // تحديث الوقت
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // تفعيل الحفظ التلقائي
    initializeAutoSave();
    
    // تفعيل اختصارات لوحة المفاتيح
    initializeKeyboardShortcuts();
    
    console.log('تم تهيئة النظام بنجاح');
}

/**
 * تفعيل tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تفعيل popovers
 */
function initializePopovers() {
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * تفعيل التحقق من صحة النماذج
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                showError('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * تفعيل تأثيرات الأزرار
 */
function initializeButtonEffects() {
    document.querySelectorAll('.btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            // تأثير الموجة
            createRippleEffect(e, this);
        });
    });
}

/**
 * إنشاء تأثير الموجة
 */
function createRippleEffect(event, element) {
    const ripple = document.createElement('span');
    ripple.classList.add('ripple');
    element.appendChild(ripple);
    
    const rect = element.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * تحديث الوقت الحالي
 */
function updateCurrentTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Africa/Algiers'
    };
    
    const timeString = now.toLocaleDateString('ar-DZ', options);
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.innerHTML = '<strong>الوقت الحالي:</strong> ' + timeString;
    }
}

/**
 * عرض رسالة نجاح
 */
function showSuccess(message, title = 'تم بنجاح!') {
    Swal.fire({
        title: title,
        text: message,
        icon: 'success',
        confirmButtonText: 'موافق',
        timer: 3000,
        timerProgressBar: true,
        toast: true,
        position: 'top-end',
        showConfirmButton: false
    });
}

/**
 * عرض رسالة خطأ
 */
function showError(message, title = 'خطأ!') {
    Swal.fire({
        title: title,
        text: message,
        icon: 'error',
        confirmButtonText: 'موافق',
        confirmButtonColor: '#d33'
    });
}

/**
 * عرض رسالة تحذير
 */
function showWarning(message, title = 'تحذير!') {
    Swal.fire({
        title: title,
        text: message,
        icon: 'warning',
        confirmButtonText: 'موافق',
        confirmButtonColor: '#f39c12'
    });
}

/**
 * تأكيد الحذف
 */
function confirmDelete(message = 'هل أنت متأكد من الحذف؟', title = 'تأكيد الحذف') {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    });
}

/**
 * تأكيد الإجراء
 */
function confirmAction(message, title = 'تأكيد الإجراء') {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، تأكيد',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    });
}

/**
 * عرض شاشة التحميل
 */
function showLoading(element = null) {
    isLoading = true;
    if (element) {
        element.classList.add('loading');
        element.disabled = true;
    } else {
        Swal.fire({
            title: 'جاري التحميل...',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoading(element = null) {
    isLoading = false;
    if (element) {
        element.classList.remove('loading');
        element.disabled = false;
    } else {
        Swal.close();
    }
}

/**
 * طباعة الصفحة
 */
function printPage() {
    window.print();
}

/**
 * تصدير إلى PDF
 */
function exportToPDF() {
    showWarning('وظيفة التصدير إلى PDF قيد التطوير');
}

/**
 * إرسال عبر البريد الإلكتروني
 */
function sendEmail(type = 'gmail') {
    const subject = encodeURIComponent(document.title);
    const body = encodeURIComponent('مرفق مستند من نظام تسيير المخزن');
    
    let emailUrl = '';
    switch(type) {
        case 'gmail':
            emailUrl = `https://mail.google.com/mail/?view=cm&fs=1&su=${subject}&body=${body}`;
            break;
        case 'outlook':
            emailUrl = `https://outlook.live.com/mail/0/deeplink/compose?subject=${subject}&body=${body}`;
            break;
        case 'yahoo':
            emailUrl = `https://compose.mail.yahoo.com/?subject=${subject}&body=${body}`;
            break;
    }
    
    if (emailUrl) {
        window.open(emailUrl, '_blank');
    }
}

/**
 * إرسال عبر WhatsApp
 */
function sendWhatsApp() {
    const text = encodeURIComponent('مستند من نظام تسيير المخزن - ' + document.title);
    const whatsappUrl = `https://wa.me/?text=${text}`;
    window.open(whatsappUrl, '_blank');
}

/**
 * تحويل الأرقام إلى العربية
 */
function convertToArabicNumbers(number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    let result = number.toString();
    for (let i = 0; i < englishNumbers.length; i++) {
        result = result.replace(new RegExp(englishNumbers[i], 'g'), arabicNumbers[i]);
    }
    return result;
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number, decimals = 2) {
    return new Intl.NumberFormat('ar-DZ', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'DZD') {
    const currencySymbols = {
        'DZD': 'دج',
        'USD': '$',
        'EUR': '€',
        'GBP': '£'
    };
    
    const formattedAmount = formatNumber(amount);
    const symbol = currencySymbols[currency] || currency;
    
    return `${formattedAmount} ${symbol}`;
}

/**
 * تحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * تحقق من صحة رقم الهاتف
 */
function isValidPhone(phone) {
    const phoneRegex = /^(\+213|0)[5-7][0-9]{8}$/;
    return phoneRegex.test(phone);
}

/**
 * تنظيف النص
 */
function sanitizeText(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * تفعيل الحفظ التلقائي
 */
function initializeAutoSave() {
    const forms = document.querySelectorAll('form[data-autosave="true"]');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                autoSaveForm(form);
            });
        });
    });
}

/**
 * حفظ النموذج تلقائياً
 */
function autoSaveForm(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // حفظ في localStorage
    const formId = form.id || 'default-form';
    localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
    
    console.log('تم الحفظ التلقائي للنموذج:', formId);
}

/**
 * استرداد البيانات المحفوظة تلقائياً
 */
function restoreAutoSavedData(formId) {
    const savedData = localStorage.getItem(`autosave_${formId}`);
    if (savedData) {
        const data = JSON.parse(savedData);
        const form = document.getElementById(formId);
        
        if (form) {
            Object.keys(data).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    input.value = data[key];
                }
            });
        }
    }
}

/**
 * تفعيل اختصارات لوحة المفاتيح
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+S للحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveBtn = document.querySelector('.btn-save, [data-action="save"]');
            if (saveBtn) {
                saveBtn.click();
            }
        }
        
        // Ctrl+P للطباعة
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printPage();
        }
        
        // Ctrl+N لإضافة جديد
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const newBtn = document.querySelector('.btn-new, [data-action="new"]');
            if (newBtn) {
                newBtn.click();
            }
        }
        
        // F3 للبحث
        if (e.key === 'F3') {
            e.preventDefault();
            const searchInput = document.querySelector('input[type="search"], .search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
}

/**
 * تحديث حالة الاتصال
 */
function updateConnectionStatus() {
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
        if (navigator.onLine) {
            statusElement.innerHTML = '<i class="fas fa-wifi text-success"></i> متصل';
            statusElement.className = 'badge bg-success';
        } else {
            statusElement.innerHTML = '<i class="fas fa-wifi-slash text-danger"></i> غير متصل';
            statusElement.className = 'badge bg-danger';
        }
    }
}

// مراقبة حالة الاتصال
window.addEventListener('online', updateConnectionStatus);
window.addEventListener('offline', updateConnectionStatus);

/**
 * تصدير البيانات
 */
function exportData(format = 'excel') {
    showLoading();
    
    setTimeout(() => {
        hideLoading();
        showWarning(`وظيفة التصدير إلى ${format} قيد التطوير`);
    }, 1000);
}

/**
 * استيراد البيانات
 */
function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls,.csv';
    
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            showLoading();
            
            // محاكاة عملية الاستيراد
            setTimeout(() => {
                hideLoading();
                showSuccess('تم استيراد البيانات بنجاح');
            }, 2000);
        }
    };
    
    input.click();
}

// تصدير الدوال للاستخدام العام
window.WarehouseSystem = {
    showSuccess,
    showError,
    showWarning,
    confirmDelete,
    confirmAction,
    showLoading,
    hideLoading,
    printPage,
    exportToPDF,
    sendEmail,
    sendWhatsApp,
    formatNumber,
    formatCurrency,
    isValidEmail,
    isValidPhone,
    exportData,
    importData
};
