<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $item_name = $_GET['item_name'] ?? '';
    
    if (empty($item_name)) {
        echo json_encode(['exists' => false], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("SELECT current_stock, unit_of_measure 
                         FROM inventory_cards 
                         WHERE item_name = ? 
                         LIMIT 1");
    $stmt->execute([$item_name]);
    $item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($item) {
        echo json_encode([
            'exists' => true,
            'current_stock' => $item['current_stock'],
            'unit_of_measure' => $item['unit_of_measure']
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode(['exists' => false], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم'], JSON_UNESCAPED_UNICODE);
}
?>
