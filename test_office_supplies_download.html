<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنزيل طلب اللوازم المكتبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-download me-2"></i>
                            اختبار تنزيل طلب اللوازم المكتبية
                        </h4>
                    </div>
                    <div class="card-body">
                        <form id="testForm" method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="receipt_number" class="form-label">رقم الوصل</label>
                                    <input type="text" class="form-control" id="receipt_number" name="receipt_number" value="TEST-001" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="request_date" class="form-label">التاريخ</label>
                                    <input type="date" class="form-control" id="request_date" name="request_date" value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="beneficiary_directorate" class="form-label">المديرية المستفيدة</label>
                                <input type="text" class="form-control" id="beneficiary_directorate" name="beneficiary_directorate" value="مديرية الامتحانات" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="recipient_name" class="form-label">اسم المستلم</label>
                                    <input type="text" class="form-control" id="recipient_name" name="recipient_name" value="أحمد محمد" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="item_name" class="form-label">اسم المادة</label>
                                    <input type="text" class="form-control" id="item_name" name="item_name" value="أوراق A4" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="quantity" class="form-label">الكمية</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" value="100" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="supply_number" class="form-label">رقم اللوازم</label>
                                    <input type="text" class="form-control" id="supply_number" name="supply_number" value="SUP-123">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">الملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">طلب عاجل للامتحانات</textarea>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label">التوقيعات</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="signature_warehouse_manager" name="signature_warehouse_manager" checked>
                                            <label class="form-check-label" for="signature_warehouse_manager">
                                                إمضاء وختم المكلف بتسيير المخزن
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="signature_means_chief" name="signature_means_chief" checked>
                                            <label class="form-check-label" for="signature_means_chief">
                                                إمضاء وختم رئيس مصلحة الوسائل
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="signature_sub_director" name="signature_sub_director">
                                            <label class="form-check-label" for="signature_sub_director">
                                                إمضاء وختم المدير الفرعي للإدارة العامة
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="signature_beneficiary" name="signature_beneficiary" checked>
                                            <label class="form-check-label" for="signature_beneficiary">
                                                إمضاء المستفيد
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-danger" onclick="downloadFile('pdf')">
                                        <i class="fas fa-file-pdf me-1"></i> تنزيل PDF
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="downloadFile('docx')">
                                        <i class="fas fa-file-word me-1"></i> تنزيل DOCX
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="downloadFile('doc')">
                                        <i class="fas fa-file-word me-1"></i> تنزيل DOC
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">روابط الاختبار المباشرة</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="test_download.php?format=pdf" class="btn btn-outline-danger" target="_blank">
                                <i class="fas fa-file-pdf me-2"></i> اختبار PDF
                            </a>
                            <a href="test_download.php?format=docx" class="btn btn-outline-primary" target="_blank">
                                <i class="fas fa-file-word me-2"></i> اختبار DOCX
                            </a>
                            <a href="test_download.php?format=doc" class="btn btn-outline-info" target="_blank">
                                <i class="fas fa-file-word me-2"></i> اختبار DOC
                            </a>
                            <a href="test_download.php" class="btn btn-outline-success" target="_blank">
                                <i class="fas fa-eye me-2"></i> معاينة HTML
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function downloadFile(format) {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            // إنشاء نموذج مخفي للإرسال
            const hiddenForm = document.createElement('form');
            hiddenForm.method = 'POST';
            hiddenForm.action = `export/office_supplies_export.php?format=${format}`;
            hiddenForm.style.display = 'none';
            
            // إضافة البيانات
            for (let [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                hiddenForm.appendChild(input);
            }
            
            document.body.appendChild(hiddenForm);
            hiddenForm.submit();
            document.body.removeChild(hiddenForm);
        }
    </script>
</body>
</html>
