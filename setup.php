<?php
/**
 * ملف إعداد النظام وقاعدة البيانات
 * نظام تسيير المخزن - الديوان الوطني للامتحانات والمسابقات
 */

require_once 'config/config.php';

$page_title = 'إعداد النظام';
$setup_completed = false;
$error_message = '';
$success_message = '';

// التحقق من وجود قاعدة البيانات
function checkDatabaseExists() {
    try {
        $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
        $stmt = $pdo->query("SHOW DATABASES LIKE 'warehouse_management'");
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// إنشاء قاعدة البيانات
function createDatabase() {
    try {
        // قراءة ملف SQL
        $sql_file = 'database/warehouse_management.sql';
        if (!file_exists($sql_file)) {
            throw new Exception('ملف قاعدة البيانات غير موجود');
        }
        
        $sql_content = file_get_contents($sql_file);
        
        // الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql_content);
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $pdo->exec($query);
            }
        }
        
        return true;
    } catch (Exception $e) {
        throw new Exception('خطأ في إنشاء قاعدة البيانات: ' . $e->getMessage());
    }
}

// معالجة طلب الإعداد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_database'])) {
    try {
        if (checkDatabaseExists()) {
            $error_message = 'قاعدة البيانات موجودة بالفعل';
        } else {
            createDatabase();
            $success_message = 'تم إنشاء قاعدة البيانات بنجاح';
            $setup_completed = true;
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// التحقق من حالة النظام
$database_exists = checkDatabaseExists();
$php_version_ok = version_compare(PHP_VERSION, '7.4.0', '>=');
$mysql_available = extension_loaded('pdo_mysql');
$required_dirs = ['uploads', 'logs', 'assets/images'];
$dirs_writable = true;

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    if (!is_writable($dir)) {
        $dirs_writable = false;
        break;
    }
}

include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    إعداد نظام تسيير المخزن
                </h4>
            </div>
            <div class="card-body">
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <!-- فحص متطلبات النظام -->
                <div class="mb-4">
                    <h5>فحص متطلبات النظام</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>المتطلب</th>
                                    <th>الحالة</th>
                                    <th>التفاصيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>إصدار PHP</td>
                                    <td>
                                        <?php if ($php_version_ok): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> متوفر
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times"></i> غير متوفر
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        الإصدار الحالي: <?php echo PHP_VERSION; ?>
                                        (المطلوب: 7.4.0 أو أحدث)
                                    </td>
                                </tr>
                                <tr>
                                    <td>MySQL PDO</td>
                                    <td>
                                        <?php if ($mysql_available): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> متوفر
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times"></i> غير متوفر
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>امتداد PDO MySQL مطلوب للاتصال بقاعدة البيانات</td>
                                </tr>
                                <tr>
                                    <td>صلاحيات الكتابة</td>
                                    <td>
                                        <?php if ($dirs_writable): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> متوفرة
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times"></i> غير متوفرة
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>صلاحيات الكتابة في مجلدات: uploads, logs, assets/images</td>
                                </tr>
                                <tr>
                                    <td>قاعدة البيانات</td>
                                    <td>
                                        <?php if ($database_exists): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check"></i> موجودة
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-exclamation-triangle"></i> غير موجودة
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>قاعدة البيانات warehouse_management</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- إعداد قاعدة البيانات -->
                <?php if (!$database_exists && $php_version_ok && $mysql_available): ?>
                    <div class="mb-4">
                        <h5>إعداد قاعدة البيانات</h5>
                        <p class="text-muted">
                            سيتم إنشاء قاعدة البيانات والجداول المطلوبة للنظام.
                        </p>
                        
                        <form method="POST">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                                <ul class="mb-0">
                                    <li>تأكد من تشغيل خادم MySQL</li>
                                    <li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>
                                    <li>سيتم إنشاء قاعدة بيانات جديدة باسم "warehouse_management"</li>
                                    <li>سيتم إنشاء 9 جداول رئيسية مع البيانات الأساسية</li>
                                </ul>
                            </div>
                            
                            <button type="submit" name="setup_database" class="btn btn-primary btn-lg">
                                <i class="fas fa-database me-2"></i>
                                إنشاء قاعدة البيانات
                            </button>
                        </form>
                    </div>
                <?php elseif ($database_exists): ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>النظام جاهز للاستخدام!</h5>
                        <p class="mb-3">
                            تم إعداد قاعدة البيانات بنجاح. يمكنك الآن البدء في استخدام النظام.
                        </p>
                        <a href="index.php" class="btn btn-success btn-lg">
                            <i class="fas fa-home me-2"></i>
                            الانتقال إلى الصفحة الرئيسية
                        </a>
                    </div>
                <?php endif; ?>

                <!-- معلومات إضافية -->
                <div class="mt-4">
                    <h5>معلومات النظام</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>
                                        تفاصيل النظام
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>الاسم:</strong> نظام تسيير المخزن</li>
                                        <li><strong>الإصدار:</strong> 1.0.0</li>
                                        <li><strong>التاريخ:</strong> <?php echo date('Y-m-d'); ?></li>
                                        <li><strong>اللغة:</strong> العربية</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-cogs me-2"></i>
                                        المتطلبات التقنية
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>PHP:</strong> 7.4+ مع PDO MySQL</li>
                                        <li><strong>MySQL:</strong> 5.7+ أو MariaDB 10.2+</li>
                                        <li><strong>الذاكرة:</strong> 128MB على الأقل</li>
                                        <li><strong>المساحة:</strong> 50MB على الأقل</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الميزات -->
                <div class="mt-4">
                    <h5>ميزات النظام</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-boxes fa-3x text-primary mb-2"></i>
                                <h6>إدارة المخزون</h6>
                                <p class="text-muted small">
                                    إدارة شاملة لبطاقات المخزون والسلع
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-truck fa-3x text-success mb-2"></i>
                                <h6>إدارة الممونين</h6>
                                <p class="text-muted small">
                                    قاعدة بيانات شاملة للممونين والموردين
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-file-alt fa-3x text-info mb-2"></i>
                                <h6>الوثائق الرسمية</h6>
                                <p class="text-muted small">
                                    إنشاء وطباعة الوثائق الرسمية
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-chart-bar fa-3x text-warning mb-2"></i>
                                <h6>التقارير</h6>
                                <p class="text-muted small">
                                    تقارير مفصلة عن حالة المخزون
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-bell fa-3x text-danger mb-2"></i>
                                <h6>التنبيهات</h6>
                                <p class="text-muted small">
                                    تنبيهات المخزون المنخفض
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-globe fa-3x text-secondary mb-2"></i>
                                <h6>متعدد العملات</h6>
                                <p class="text-muted small">
                                    دعم العملات المحلية والدولية
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
