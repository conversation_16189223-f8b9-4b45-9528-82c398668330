<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$type = $_GET['type'] ?? 'all';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$categories = $_GET['categories'] ?? '';
$start_page = intval($_GET['start_page'] ?? 1);
$end_page = intval($_GET['end_page'] ?? 1);

// فئات اللوازم
$all_categories = [
    'لوازم الورق',
    'مستهلكات الطبع والنسخ',
    'اللوازم المكتبية',
    'اللوازم الصيدلانية والوقائية',
    'لوازم السيارات ولواحقها',
    'الأثاث',
    'خاص بمتفوقي الامتحانات المدرسية'
];

// بناء الاستعلام حسب نوع الطباعة
$where_conditions = [];
$params = [];

switch ($type) {
    case 'single':
        if (empty($id)) {
            die('معرف المادة مطلوب');
        }
        $where_conditions[] = "id = ?";
        $params[] = $id;
        break;
        
    case 'current':
        if (!empty($search)) {
            $where_conditions[] = "(item_name LIKE ? OR item_number LIKE ? OR old_item_number LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        if (!empty($category_filter)) {
            $where_conditions[] = "category = ?";
            $params[] = $category_filter;
        }
        break;
        
    case 'custom':
        if (!empty($categories)) {
            $selected_categories = explode(',', $categories);
            $placeholders = str_repeat('?,', count($selected_categories) - 1) . '?';
            $where_conditions[] = "category IN ($placeholders)";
            $params = array_merge($params, $selected_categories);
        }
        break;
        
    case 'all':
    default:
        // لا توجد شروط إضافية
        break;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// جلب البيانات
$stmt = $db->prepare("SELECT * FROM warehouse_supplies $where_clause ORDER BY category, item_name ASC");
$stmt->execute($params);
$supplies = $stmt->fetchAll();

if (empty($supplies)) {
    die('لا توجد بيانات للطباعة');
}

// تطبيق نطاق الصفحات للطباعة المخصصة
if ($type === 'custom' && isset($_GET['start_page'])) {
    $items_per_page = 20; // عدد العناصر في الصفحة
    $start_index = ($start_page - 1) * $items_per_page;
    $end_index = $end_page * $items_per_page;
    $supplies = array_slice($supplies, $start_index, $end_index - $start_index);
}

// تحديد حجم الخط حسب عدد العناصر
$supplies_count = count($supplies);
$font_size = '14px';
$table_font_size = '12px';
$header_font_size = '18px';

if ($supplies_count > 50) {
    $font_size = '10px';
    $table_font_size = '9px';
    $header_font_size = '14px';
} elseif ($supplies_count > 20) {
    $font_size = '12px';
    $table_font_size = '11px';
    $header_font_size = '16px';
}

// ألوان الفئات
$category_colors = [
    'لوازم الورق' => '#007bff',
    'مستهلكات الطبع والنسخ' => '#28a745',
    'اللوازم المكتبية' => '#17a2b8',
    'اللوازم الصيدلانية والوقائية' => '#ffc107',
    'لوازم السيارات ولواحقها' => '#dc3545',
    'الأثاث' => '#343a40',
    'خاص بمتفوقي الامتحانات المدرسية' => '#6f42c1'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة اللوازم الموجودة في المخزن</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 15px;
            direction: rtl;
            font-size: <?php echo $font_size; ?>;
            line-height: 1.4;
        }
        .header { 
            text-align: center; 
            margin-bottom: 20px; 
            border-bottom: 2px solid #2c5530;
            padding-bottom: 15px;
        }
        .header h1 { 
            color: #2c5530; 
            margin: 3px 0; 
            font-size: <?php echo $header_font_size; ?>;
        }
        .header h2 { 
            color: #4a7c59; 
            margin: 3px 0; 
            font-size: calc(<?php echo $header_font_size; ?> - 2px);
        }
        .header h3 { 
            color: #666; 
            margin: 3px 0; 
            font-size: calc(<?php echo $header_font_size; ?> - 4px);
        }
        .report-title { 
            text-align: center; 
            font-size: calc(<?php echo $header_font_size; ?> + 4px); 
            font-weight: bold; 
            margin: 15px 0; 
            color: #2c5530;
            border: 2px solid #2c5530;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .supplies-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .supplies-table th,
        .supplies-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            font-size: <?php echo $table_font_size; ?>;
        }
        .supplies-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .category-badge {
            padding: 2px 6px;
            border-radius: 3px;
            color: white;
            font-size: calc(<?php echo $table_font_size; ?> - 1px);
            font-weight: bold;
        }
        .item-number {
            background-color: #e3f2fd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .old-number {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            color: #666;
        }
        @media print {
            body { margin: 5mm; font-size: <?php echo $table_font_size; ?>; }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            border-top: 1px solid #ccc;
            padding-top: 15px;
            font-size: calc(<?php echo $table_font_size; ?> - 1px);
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
        <h2><?php echo MINISTRY_NAME_AR; ?></h2>
        <h3><?php echo OFFICE_NAME_AR; ?></h3>
    </div>
    
    <div class="report-title">
        قائمة اللوازم الموجودة في المخزن
        <?php if ($type === 'single'): ?>
            - مادة واحدة
        <?php elseif ($type === 'current'): ?>
            - الصفحة الحالية (<?php echo $supplies_count; ?> مادة)
        <?php elseif ($type === 'custom'): ?>
            - طباعة مخصصة (<?php echo $supplies_count; ?> مادة)
        <?php else: ?>
            - جميع المواد (<?php echo $supplies_count; ?> مادة)
        <?php endif; ?>
    </div>
    
    <!-- جدول اللوازم -->
    <table class="supplies-table">
        <thead>
            <tr>
                <th width="5%">#</th>
                <th width="30%">اسم المادة</th>
                <th width="15%">رقم المادة</th>
                <th width="15%">الرقم القديم</th>
                <th width="20%">الفئة</th>
                <th width="15%">الملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <?php $counter = 1; ?>
            <?php foreach ($supplies as $supply): ?>
                <tr>
                    <td><?php echo $counter++; ?></td>
                    <td style="text-align: right; font-weight: bold;">
                        <?php echo htmlspecialchars($supply['item_name']); ?>
                    </td>
                    <td>
                        <span class="item-number">
                            <?php echo htmlspecialchars($supply['item_number']); ?>
                        </span>
                    </td>
                    <td>
                        <?php if ($supply['old_item_number']): ?>
                            <span class="old-number">
                                <?php echo htmlspecialchars($supply['old_item_number']); ?>
                            </span>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td>
                        <span class="category-badge" style="background-color: <?php echo $category_colors[$supply['category']] ?? '#6c757d'; ?>;">
                            <?php echo htmlspecialchars($supply['category']); ?>
                        </span>
                    </td>
                    <td style="text-align: right; font-size: calc(<?php echo $table_font_size; ?> - 1px);">
                        <?php 
                        if ($supply['notes']) {
                            echo htmlspecialchars(substr($supply['notes'], 0, 50));
                            if (strlen($supply['notes']) > 50) echo '...';
                        } else {
                            echo '-';
                        }
                        ?>
                    </td>
                </tr>
                
                <!-- فاصل صفحة كل 25 عنصر -->
                <?php if ($counter % 25 == 1 && $counter > 1): ?>
                    </tbody>
                    </table>
                    <div class="page-break"></div>
                    <table class="supplies-table">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="30%">اسم المادة</th>
                                <th width="15%">رقم المادة</th>
                                <th width="15%">الرقم القديم</th>
                                <th width="20%">الفئة</th>
                                <th width="15%">الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                <?php endif; ?>
            <?php endforeach; ?>
        </tbody>
    </table>
    
    <!-- إحصائيات سريعة -->
    <?php if ($type !== 'single'): ?>
        <div style="margin-top: 20px;">
            <h6 style="color: #2c5530; border-bottom: 1px solid #2c5530; padding-bottom: 5px;">
                إحصائيات سريعة:
            </h6>
            <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
                <?php
                $category_counts = [];
                foreach ($supplies as $supply) {
                    $category_counts[$supply['category']] = ($category_counts[$supply['category']] ?? 0) + 1;
                }
                ?>
                <?php foreach ($category_counts as $category => $count): ?>
                    <div style="background-color: <?php echo $category_colors[$category] ?? '#6c757d'; ?>; color: white; padding: 5px 10px; border-radius: 5px; font-size: <?php echo $table_font_size; ?>;">
                        <?php echo $category; ?>: <?php echo $count; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- تذييل -->
    <div class="footer">
        تم إنشاء هذا المستند تلقائياً من نظام إدارة المخزن - <?php echo date('Y-m-d H:i:s'); ?>
        <br>إجمالي المواد المطبوعة: <?php echo $supplies_count; ?> مادة
        <?php if ($type === 'custom' && isset($_GET['start_page'])): ?>
            <br>الصفحات: من <?php echo $start_page; ?> إلى <?php echo $end_page; ?>
        <?php endif; ?>
    </div>
    
    <!-- أزرار التحكم -->
    <div class="text-center mt-4 no-print">
        <button onclick="window.print()" class="btn btn-primary btn-lg me-2">
            <i class="fas fa-print me-2"></i>طباعة
        </button>
        <button onclick="window.close()" class="btn btn-secondary btn-lg me-2">
            <i class="fas fa-times me-2"></i>إغلاق
        </button>
        <a href="warehouse_supplies.php" class="btn btn-info btn-lg">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <script>
        // طباعة تلقائية عند التحميل (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
