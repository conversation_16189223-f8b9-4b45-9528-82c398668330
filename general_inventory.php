<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'الجرد العام';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';
$search_type = $_GET['search_type'] ?? 'designation';

// رسائل النظام
$success_message = '';
$error_message = '';

// جلب قائمة المديريات والمصالح والمكاتب
$stmt = $db->query("SELECT id, name, type FROM departments_services_offices WHERE is_active = 1 ORDER BY name ASC");
$departments = $stmt->fetchAll();

// جلب قائمة العملات
$stmt = $db->query("SELECT code, name_ar, symbol FROM currencies WHERE is_active = 1 ORDER BY name_ar ASC");
$currencies = $stmt->fetchAll();

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $inventory_number = sanitizeInput($_POST['inventory_number']);
    $registration_date = $_POST['registration_date'];
    $designation = sanitizeInput($_POST['designation']);
    $source = sanitizeInput($_POST['source']);
    $value = floatval($_POST['value']);
    $currency_type = sanitizeInput($_POST['currency_type']);
    $allocation = sanitizeInput($_POST['allocation']);
    $office_id = !empty($_POST['office_id']) ? intval($_POST['office_id']) : null;
    $exit_info = sanitizeInput($_POST['exit_info']);
    $notes = sanitizeInput($_POST['notes']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE general_inventory SET 
                    inventory_number = ?, registration_date = ?, designation = ?, 
                    source = ?, value = ?, currency_type = ?, allocation = ?, 
                    office_id = ?, exit_info = ?, notes = ?
                    WHERE id = ?");
                
                $stmt->execute([
                    $inventory_number, $registration_date, $designation,
                    $source, $value, $currency_type, $allocation,
                    $office_id, $exit_info, $notes, $_POST['id']
                ]);
                
                $success_message = 'تم تحديث عنصر الجرد بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO general_inventory 
                    (inventory_number, registration_date, designation, source, 
                     value, currency_type, allocation, office_id, exit_info, notes) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $inventory_number, $registration_date, $designation, $source,
                    $value, $currency_type, $allocation, $office_id, $exit_info, $notes
                ]);
                
                $success_message = 'تم إضافة عنصر الجرد بنجاح';
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        $stmt = $db->prepare("DELETE FROM general_inventory WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف عنصر الجرد بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT gi.*, dso.name as office_name 
                         FROM general_inventory gi 
                         LEFT JOIN departments_services_offices dso ON gi.office_id = dso.id 
                         WHERE gi.id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    if ($search_type === 'designation') {
        $where_clause = "WHERE gi.designation LIKE ? OR gi.source LIKE ?";
        $params = ["%$search%", "%$search%"];
    } elseif ($search_type === 'inventory_number') {
        $where_clause = "WHERE gi.inventory_number LIKE ?";
        $params = ["%$search%"];
    }
}

$stmt = $db->prepare("SELECT gi.*, dso.name as office_name, dso.type as office_type
                     FROM general_inventory gi 
                     LEFT JOIN departments_services_offices dso ON gi.office_id = dso.id 
                     $where_clause 
                     ORDER BY gi.registration_date DESC, gi.created_at DESC");
$stmt->execute($params);
$inventory_items = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>
                    الجرد العام
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate id="inventoryForm">
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="inventory_number" class="form-label">رقم الجرد *</label>
                            <input type="text" class="form-control" id="inventory_number" name="inventory_number" 
                                   value="<?php echo $edit_data['inventory_number'] ?? generateReferenceNumber('INV-'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال رقم الجرد</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="registration_date" class="form-label">تاريخ التسجيل *</label>
                            <input type="date" class="form-control" id="registration_date" name="registration_date" 
                                   value="<?php echo $edit_data['registration_date'] ?? date('Y-m-d'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال تاريخ التسجيل</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="designation" class="form-label">التعيين *</label>
                            <input type="text" class="form-control" id="designation" name="designation" 
                                   value="<?php echo $edit_data['designation'] ?? ''; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال التعيين</div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="source" class="form-label">المصدر</label>
                            <input type="text" class="form-control" id="source" name="source" 
                                   value="<?php echo $edit_data['source'] ?? ''; ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="value" class="form-label">القيمة *</label>
                            <div class="input-group">
                                <input type="number" step="0.01" class="form-control" id="value" name="value" 
                                       value="<?php echo $edit_data['value'] ?? '0'; ?>" required min="0">
                                <select class="form-select" id="currency_type" name="currency_type" style="max-width: 120px;">
                                    <?php foreach ($currencies as $currency): ?>
                                        <option value="<?php echo $currency['code']; ?>" 
                                                <?php echo ($edit_data['currency_type'] ?? 'DZD') === $currency['code'] ? 'selected' : ''; ?>>
                                            <?php echo $currency['symbol']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="invalid-feedback">يرجى إدخال القيمة</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="allocation" class="form-label">التخصيص</label>
                            <input type="text" class="form-control" id="allocation" name="allocation" 
                                   value="<?php echo $edit_data['allocation'] ?? ''; ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="office_id" class="form-label">المكتب/المديرية/المصلحة</label>
                            <select class="form-select" id="office_id" name="office_id">
                                <option value="">اختر المكتب أو المديرية</option>
                                <?php 
                                $current_type = '';
                                foreach ($departments as $dept): 
                                    if ($dept['type'] !== $current_type) {
                                        if ($current_type !== '') echo '</optgroup>';
                                        echo '<optgroup label="' . $dept['type'] . '">';
                                        $current_type = $dept['type'];
                                    }
                                ?>
                                    <option value="<?php echo $dept['id']; ?>" 
                                            <?php echo ($edit_data['office_id'] ?? '') == $dept['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                                <?php if ($current_type !== '') echo '</optgroup>'; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="exit_info" class="form-label">معلومات الخروج</label>
                            <input type="text" class="form-control" id="exit_info" name="exit_info" 
                                   value="<?php echo $edit_data['exit_info'] ?? ''; ?>">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">الملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $edit_data['notes'] ?? ''; ?></textarea>
                    </div>
                    
                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="general_inventory.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                                <button type="button" class="btn btn-info" onclick="generateInventoryReport()">
                                    <i class="fas fa-chart-bar me-1"></i> تقرير الجرد
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الجرد العام
                </h5>

                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <select name="search_type" class="form-select me-2" style="width: auto;">
                        <option value="designation" <?php echo $search_type === 'designation' ? 'selected' : ''; ?>>بحث بالتعيين</option>
                        <option value="inventory_number" <?php echo $search_type === 'inventory_number' ? 'selected' : ''; ?>>بحث برقم الجرد</option>
                    </select>
                    <input type="text" name="search" class="form-control me-2"
                           placeholder="ابحث هنا..."
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="general_inventory.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5><?php echo count($inventory_items); ?></h5>
                                <small>إجمالي العناصر</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5><?php echo number_format(array_sum(array_column($inventory_items, 'value')), 2); ?></h5>
                                <small>إجمالي القيمة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5><?php echo count(array_unique(array_column($inventory_items, 'office_id'))); ?></h5>
                                <small>عدد المكاتب</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5><?php echo count(array_filter($inventory_items, function($item) { return !empty($item['exit_info']); })); ?></h5>
                                <small>عناصر خارجة</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الجرد</th>
                                <th>تاريخ التسجيل</th>
                                <th>التعيين</th>
                                <th>المصدر</th>
                                <th>القيمة</th>
                                <th>المكتب/المديرية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($inventory_items)): ?>
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($inventory_items as $item): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($item['inventory_number']); ?></strong>
                                        </td>
                                        <td><?php echo formatArabicDate($item['registration_date']); ?></td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($item['designation']); ?>">
                                                <?php echo htmlspecialchars($item['designation']); ?>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($item['source']) ?: '-'; ?></td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo number_format($item['value'], 2); ?> <?php echo $item['currency_type']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($item['office_name']): ?>
                                                <small class="text-muted">
                                                    <span class="badge bg-info"><?php echo $item['office_type']; ?></span><br>
                                                    <?php echo htmlspecialchars($item['office_name']); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($item['exit_info'])): ?>
                                                <span class="badge bg-warning" title="<?php echo htmlspecialchars($item['exit_info']); ?>">
                                                    خارج
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-success">متوفر</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $item['id']; ?>"
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteItem(<?php echo $item['id']; ?>)"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button onclick="viewItemDetails(<?php echo $item['id']; ?>)"
                                                        class="btn btn-outline-info" title="تفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث رقم الجرد تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const inventoryNumberInput = document.getElementById('inventory_number');
    const registrationDateInput = document.getElementById('registration_date');

    // إذا كان رقم الجرد فارغاً، قم بإنشاء رقم جديد
    if (!inventoryNumberInput.value || inventoryNumberInput.value.startsWith('INV-')) {
        generateInventoryNumber();
    }

    // تحديث رقم الجرد عند تغيير التاريخ
    registrationDateInput.addEventListener('change', generateInventoryNumber);
});

// إنشاء رقم جرد جديد
function generateInventoryNumber() {
    const date = document.getElementById('registration_date').value;
    if (date) {
        const dateObj = new Date(date);
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const day = String(dateObj.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

        const inventoryNumber = `INV-${year}${month}${day}-${random}`;
        document.getElementById('inventory_number').value = inventoryNumber;
    }
}

// حذف عنصر الجرد
function deleteItem(id) {
    confirmDelete('هل أنت متأكد من حذف هذا العنصر من الجرد؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// عرض تفاصيل العنصر
function viewItemDetails(id) {
    fetch(`api/get_inventory_item.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const item = data.item;

                let detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>رقم الجرد:</strong> ${item.inventory_number}</p>
                            <p><strong>تاريخ التسجيل:</strong> ${item.registration_date}</p>
                            <p><strong>التعيين:</strong> ${item.designation}</p>
                            <p><strong>المصدر:</strong> ${item.source || 'غير محدد'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>القيمة:</strong> ${item.value} ${item.currency_type}</p>
                            <p><strong>التخصيص:</strong> ${item.allocation || 'غير محدد'}</p>
                            <p><strong>المكتب:</strong> ${item.office_name || 'غير محدد'}</p>
                            <p><strong>معلومات الخروج:</strong> ${item.exit_info || 'غير محدد'}</p>
                        </div>
                    </div>
                `;

                if (item.notes) {
                    detailsHtml += `<div class="mt-3"><strong>الملاحظات:</strong><br>${item.notes}</div>`;
                }

                Swal.fire({
                    title: 'تفاصيل عنصر الجرد',
                    html: detailsHtml,
                    icon: 'info',
                    confirmButtonText: 'موافق',
                    width: '600px'
                });
            } else {
                showError('خطأ في جلب تفاصيل العنصر');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showError('خطأ في الاتصال بالخادم');
        });
}

// إنشاء تقرير الجرد
function generateInventoryReport() {
    const reportOptions = [
        { value: 'summary', text: 'تقرير ملخص' },
        { value: 'detailed', text: 'تقرير مفصل' },
        { value: 'by_office', text: 'تقرير حسب المكتب' },
        { value: 'by_value', text: 'تقرير حسب القيمة' }
    ];

    let optionsHtml = '<select id="reportType" class="form-select mb-3">';
    reportOptions.forEach(option => {
        optionsHtml += `<option value="${option.value}">${option.text}</option>`;
    });
    optionsHtml += '</select>';

    Swal.fire({
        title: 'اختر نوع التقرير',
        html: optionsHtml,
        showCancelButton: true,
        confirmButtonText: 'إنشاء التقرير',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const reportType = document.getElementById('reportType').value;
            return reportType;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            window.open(`reports/inventory_report.php?type=${result.value}`, '_blank');
        }
    });
}

// تصدير البيانات
function exportInventoryData(format) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `export/inventory_export.php?format=${format}`;
    form.style.display = 'none';

    // إضافة فلاتر البحث الحالية
    const searchInput = document.querySelector('input[name="search"]');
    const searchTypeSelect = document.querySelector('select[name="search_type"]');

    if (searchInput && searchInput.value) {
        const searchField = document.createElement('input');
        searchField.type = 'hidden';
        searchField.name = 'search';
        searchField.value = searchInput.value;
        form.appendChild(searchField);
    }

    if (searchTypeSelect && searchTypeSelect.value) {
        const searchTypeField = document.createElement('input');
        searchTypeField.type = 'hidden';
        searchTypeField.name = 'search_type';
        searchTypeField.value = searchTypeSelect.value;
        form.appendChild(searchTypeField);
    }

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// إضافة أزرار التصدير
document.querySelector('.card-header h5').insertAdjacentHTML('afterend', `
    <div class="mt-2">
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-success" onclick="exportInventoryData('excel')">
                <i class="fas fa-file-excel me-1"></i> Excel
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="exportInventoryData('pdf')">
                <i class="fas fa-file-pdf me-1"></i> PDF
            </button>
            <button type="button" class="btn btn-outline-info" onclick="exportInventoryData('csv')">
                <i class="fas fa-file-csv me-1"></i> CSV
            </button>
        </div>
    </div>
`);
</script>

<?php include 'includes/footer.php'; ?>
