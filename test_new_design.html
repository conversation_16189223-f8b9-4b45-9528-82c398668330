<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم الجديد - جدول المواد والتوقيعات 2×2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            اختبار التصميم الجديد - جدول المواد والتوقيعات 2×2
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h5><i class="fas fa-star me-2"></i>الميزات الجديدة والمحسنة:</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ جدول عمودي للمواد المتعددة</li>
                                        <li>✅ إضافة وحذف المواد ديناميكياً</li>
                                        <li>✅ حقول: اسم المادة، الكمية، الوحدة، رقم اللوازم</li>
                                        <li>✅ ترقيم تلقائي للمواد</li>
                                        <li>✅ قائمة منسدلة للمديريات والمصالح</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ التوقيعات مرتبة 2×2 أفقياً (صفين × عمودين)</li>
                                        <li>✅ مساحات توقيع كبيرة ومنظمة</li>
                                        <li>✅ تصغير الخط تلقائياً للمواد الكثيرة</li>
                                        <li>✅ طباعة في صفحة واحدة مهما كان عدد المواد</li>
                                        <li>✅ تصدير محسن مع الجدول</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <form id="testForm" method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="receipt_number" class="form-label">رقم الوصل *</label>
                                    <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                                           value="NEW-DESIGN-001" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="request_date" class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="request_date" name="request_date" 
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="beneficiary_directorate" class="form-label">المديرية المستفيدة *</label>
                                    <select class="form-select" id="beneficiary_directorate" name="beneficiary_directorate" required>
                                        <option value="">اختر المديرية أو المصلحة</option>
                                        <option value="مديرية الامتحانات" selected>مديرية الامتحانات</option>
                                        <option value="مديرية المسابقات">مديرية المسابقات</option>
                                        <option value="مديرية الإدارة العامة">مديرية الإدارة العامة</option>
                                        <option value="مديرية الوسائل">مديرية الوسائل</option>
                                        <option value="مصلحة المحاسبة">مصلحة المحاسبة</option>
                                        <option value="مصلحة الموارد البشرية">مصلحة الموارد البشرية</option>
                                        <option value="مصلحة الصيانة">مصلحة الصيانة</option>
                                        <option value="مصلحة الأمن">مصلحة الأمن</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="recipient_name" class="form-label">اسم المستلم *</label>
                                    <input type="text" class="form-control" id="recipient_name" name="recipient_name" 
                                           value="أحمد محمد علي" required>
                                </div>
                            </div>
                            
                            <!-- جدول المواد الجديد -->
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-table me-2"></i>
                                        جدول المواد المطلوبة (التصميم الجديد)
                                    </h6>
                                    <button type="button" class="btn btn-sm btn-light" onclick="addMaterialRow()">
                                        <i class="fas fa-plus me-1"></i> إضافة مادة
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="materialsTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="8%">الرقم</th>
                                                    <th width="40%">اسم المادة</th>
                                                    <th width="12%">الكمية</th>
                                                    <th width="15%">الوحدة</th>
                                                    <th width="20%">رقم اللوازم</th>
                                                    <th width="5%">حذف</th>
                                                </tr>
                                            </thead>
                                            <tbody id="materialsTableBody">
                                                <tr>
                                                    <td>1</td>
                                                    <td><input type="text" class="form-control" name="materials[0][item_name]" value="أوراق A4 بيضاء" required></td>
                                                    <td><input type="number" class="form-control" name="materials[0][quantity]" value="500" min="1" required></td>
                                                    <td><input type="text" class="form-control" name="materials[0][unit]" value="ورقة"></td>
                                                    <td><input type="text" class="form-control" name="materials[0][supply_number]" value="SUP-001"></td>
                                                    <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
                                                </tr>
                                                <tr>
                                                    <td>2</td>
                                                    <td><input type="text" class="form-control" name="materials[1][item_name]" value="أقلام حبر زرقاء" required></td>
                                                    <td><input type="number" class="form-control" name="materials[1][quantity]" value="50" min="1" required></td>
                                                    <td><input type="text" class="form-control" name="materials[1][unit]" value="قلم"></td>
                                                    <td><input type="text" class="form-control" name="materials[1][supply_number]" value="SUP-002"></td>
                                                    <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
                                                </tr>
                                                <tr>
                                                    <td>3</td>
                                                    <td><input type="text" class="form-control" name="materials[2][item_name]" value="مجلدات بلاستيكية" required></td>
                                                    <td><input type="number" class="form-control" name="materials[2][quantity]" value="20" min="1" required></td>
                                                    <td><input type="text" class="form-control" name="materials[2][unit]" value="مجلد"></td>
                                                    <td><input type="text" class="form-control" name="materials[2][supply_number]" value="SUP-003"></td>
                                                    <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="alert alert-info mt-3">
                                        <small>
                                            <i class="fas fa-info-circle me-1"></i>
                                            يمكنك إضافة عدة مواد في نفس الطلب. سيتم عرضها في جدول منظم في الملف المصدر.
                                            <br><strong>ملاحظة:</strong> عند إضافة أكثر من 3 مواد، سيتم تصغير الخط تلقائياً لضمان الطباعة في صفحة واحدة.
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">الملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">طلب عاجل للامتحانات النهائية - يرجى التسليم في أقرب وقت ممكن مع مراعاة الجودة المطلوبة.</textarea>
                            </div>
                            
                            <!-- التوقيعات الجديدة 2×2 -->
                            <div class="card mb-4">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-signature me-2"></i>
                                        التوقيعات الجديدة (2×2)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <strong>الترتيب الجديد:</strong> التوقيعات ستظهر في صفين، كل صف يحتوي على توقيعين جنباً إلى جنب
                                    </div>
                                    
                                    <div class="row">
                                        <!-- الصف الأول -->
                                        <div class="col-12 mb-3">
                                            <h6 class="text-primary">الصف الأول:</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="signature_warehouse_manager" 
                                                               name="signature_warehouse_manager" checked>
                                                        <label class="form-check-label" for="signature_warehouse_manager">
                                                            <strong>إمضاء وختم المكلف بتسيير المخزن</strong>
                                                        </label>
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="signature_means_chief" 
                                                               name="signature_means_chief" checked>
                                                        <label class="form-check-label" for="signature_means_chief">
                                                            <strong>إمضاء وختم رئيس مصلحة الوسائل</strong>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- الصف الثاني -->
                                        <div class="col-12">
                                            <h6 class="text-success">الصف الثاني:</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="signature_sub_director" 
                                                               name="signature_sub_director" checked>
                                                        <label class="form-check-label" for="signature_sub_director">
                                                            <strong>إمضاء وختم المدير الفرعي للإدارة العامة</strong>
                                                        </label>
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="signature_beneficiary" 
                                                               name="signature_beneficiary" checked>
                                                        <label class="form-check-label" for="signature_beneficiary">
                                                            <strong>إمضاء المستفيد</strong>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <h5 class="mb-3">اختبار التصميم الجديد:</h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-danger btn-lg" onclick="downloadFile('pdf')">
                                        <i class="fas fa-file-pdf me-2"></i> PDF جديد
                                    </button>
                                    <button type="button" class="btn btn-primary btn-lg" onclick="downloadFile('docx')">
                                        <i class="fas fa-file-word me-2"></i> DOCX جديد
                                    </button>
                                    <button type="button" class="btn btn-info btn-lg" onclick="downloadFile('doc')">
                                        <i class="fas fa-file-word me-2"></i> DOC جديد
                                    </button>
                                </div>
                                
                                <div class="mt-3">
                                    <button type="button" class="btn btn-success btn-lg" onclick="previewNewDesign()">
                                        <i class="fas fa-eye me-2"></i> معاينة التصميم الجديد
                                    </button>
                                </div>
                                
                                <div class="mt-3">
                                    <a href="office_supplies_new.php" class="btn btn-outline-primary">
                                        <i class="fas fa-external-link-alt me-2"></i> فتح النظام الجديد
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-comparison me-2"></i>
                            مقارنة التصميمين
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">التصميم القديم:</h6>
                                <ul class="list-unstyled">
                                    <li>❌ مادة واحدة فقط</li>
                                    <li>❌ حقول منفصلة</li>
                                    <li>❌ توقيعات في صف واحد</li>
                                    <li>❌ مساحة محدودة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">التصميم الجديد:</h6>
                                <ul class="list-unstyled">
                                    <li>✅ عدة مواد في جدول</li>
                                    <li>✅ إضافة/حذف ديناميكي</li>
                                    <li>✅ توقيعات 2×2 منظمة</li>
                                    <li>✅ مساحات أكبر وأوضح</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let materialCounter = 3;

        // إضافة صف مادة جديد
        function addMaterialRow() {
            const tbody = document.getElementById('materialsTableBody');
            const currentRows = tbody.children.length;

            // تنبيه عند إضافة مواد كثيرة
            if (currentRows >= 5) {
                Swal.fire({
                    icon: 'info',
                    title: 'تنبيه',
                    text: 'عند إضافة أكثر من 5 مواد، سيتم تصغير الخط تلقائياً لضمان الطباعة في صفحة واحدة.',
                    confirmButtonText: 'فهمت'
                });
            }

            const row = document.createElement('tr');

            row.innerHTML = `
                <td>${materialCounter + 1}</td>
                <td><input type="text" class="form-control" name="materials[${materialCounter}][item_name]" placeholder="أدخل اسم المادة" required></td>
                <td><input type="number" class="form-control" name="materials[${materialCounter}][quantity]" value="1" min="1" required></td>
                <td><input type="text" class="form-control" name="materials[${materialCounter}][unit]" placeholder="الوحدة"></td>
                <td><input type="text" class="form-control" name="materials[${materialCounter}][supply_number]" placeholder="رقم اللوازم"></td>
                <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
            `;

            tbody.appendChild(row);
            materialCounter++;
            updateRowNumbers();
            updateFontSizeInfo();
        }

        // حذف صف مادة
        function removeMaterialRow(button) {
            const row = button.closest('tr');
            const tbody = document.getElementById('materialsTableBody');

            if (tbody.children.length > 1) {
                row.remove();
                updateRowNumbers();
                updateFontSizeInfo();
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'تحذير!',
                    text: 'يجب أن يحتوي الجدول على مادة واحدة على الأقل'
                });
            }
        }

        // تحديث أرقام الصفوف
        function updateRowNumbers() {
            const rows = document.querySelectorAll('#materialsTableBody tr');
            rows.forEach((row, index) => {
                row.cells[0].textContent = index + 1;
            });
        }

        // تحديث معلومات حجم الخط
        function updateFontSizeInfo() {
            const tbody = document.getElementById('materialsTableBody');
            const rowCount = tbody.children.length;
            const alertDiv = document.querySelector('.alert-info');

            let fontSizeText = '';
            if (rowCount > 5) {
                fontSizeText = '<br><span class="text-warning"><strong>تنبيه:</strong> سيتم استخدام خط صغير (14px) لضمان الطباعة في صفحة واحدة.</span>';
            } else if (rowCount > 3) {
                fontSizeText = '<br><span class="text-info"><strong>ملاحظة:</strong> سيتم استخدام خط متوسط (15px) للحصول على أفضل تنسيق.</span>';
            } else {
                fontSizeText = '<br><span class="text-success"><strong>مثالي:</strong> سيتم استخدام الخط العادي (16px) مع تنسيق مريح.</span>';
            }

            const baseText = 'يمكنك إضافة عدة مواد في نفس الطلب. سيتم عرضها في جدول منظم في الملف المصدر.';
            alertDiv.innerHTML = '<small><i class="fas fa-info-circle me-1"></i>' + baseText + fontSizeText + '</small>';
        }

        // تنزيل الملف
        function downloadFile(format) {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            Swal.fire({
                title: `جاري إنشاء ملف ${format.toUpperCase()} بالتصميم الجديد...`,
                html: 'مع جدول المواد والتوقيعات 2×2',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            const hiddenForm = document.createElement('form');
            hiddenForm.method = 'POST';
            hiddenForm.action = `export/office_supplies_new_export.php?format=${format}`;
            hiddenForm.style.display = 'none';
            
            for (let [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                hiddenForm.appendChild(input);
            }
            
            document.body.appendChild(hiddenForm);
            hiddenForm.submit();
            document.body.removeChild(hiddenForm);
            
            setTimeout(() => {
                Swal.close();
                Swal.fire({
                    icon: 'success',
                    title: 'تم إنشاء الملف!',
                    html: `
                        <div class="text-start">
                            <p>✅ ملف ${format.toUpperCase()} بالتصميم الجديد</p>
                            <p>✅ جدول المواد المتعددة</p>
                            <p>✅ التوقيعات 2×2</p>
                            <p>✅ تنسيق محسن</p>
                        </div>
                    `,
                    timer: 3000,
                    timerProgressBar: true
                });
            }, 2000);
        }

        // معاينة التصميم الجديد
        function previewNewDesign() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            // جمع بيانات المواد
            const materials = [];
            const rows = document.querySelectorAll('#materialsTableBody tr');
            rows.forEach(row => {
                const itemName = row.querySelector('input[name*="[item_name]"]').value;
                const quantity = row.querySelector('input[name*="[quantity]"]').value;
                const unit = row.querySelector('input[name*="[unit]"]').value;
                const supplyNumber = row.querySelector('input[name*="[supply_number]"]').value;
                
                if (itemName) {
                    materials.push({
                        item_name: itemName,
                        quantity: quantity,
                        unit: unit,
                        supply_number: supplyNumber
                    });
                }
            });
            
            // بناء جدول المواد
            let materialsHtml = '<table class="table table-bordered"><thead class="table-light"><tr><th>الرقم</th><th>اسم المادة</th><th>الكمية</th><th>الوحدة</th><th>رقم اللوازم</th></tr></thead><tbody>';
            
            materials.forEach((material, index) => {
                materialsHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${material.item_name}</td>
                        <td>${material.quantity}</td>
                        <td>${material.unit || '-'}</td>
                        <td>${material.supply_number || '-'}</td>
                    </tr>
                `;
            });
            
            materialsHtml += '</tbody></table>';
            
            // بناء التوقيعات 2×2
            let signaturesHtml = '<div style="margin-top: 50px;"><h6>التوقيعات (2×2):</h6><table style="width: 100%; border-collapse: collapse;"><tr>';
            
            if (formData.get('signature_warehouse_manager')) {
                signaturesHtml += '<td style="text-align: center; padding: 30px; border: 3px solid #000; width: 50%;"><div style="height: 80px; background-color: #fafafa; border: 1px dashed #ccc; margin-bottom: 15px;"></div><strong>إمضاء وختم المكلف بتسيير المخزن</strong></td>';
            }
            
            if (formData.get('signature_means_chief')) {
                signaturesHtml += '<td style="text-align: center; padding: 30px; border: 3px solid #000; width: 50%;"><div style="height: 80px; background-color: #fafafa; border: 1px dashed #ccc; margin-bottom: 15px;"></div><strong>إمضاء وختم رئيس مصلحة الوسائل</strong></td>';
            }
            
            signaturesHtml += '</tr><tr>';
            
            if (formData.get('signature_sub_director')) {
                signaturesHtml += '<td style="text-align: center; padding: 30px; border: 3px solid #000; width: 50%;"><div style="height: 80px; background-color: #fafafa; border: 1px dashed #ccc; margin-bottom: 15px;"></div><strong>إمضاء وختم المدير الفرعي للإدارة العامة</strong></td>';
            }
            
            if (formData.get('signature_beneficiary')) {
                signaturesHtml += '<td style="text-align: center; padding: 30px; border: 3px solid #000; width: 50%;"><div style="height: 80px; background-color: #fafafa; border: 1px dashed #ccc; margin-bottom: 15px;"></div><strong>إمضاء المستفيد</strong></td>';
            }
            
            signaturesHtml += '</tr></table></div>';
            
            const previewContent = `
                <div class="text-center mb-4">
                    <h3>الجمهورية الجزائرية الديمقراطية الشعبية</h3>
                    <h4>وزارة التربية الوطنية</h4>
                    <h5>الديوان الوطني للامتحانات والمسابقات</h5>
                    <hr>
                    <h4 style="color: #2c5530; border: 2px solid #2c5530; padding: 15px; background-color: #f8f9fa;">طلب واستلام اللوازم المكتبية</h4>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6"><strong>رقم الوصل:</strong> ${formData.get('receipt_number')}</div>
                    <div class="col-6"><strong>التاريخ:</strong> ${formData.get('request_date')}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12"><strong>المديرية المستفيدة:</strong> ${formData.get('beneficiary_directorate')}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12"><strong>اسم ولقب المستلم:</strong> ${formData.get('recipient_name')}</div>
                </div>
                
                <h6 style="color: #2c5530; margin-top: 30px;">جدول المواد المطلوبة:</h6>
                ${materialsHtml}
                
                ${formData.get('notes') ? `<div class="mb-3"><strong>الملاحظات:</strong><br>${formData.get('notes')}</div>` : ''}
                
                ${signaturesHtml}
            `;
            
            Swal.fire({
                title: 'معاينة التصميم الجديد',
                html: previewContent,
                width: '1000px',
                showConfirmButton: true,
                confirmButtonText: 'ممتاز!',
                customClass: {
                    htmlContainer: 'text-start'
                }
            });
        }

        // تحديث معلومات الخط عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateFontSizeInfo();
        });
    </script>
</body>
</html>
