<?php
require_once 'config/config.php';
require_once 'config/database.php';

// التحقق من وجود مكتبة TCPDF
if (!class_exists('TCPDF')) {
    // تحميل TCPDF من CDN أو استخدام مكتبة محلية
    require_once 'vendor/tecnickcom/tcpdf/tcpdf.php';
}

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$font_size = $_GET['font_size'] ?? '14BOLD';

if (empty($id)) {
    die('معرف الطلب مطلوب');
}

// جلب بيانات الطلب
$stmt = $db->prepare("SELECT * FROM office_supplies_requests WHERE id = ?");
$stmt->execute([$id]);
$request = $stmt->fetch();

if (!$request) {
    die('الطلب غير موجود');
}

// فك تشفير المواد
$materials = json_decode($request['materials'], true) ?? [];

// استخراج الحجم الرقمي
$numeric_size = (int) str_replace('BOLD', '', $font_size);

// إنشاء PDF جديد
$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// إعداد معلومات المستند
$pdf->SetCreator('نظام تسيير المخزن');
$pdf->SetAuthor('الديوان الوطني للامتحانات والمسابقات');
$pdf->SetTitle('طلب واستلام اللوازم المكتبية - ' . $request['receipt_number']);
$pdf->SetSubject('طلب اللوازم المكتبية');

// إعداد الخط العربي
$pdf->SetFont('aealarabiya', 'B', $numeric_size);

// إضافة صفحة
$pdf->AddPage();

// إعداد اتجاه النص من اليمين لليسار
$pdf->setRTL(true);

// رأس المؤسسة
$html_header = '
<div style="text-align: center; margin-bottom: 20px;">
    <h1 style="font-size: ' . ($numeric_size + 4) . 'px; font-weight: bold; color: #000; margin: 5px 0;">الجمهورية الجزائرية الديمقراطية الشعبية</h1>
    <h2 style="font-size: ' . $numeric_size . 'px; font-weight: bold; color: #000; margin: 5px 0;">وزارة التربية الوطنية</h2>
    <h3 style="font-size: ' . ($numeric_size - 2) . 'px; font-weight: bold; color: #000; margin: 5px 0;">الديوان الوطني للامتحانات والمسابقات</h3>
    <hr style="border: 1px solid #000; margin: 15px 0;">
</div>';

$pdf->writeHTML($html_header, true, false, true, false, '');

// عنوان النموذج
$html_title = '
<div style="text-align: center; margin: 15px 0;">
    <h2 style="font-size: ' . ($numeric_size + 6) . 'px; font-weight: bold; color: #2c5530; border: 2px solid #2c5530; padding: 10px; background-color: #f8f9fa;">
        طلب واستلام اللوازم المكتبية
    </h2>
</div>';

$pdf->writeHTML($html_title, true, false, true, false, '');

// معلومات الطلب
$html_info = '
<table style="width: 100%; border: 1px solid #ddd; margin: 10px 0;">
    <tr>
        <td style="width: 25%; font-weight: bold; padding: 8px; border: 1px solid #ddd;">رقم الوصل:</td>
        <td style="width: 25%; padding: 8px; border: 1px solid #ddd;">' . htmlspecialchars($request['receipt_number']) . '</td>
        <td style="width: 25%; font-weight: bold; padding: 8px; border: 1px solid #ddd;">التاريخ:</td>
        <td style="width: 25%; padding: 8px; border: 1px solid #ddd;">' . date('Y/m/d', strtotime($request['request_date'])) . '</td>
    </tr>
    <tr>
        <td style="font-weight: bold; padding: 8px; border: 1px solid #ddd;">المديرية أو المصلحة:</td>
        <td style="padding: 8px; border: 1px solid #ddd;">' . htmlspecialchars($request['beneficiary_directorate']) . '</td>
        <td style="font-weight: bold; padding: 8px; border: 1px solid #ddd;">اسم المستلم:</td>
        <td style="padding: 8px; border: 1px solid #ddd;">' . htmlspecialchars($request['recipient_name']) . '</td>
    </tr>
</table>';

$pdf->writeHTML($html_info, true, false, true, false, '');

// جدول المواد
$html_table = '
<table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
    <thead>
        <tr style="background-color: #f8f9fa;">
            <th style="width: 6%; border: 1px solid #000; padding: 8px; text-align: center; font-weight: bold;">الرقم</th>
            <th style="width: 40%; border: 1px solid #000; padding: 8px; text-align: center; font-weight: bold;">اسم المادة</th>
            <th style="width: 10%; border: 1px solid #000; padding: 8px; text-align: center; font-weight: bold;">الكمية</th>
            <th style="width: 12%; border: 1px solid #000; padding: 8px; text-align: center; font-weight: bold;">رقم اللوازم</th>
            <th style="width: 32%; border: 1px solid #000; padding: 8px; text-align: center; font-weight: bold;">الملاحظات</th>
        </tr>
    </thead>
    <tbody>';

if (empty($materials)) {
    $html_table .= '
        <tr>
            <td colspan="5" style="border: 1px solid #000; padding: 8px; text-align: center;">لا توجد مواد مسجلة</td>
        </tr>';
} else {
    foreach ($materials as $index => $material) {
        $html_table .= '
        <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">' . ($index + 1) . '</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: right; font-weight: bold;">' . htmlspecialchars($material['item_name']) . '</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; font-weight: bold;">' . number_format($material['quantity'], 0) . '</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">' . htmlspecialchars($material['supply_number'] ?: '-') . '</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: right; font-weight: bold;">' . htmlspecialchars($material['notes'] ?? '-') . '</td>
        </tr>';
    }
}

$html_table .= '
    </tbody>
</table>';

$pdf->writeHTML($html_table, true, false, true, false, '');

// الملاحظات العامة
if ($request['notes']) {
    $html_notes = '
    <div style="margin: 15px 0; border: 1px solid #ddd; padding: 10px;">
        <strong>الملاحظات العامة:</strong><br>
        ' . nl2br(htmlspecialchars($request['notes'])) . '
    </div>';
    $pdf->writeHTML($html_notes, true, false, true, false, '');
}

// التوقيعات
$signatures = [
    'إمضاء وختم رئيس مصلحة الوسائل',
    'إمضاء وختم المدير الفرعي للإدارة العامة',
    'إمضاء وختم المكلف بتسيير المخزن',
    'إمضاء وختم المستفيد'
];

$html_signatures = '
<div style="margin-top: 30px;">
    <h3 style="text-align: center; font-weight: bold; margin-bottom: 20px;">التوقيعات والأختام</h3>
    
    <table style="width: 100%; margin: 20px 0;">
        <tr>
            <td style="width: 50%; padding: 40px 10px; text-align: right; font-weight: bold; vertical-align: bottom;">
                ' . $signatures[0] . '
                <br><br><br>
                ________________________
            </td>
            <td style="width: 50%; padding: 40px 10px; text-align: left; font-weight: bold; vertical-align: bottom;">
                ' . $signatures[1] . '
                <br><br><br>
                ________________________
            </td>
        </tr>
    </table>
    
    <div style="height: 30px; border-bottom: 1px dotted #ccc; margin: 20px 0;"></div>
    
    <table style="width: 100%; margin: 20px 0;">
        <tr>
            <td style="width: 50%; padding: 40px 10px; text-align: right; font-weight: bold; vertical-align: bottom;">
                ' . $signatures[2] . '
                <br><br><br>
                ________________________
            </td>
            <td style="width: 50%; padding: 40px 10px; text-align: left; font-weight: bold; vertical-align: bottom;">
                ' . $signatures[3] . '
                <br><br><br>
                ________________________
            </td>
        </tr>
    </table>
</div>';

$pdf->writeHTML($html_signatures, true, false, true, false, '');

// إخراج PDF
$filename = 'طلب_اللوازم_المكتبية_' . $request['receipt_number'] . '.pdf';
$pdf->Output($filename, 'D'); // D للتحميل المباشر
?>
