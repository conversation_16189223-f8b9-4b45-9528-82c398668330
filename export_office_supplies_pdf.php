<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$font_size = $_GET['font_size'] ?? '14BOLD';

if (empty($id)) {
    die('معرف الطلب مطلوب');
}

// جلب بيانات الطلب
$stmt = $db->prepare("SELECT * FROM office_supplies_requests WHERE id = ?");
$stmt->execute([$id]);
$request = $stmt->fetch();

if (!$request) {
    die('الطلب غير موجود');
}

// فك تشفير المواد
$materials = json_decode($request['materials'], true) ?? [];

// استخراج الحجم الرقمي
$numeric_size = (int) str_replace('BOLD', '', $font_size);

// إعداد headers للـ PDF - استخدام HTML للطباعة
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب واستلام اللوازم المكتبية - <?php echo htmlspecialchars($request['receipt_number']); ?></title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        @media print {
            body { -webkit-print-color-adjust: exact; }
            .no-print { display: none !important; }
        }
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            font-size: <?php echo $numeric_size; ?>px;
            line-height: 1.4;
            color: #000;
            font-weight: bold;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 3px solid #000000 !important;
            padding-bottom: 15px;
            background-color: white !important;
        }
        .header h1, .header h2, .header h3 {
            color: #000000 !important;
            font-weight: 900 !important; /* أقصى درجة سُمك */
            font-family: 'Arial Black', Arial, sans-serif !important;
            margin: 8px 0;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        .header h1 {
            font-size: <?php echo ($numeric_size + 6); ?>px;
            letter-spacing: 1px;
        }
        .header h2 {
            font-size: <?php echo ($numeric_size + 2); ?>px;
            letter-spacing: 0.5px;
        }
        .header h3 {
            font-size: <?php echo $numeric_size; ?>px;
            letter-spacing: 0.5px;
        }

        .form-title {
            text-align: center;
            font-size: <?php echo ($numeric_size + 6); ?>px;
            font-weight: bold;
            margin: 15px 0;
            color: #2c5530;
            border: 2px solid #2c5530;
            padding: 10px;
            background-color: #f8f9fa;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .info-table td {
            border: 1px solid #000;
            padding: 8px;
            font-weight: bold;
        }
        .info-label {
            background-color: #f8f9fa;
            width: 25%;
        }

        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .materials-table th,
        .materials-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            font-weight: bold;
        }
        .materials-table th {
            background-color: #f8f9fa;
        }
        .materials-table td.item-name,
        .materials-table td.notes {
            text-align: right;
        }

        .signatures-section {
            margin-top: 15px; /* تقليل المسافة بين الجدول والتوقيعات */
            page-break-inside: avoid;
        }
        .signatures-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 25px;
            font-size: <?php echo ($numeric_size + 2); ?>px;
        }
        .signature-row {
            display: flex;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 100px; /* زيادة المسافة بين الصفوف بشكل كبير */
            padding: 40px 0; /* زيادة الحشو */
        }
        .signature-box {
            width: 30%; /* تقليل العرض لزيادة المسافة */
            padding: 80px 20px; /* زيادة المسافة للإمضاءات بشكل كبير */
            font-weight: bold;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            min-height: 150px; /* زيادة الارتفاع */
        }
        .signature-box.right-align {
            text-align: right;
        }
        .signature-box.left-align {
            text-align: left;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            margin-top: 30px;
            height: 1px;
        }
        .separator {
            height: 40px;
            border-bottom: 1px dotted #ccc;
            margin: 30px 0;
        }
        .print-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 20px;
            font-weight: bold;
        }
        .print-button:hover {
            background: #0056b3;
        }
    </style>
    <script>
        function printDocument() {
            window.print();
        }

        function downloadPDF() {
            // إخفاء الأزرار
            document.querySelectorAll('.no-print').forEach(function(el) {
                el.style.display = 'none';
            });

            // طباعة (المستخدم يمكنه اختيار "حفظ كـ PDF")
            setTimeout(function() {
                window.print();
            }, 500);
        }

        window.onload = function() {
            // إذا كان هناك معامل auto_print في URL أو تم الوصول مباشرة للملف
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('auto_print') === '1' || window.location.href.indexOf('export_office_supplies_pdf.php') > -1) {
                setTimeout(function() {
                    downloadPDF();
                }, 1500);
            }
        };
    </script>
</head>
<body>
    <div class="no-print" style="text-align: center; margin-bottom: 20px;">
        <button onclick="printDocument()" class="print-button">
            🖨️ طباعة
        </button>
        <button onclick="downloadPDF()" class="print-button">
            📄 حفظ كـ PDF
        </button>
        <button onclick="window.close()" class="print-button" style="background: #6c757d;">
            ❌ إغلاق
        </button>
    </div>

    <div class="header">
        <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
        <h2>وزارة التربية الوطنية</h2>
        <h3>الديوان الوطني للامتحانات والمسابقات</h3>
    </div>

    <div class="form-title">
        طلب واستلام اللوازم المكتبية
    </div>

    <table class="info-table">
        <tr>
            <td class="info-label">رقم الوصل:</td>
            <td><?php echo htmlspecialchars($request['receipt_number']); ?></td>
            <td class="info-label">التاريخ:</td>
            <td><?php echo date('Y/m/d', strtotime($request['request_date'])); ?></td>
        </tr>
        <tr>
            <td class="info-label">المديرية أو المصلحة:</td>
            <td><?php echo htmlspecialchars($request['beneficiary_directorate']); ?></td>
            <td class="info-label">اسم المستلم:</td>
            <td><?php echo htmlspecialchars($request['recipient_name']); ?></td>
        </tr>
    </table>

    <table class="materials-table">
        <thead>
            <tr>
                <th width="6%">الرقم</th>
                <th width="40%">اسم المادة</th>
                <th width="10%">الكمية</th>
                <th width="12%">رقم اللوازم</th>
                <th width="32%">الملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($materials)): ?>
                <tr>
                    <td colspan="5">لا توجد مواد مسجلة</td>
                </tr>
            <?php else: ?>
                <?php foreach ($materials as $index => $material): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td class="item-name"><?php echo htmlspecialchars($material['item_name']); ?></td>
                        <td><?php echo number_format($material['quantity'], 0); ?></td>
                        <td><?php echo htmlspecialchars($material['supply_number'] ?: '-'); ?></td>
                        <td class="notes"><?php echo htmlspecialchars($material['notes'] ?? '-'); ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>

    <?php if ($request['notes']): ?>
        <table class="info-table">
            <tr>
                <td class="info-label" width="20%">الملاحظات العامة:</td>
                <td><?php echo nl2br(htmlspecialchars($request['notes'])); ?></td>
            </tr>
        </table>
    <?php endif; ?>

    <div class="signatures-section">
        <div class="signatures-title">التوقيعات والأختام</div>

        <div class="signature-row">
            <div class="signature-box right-align">
                إمضاء وختم رئيس مصلحة الوسائل
                <div class="signature-line"></div>
            </div>
            <!-- مسافة كبيرة بين التوقيعات -->
            <div style="width: 40%; min-width: 300px;"></div>
            <div class="signature-box left-align">
                إمضاء وختم المدير الفرعي للإدارة العامة
                <div class="signature-line"></div>
            </div>
        </div>

        <div class="separator" style="height: 80px; margin: 60px 0;"></div>

        <div class="signature-row">
            <div class="signature-box right-align">
                إمضاء وختم المكلف بتسيير المخزن
                <div class="signature-line"></div>
            </div>
            <!-- مسافة كبيرة بين التوقيعات -->
            <div style="width: 40%; min-width: 300px;"></div>
            <div class="signature-box left-align">
                إمضاء وختم المستفيد
                <div class="signature-line"></div>
            </div>
        </div>
    </div>
</body>
</html>


