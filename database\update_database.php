<?php
// ملف تحديث قاعدة البيانات لدعم المواد المتعددة
require_once '../config/config.php';
require_once '../config/database.php';

$database = new Database();
$db = $database->getConnection();

$success_messages = [];
$error_messages = [];

try {
    // التحقق من وجود عمود materials
    $stmt = $db->query("SHOW COLUMNS FROM office_supplies_requests LIKE 'materials'");
    $materials_column_exists = $stmt->rowCount() > 0;
    
    if (!$materials_column_exists) {
        // إضافة عمود materials
        $db->exec("ALTER TABLE office_supplies_requests ADD COLUMN materials JSON AFTER request_date");
        $success_messages[] = "تم إضافة عمود materials بنجاح";
        
        // التحقق من وجود عمود unit
        $stmt = $db->query("SHOW COLUMNS FROM office_supplies_requests LIKE 'unit'");
        $unit_column_exists = $stmt->rowCount() > 0;
        
        if (!$unit_column_exists) {
            $db->exec("ALTER TABLE office_supplies_requests ADD COLUMN unit VARCHAR(50) AFTER quantity");
            $success_messages[] = "تم إضافة عمود unit بنجاح";
        }
        
        // نقل البيانات الموجودة
        $stmt = $db->query("SELECT id, item_name, quantity, supply_number FROM office_supplies_requests WHERE materials IS NULL");
        $old_records = $stmt->fetchAll();
        
        foreach ($old_records as $record) {
            $materials = [[
                'item_name' => $record['item_name'],
                'quantity' => $record['quantity'],
                'unit' => '',
                'supply_number' => $record['supply_number'] ?? ''
            ]];
            
            $update_stmt = $db->prepare("UPDATE office_supplies_requests SET materials = ? WHERE id = ?");
            $update_stmt->execute([json_encode($materials, JSON_UNESCAPED_UNICODE), $record['id']]);
        }
        
        $success_messages[] = "تم نقل " . count($old_records) . " سجل من التنسيق القديم إلى الجديد";
    } else {
        $success_messages[] = "عمود materials موجود بالفعل";
    }
    
    // إنشاء جدول المديريات والمصالح
    $create_directorates_table = "
    CREATE TABLE IF NOT EXISTS directorates_services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        directorate_name VARCHAR(255) NOT NULL UNIQUE,
        directorate_type ENUM('مديرية', 'مصلحة') DEFAULT 'مديرية',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_directorate_name (directorate_name),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($create_directorates_table);
    $success_messages[] = "تم إنشاء جدول المديريات والمصالح";
    
    // إدراج البيانات الافتراضية
    $directorates = [
        ['مديرية الامتحانات', 'مديرية'],
        ['مديرية المسابقات', 'مديرية'],
        ['مديرية الإدارة العامة', 'مديرية'],
        ['مديرية الوسائل', 'مديرية'],
        ['مديرية التكنولوجيا', 'مديرية'],
        ['مصلحة المحاسبة', 'مصلحة'],
        ['مصلحة الموارد البشرية', 'مصلحة'],
        ['مصلحة الصيانة', 'مصلحة'],
        ['مصلحة الأمن', 'مصلحة'],
        ['مصلحة الاتصالات', 'مصلحة'],
        ['مصلحة الأرشيف', 'مصلحة'],
        ['مصلحة النقل', 'مصلحة']
    ];
    
    $insert_stmt = $db->prepare("INSERT IGNORE INTO directorates_services (directorate_name, directorate_type) VALUES (?, ?)");
    $inserted_count = 0;
    
    foreach ($directorates as $directorate) {
        $insert_stmt->execute($directorate);
        if ($insert_stmt->rowCount() > 0) {
            $inserted_count++;
        }
    }
    
    $success_messages[] = "تم إدراج $inserted_count مديرية/مصلحة جديدة";
    
} catch (PDOException $e) {
    $error_messages[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            تحديث قاعدة البيانات
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($success_messages)): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>تم التحديث بنجاح:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($success_messages as $message): ?>
                                        <li><?php echo $message; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($error_messages)): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($error_messages as $message): ?>
                                        <li><?php echo $message; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>ما تم تحديثه:</h6>
                            <ul class="mb-0">
                                <li>✅ إضافة عمود <code>materials</code> لدعم المواد المتعددة</li>
                                <li>✅ إضافة عمود <code>unit</code> للوحدات</li>
                                <li>✅ نقل البيانات القديمة إلى التنسيق الجديد</li>
                                <li>✅ إنشاء جدول المديريات والمصالح</li>
                                <li>✅ إدراج البيانات الافتراضية للمديريات</li>
                            </ul>
                        </div>
                        
                        <div class="text-center">
                            <a href="../office_supplies_new.php" class="btn btn-success btn-lg">
                                <i class="fas fa-arrow-right me-2"></i>
                                انتقل إلى النظام الجديد
                            </a>
                            <a href="../test_final_improvements.html" class="btn btn-info btn-lg">
                                <i class="fas fa-test-tube me-2"></i>
                                اختبار النظام
                            </a>
                        </div>
                        
                        <?php
                        // عرض عينة من البيانات المحدثة
                        try {
                            $stmt = $db->query("SELECT id, receipt_number, materials FROM office_supplies_requests ORDER BY created_at DESC LIMIT 3");
                            $sample_data = $stmt->fetchAll();
                            
                            if (!empty($sample_data)): ?>
                                <div class="mt-4">
                                    <h6>عينة من البيانات المحدثة:</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>رقم الوصل</th>
                                                    <th>المواد (JSON)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($sample_data as $row): ?>
                                                    <tr>
                                                        <td><?php echo $row['id']; ?></td>
                                                        <td><?php echo htmlspecialchars($row['receipt_number']); ?></td>
                                                        <td>
                                                            <small>
                                                                <?php 
                                                                $materials = json_decode($row['materials'], true);
                                                                if ($materials) {
                                                                    echo count($materials) . ' مادة';
                                                                } else {
                                                                    echo 'لا توجد مواد';
                                                                }
                                                                ?>
                                                            </small>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif;
                        } catch (Exception $e) {
                            // تجاهل الأخطاء في عرض البيانات
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
