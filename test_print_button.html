<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الطباعة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background-color: #e8f4fd;
            border: 2px solid #007bff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
            min-height: 100px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🧪 اختبار زر الطباعة</h2>
        
        <div class="test-section">
            <h4>اختبار الزر المطابق للأصل:</h4>
            
            <!-- نسخة مطابقة من الزر الأصلي -->
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-success dropdown-toggle"
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-print me-1"></i> طباعة
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="testPrinterDialog()">
                        <i class="fas fa-printer me-2 text-primary"></i> اختيار الطابعة
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="testDownloadFile('pdf')">
                        <i class="fas fa-file-pdf me-2 text-danger"></i> حفظ كـ PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="testDownloadFile('docx')">
                        <i class="fas fa-file-word me-2 text-primary"></i> حفظ كـ DOCX
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="testDownloadFile('doc')">
                        <i class="fas fa-file-word me-2 text-info"></i> حفظ كـ DOC
                    </a></li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h4>📊 نتائج الاختبار:</h4>
            <div id="console-output" class="console-output">
                انقر على الخيارات أعلاه لرؤية النتائج هنا...
            </div>
            <button class="btn btn-secondary btn-sm" onclick="clearConsole()">
                <i class="fas fa-trash"></i> مسح النتائج
            </button>
        </div>

        <div class="test-section">
            <h4>🔍 تشخيص المشكلة:</h4>
            <button class="btn btn-info" onclick="checkJavaScript()">
                <i class="fas fa-bug"></i> فحص JavaScript
            </button>
            <button class="btn btn-warning" onclick="checkBootstrap()">
                <i class="fas fa-check"></i> فحص Bootstrap
            </button>
            <button class="btn btn-primary" onclick="checkForm()">
                <i class="fas fa-wpforms"></i> فحص النموذج
            </button>
        </div>

        <div class="alert alert-info">
            <h5>📋 خطوات التشخيص:</h5>
            <ol>
                <li>اختبر الزر أعلاه - إذا عمل فالمشكلة في الصفحة الأصلية</li>
                <li>افحص وحدة التحكم في المتصفح (F12) للأخطاء</li>
                <li>تأكد من تحميل Bootstrap و jQuery بشكل صحيح</li>
                <li>تحقق من وجود النموذج بالـ ID الصحيح</li>
            </ol>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function log(message) {
            const output = document.getElementById('console-output');
            const time = new Date().toLocaleTimeString();
            output.innerHTML += `[${time}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearConsole() {
            document.getElementById('console-output').innerHTML = 'تم مسح النتائج...\n';
        }

        // دالة اختبار نافذة الطابعة
        function testPrinterDialog() {
            log('✅ تم استدعاء testPrinterDialog() بنجاح');
            
            try {
                // محاكاة فحص النموذج
                log('🔍 فحص وجود النموذج...');
                
                // إنشاء نافذة اختبار
                const printWindow = window.open('', '_blank', 'width=600,height=400');
                
                if (printWindow) {
                    log('✅ تم فتح النافذة بنجاح');
                    
                    printWindow.document.write(`
                        <!DOCTYPE html>
                        <html lang="ar" dir="rtl">
                        <head>
                            <meta charset="UTF-8">
                            <title>اختبار نافذة الطباعة</title>
                            <style>
                                body { 
                                    font-family: Arial, sans-serif; 
                                    text-align: center; 
                                    padding: 50px;
                                    background: linear-gradient(135deg, #28a745, #20c997);
                                    color: white;
                                }
                                .success-box {
                                    background: white;
                                    color: #333;
                                    padding: 30px;
                                    border-radius: 10px;
                                    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                                }
                                .btn {
                                    padding: 10px 20px;
                                    margin: 10px;
                                    border: none;
                                    border-radius: 5px;
                                    cursor: pointer;
                                    font-weight: bold;
                                }
                                .btn-primary { background: #007bff; color: white; }
                                .btn-secondary { background: #6c757d; color: white; }
                            </style>
                        </head>
                        <body>
                            <div class="success-box">
                                <h2>🎉 نجح الاختبار!</h2>
                                <p>نافذة الطباعة تعمل بشكل صحيح</p>
                                <button class="btn btn-primary" onclick="alert('طباعة مباشرة')">طباعة مباشرة</button>
                                <button class="btn btn-primary" onclick="alert('طباعة مع خيارات')">طباعة مع خيارات</button>
                                <button class="btn btn-secondary" onclick="window.close()">إغلاق</button>
                            </div>
                        </body>
                        </html>
                    `);
                    printWindow.document.close();
                } else {
                    log('❌ فشل في فتح النافذة - قد يكون محجوب من المتصفح');
                }
                
            } catch (error) {
                log('❌ خطأ في testPrinterDialog: ' + error.message);
            }
        }

        // دالة اختبار التصدير
        function testDownloadFile(format) {
            log(`✅ تم استدعاء testDownloadFile('${format}') بنجاح`);
            log(`📄 محاولة تصدير ملف بصيغة ${format.toUpperCase()}`);
            
            // محاكاة عملية التصدير
            setTimeout(() => {
                log(`✅ تم إنشاء ملف ${format.toUpperCase()} بنجاح (محاكاة)`);
            }, 1000);
        }

        // فحص JavaScript
        function checkJavaScript() {
            log('🔍 فحص JavaScript...');
            log('✅ jQuery متاح: ' + (typeof $ !== 'undefined' ? 'نعم' : 'لا'));
            log('✅ Bootstrap متاح: ' + (typeof bootstrap !== 'undefined' ? 'نعم' : 'لا'));
            log('✅ وحدة التحكم تعمل: نعم');
        }

        // فحص Bootstrap
        function checkBootstrap() {
            log('🔍 فحص Bootstrap...');
            const dropdown = document.querySelector('.dropdown-toggle');
            if (dropdown) {
                log('✅ عنصر dropdown موجود');
                log('✅ Bootstrap classes مطبقة');
            } else {
                log('❌ عنصر dropdown غير موجود');
            }
        }

        // فحص النموذج
        function checkForm() {
            log('🔍 فحص النموذج...');
            const form = document.getElementById('officeSuppliesForm');
            if (form) {
                log('✅ النموذج موجود بالـ ID الصحيح');
            } else {
                log('❌ النموذج غير موجود - هذا قد يكون سبب المشكلة!');
                log('💡 تأكد من وجود <form id="officeSuppliesForm"> في الصفحة الأصلية');
            }
        }

        // تسجيل تحميل الصفحة
        window.onload = function() {
            log('🚀 تم تحميل صفحة الاختبار بنجاح');
            log('📝 جاهز للاختبار...');
        };
    </script>
</body>
</html>
