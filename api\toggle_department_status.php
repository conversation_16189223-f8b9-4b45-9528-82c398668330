<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $id = $input['id'] ?? '';
    $is_active = $input['is_active'] ?? false;
    
    if (empty($id)) {
        echo json_encode(['success' => false, 'message' => 'معرف الوحدة مطلوب'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("UPDATE departments_services_offices SET is_active = ? WHERE id = ?");
    $stmt->execute([$is_active, $id]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => 'تم تغيير حالة الوحدة بنجاح'], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode(['success' => false, 'message' => 'لم يتم العثور على الوحدة'], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم'], JSON_UNESCAPED_UNICODE);
}
?>
