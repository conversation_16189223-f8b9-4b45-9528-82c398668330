<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $id = $_GET['id'] ?? '';
    
    if (empty($id)) {
        echo json_encode(['success' => false, 'message' => 'معرف العنصر مطلوب'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("SELECT gi.*, dso.name as office_name, dso.type as office_type
                         FROM general_inventory gi 
                         LEFT JOIN departments_services_offices dso ON gi.office_id = dso.id 
                         WHERE gi.id = ?");
    $stmt->execute([$id]);
    $item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($item) {
        // تنسيق التاريخ
        if ($item['registration_date']) {
            $item['registration_date'] = date('d/m/Y', strtotime($item['registration_date']));
        }
        
        echo json_encode([
            'success' => true,
            'item' => $item
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'العنصر غير موجود'
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم'
    ], JSON_UNESCAPED_UNICODE);
}
?>
