<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التنزيل النهائي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-download me-2"></i>
                            اختبار نظام التنزيل - طلب واستلام اللوازم المكتبية
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>تعليمات:</strong> املأ النموذج أدناه واختبر تنزيل الملفات بصيغ مختلفة
                        </div>
                        
                        <form id="testForm" method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="receipt_number" class="form-label">رقم الوصل *</label>
                                    <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                                           value="TEST-<?php echo date('YmdHis'); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="request_date" class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="request_date" name="request_date" 
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="beneficiary_directorate" class="form-label">المديرية الفرعية أو المصلحة المستفيدة *</label>
                                <select class="form-select" id="beneficiary_directorate" name="beneficiary_directorate" required>
                                    <option value="">اختر المديرية</option>
                                    <option value="مديرية الامتحانات" selected>مديرية الامتحانات</option>
                                    <option value="مديرية المسابقات">مديرية المسابقات</option>
                                    <option value="مديرية الإدارة العامة">مديرية الإدارة العامة</option>
                                    <option value="مديرية الوسائل">مديرية الوسائل</option>
                                </select>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="recipient_name" class="form-label">اسم ولقب المستلم *</label>
                                    <input type="text" class="form-control" id="recipient_name" name="recipient_name" 
                                           value="أحمد محمد علي" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="item_name" class="form-label">اسم المادة *</label>
                                    <select class="form-select" id="item_name" name="item_name" required>
                                        <option value="">اختر المادة</option>
                                        <option value="أوراق A4" selected>أوراق A4</option>
                                        <option value="أقلام حبر">أقلام حبر</option>
                                        <option value="مجلدات">مجلدات</option>
                                        <option value="دبابيس">دبابيس</option>
                                        <option value="مشابك ورق">مشابك ورق</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="quantity" class="form-label">الكمية *</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           value="100" min="1" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="supply_number" class="form-label">رقم اللوازم</label>
                                    <input type="text" class="form-control" id="supply_number" name="supply_number" 
                                           value="SUP-2025-001">
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="notes" class="form-label">الملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="أدخل أي ملاحظات إضافية...">طلب عاجل للامتحانات النهائية</textarea>
                            </div>
                            
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">التوقيعات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="signature_warehouse_manager" 
                                                       name="signature_warehouse_manager" checked>
                                                <label class="form-check-label" for="signature_warehouse_manager">
                                                    إمضاء وختم المكلف بتسيير المخزن
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="signature_means_chief" 
                                                       name="signature_means_chief" checked>
                                                <label class="form-check-label" for="signature_means_chief">
                                                    إمضاء وختم رئيس مصلحة الوسائل
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="signature_sub_director" 
                                                       name="signature_sub_director">
                                                <label class="form-check-label" for="signature_sub_director">
                                                    إمضاء وختم المدير الفرعي للإدارة العامة
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row mt-3">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="signature_beneficiary" 
                                                       name="signature_beneficiary" checked>
                                                <label class="form-check-label" for="signature_beneficiary">
                                                    إمضاء المستفيد
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <h5 class="mb-3">اختبار التنزيل:</h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-danger btn-lg" onclick="downloadFile('pdf')">
                                        <i class="fas fa-file-pdf me-2"></i> تنزيل PDF
                                    </button>
                                    <button type="button" class="btn btn-primary btn-lg" onclick="downloadFile('docx')">
                                        <i class="fas fa-file-word me-2"></i> تنزيل DOCX
                                    </button>
                                    <button type="button" class="btn btn-info btn-lg" onclick="downloadFile('doc')">
                                        <i class="fas fa-file-word me-2"></i> تنزيل DOC
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            أدوات التشخيص
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>اختبارات مباشرة:</h6>
                                <div class="d-grid gap-2">
                                    <a href="debug_export.php" class="btn btn-outline-warning" target="_blank">
                                        <i class="fas fa-bug me-2"></i> تشخيص النظام
                                    </a>
                                    <a href="test_download.php?format=pdf" class="btn btn-outline-danger" target="_blank">
                                        <i class="fas fa-file-pdf me-2"></i> اختبار PDF مباشر
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>ملفات النظام:</h6>
                                <div class="d-grid gap-2">
                                    <a href="office_supplies.php" class="btn btn-outline-success">
                                        <i class="fas fa-file-alt me-2"></i> النظام الأصلي
                                    </a>
                                    <button class="btn btn-outline-info" onclick="checkSystemStatus()">
                                        <i class="fas fa-check-circle me-2"></i> فحص حالة النظام
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function downloadFile(format) {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            // التحقق من البيانات الأساسية
            if (!formData.get('receipt_number') || !formData.get('item_name')) {
                Swal.fire({
                    icon: 'warning',
                    title: 'تحذير!',
                    text: 'يرجى ملء البيانات الأساسية قبل التنزيل',
                    confirmButtonText: 'موافق'
                });
                return;
            }
            
            // عرض مؤشر التحميل
            Swal.fire({
                title: `جاري إنشاء ملف ${format.toUpperCase()}...`,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // إنشاء نموذج مخفي للإرسال
            const hiddenForm = document.createElement('form');
            hiddenForm.method = 'POST';
            hiddenForm.action = `export/office_supplies_export_simple.php?format=${format}`;
            hiddenForm.style.display = 'none';
            
            // إضافة البيانات
            for (let [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                hiddenForm.appendChild(input);
            }
            
            document.body.appendChild(hiddenForm);
            hiddenForm.submit();
            document.body.removeChild(hiddenForm);
            
            // إخفاء مؤشر التحميل بعد ثانيتين
            setTimeout(() => {
                Swal.close();
                Swal.fire({
                    icon: 'success',
                    title: 'تم!',
                    text: `تم إنشاء ملف ${format.toUpperCase()} بنجاح`,
                    timer: 2000,
                    timerProgressBar: true
                });
            }, 2000);
        }
        
        function checkSystemStatus() {
            Swal.fire({
                title: 'فحص حالة النظام',
                html: `
                    <div class="text-start">
                        <p>✅ ملف التصدير المبسط: موجود</p>
                        <p>✅ دالة sanitizeInput: محلولة</p>
                        <p>✅ التوقيعات الأفقية: مطبقة</p>
                        <p>✅ دعم 3 أنواع ملفات: PDF, DOCX, DOC</p>
                        <p>✅ معالجة الأخطاء: محسنة</p>
                        <p>✅ واجهة المستخدم: محدثة</p>
                    </div>
                `,
                icon: 'success',
                confirmButtonText: 'ممتاز!'
            });
        }
        
        // تحديث رقم الوصل تلقائياً
        document.addEventListener('DOMContentLoaded', function() {
            const receiptNumber = document.getElementById('receipt_number');
            if (receiptNumber.value.includes('<?php')) {
                receiptNumber.value = 'TEST-' + new Date().toISOString().slice(0,19).replace(/[-:]/g,'').replace('T','');
            }
        });
    </script>
</body>
</html>
