-- إضافة أعمدة خيارات التنسيق لجدول طلبات اللوازم المكتبية
-- تشغيل هذا السكريبت لإضافة الأعمدة الجديدة

-- التحقق من وجود الجدول أولاً
SELECT 'جدول office_supplies_requests موجود' AS status;

-- إضافة الأعمدة الجديدة مع التحقق من عدم وجودها مسبقاً

-- إضافة عمود النص العريض
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_NAME = 'office_supplies_requests'
     AND COLUMN_NAME = 'bold_text'
     AND TABLE_SCHEMA = DATABASE()) = 0,
    'ALTER TABLE office_supplies_requests ADD COLUMN bold_text TINYINT(1) DEFAULT 0 COMMENT ''نص عريض (0=عادي، 1=عريض)''',
    'SELECT ''العمود bold_text موجود بالفعل'' AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة عمود محاذاة النص
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_NAME = 'office_supplies_requests'
     AND COLUMN_NAME = 'text_align'
     AND TABLE_SCHEMA = DATABASE()) = 0,
    'ALTER TABLE office_supplies_requests ADD COLUMN text_align VARCHAR(10) DEFAULT ''right'' COMMENT ''محاذاة النص (right, center, left)''',
    'SELECT ''العمود text_align موجود بالفعل'' AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة عمود لون الخط
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_NAME = 'office_supplies_requests'
     AND COLUMN_NAME = 'font_color'
     AND TABLE_SCHEMA = DATABASE()) = 0,
    'ALTER TABLE office_supplies_requests ADD COLUMN font_color VARCHAR(20) DEFAULT ''black'' COMMENT ''لون الخط (black, blue, red, green, brown, purple)''',
    'SELECT ''العمود font_color موجود بالفعل'' AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- التحقق من إضافة الأعمدة
SELECT 'تم التحقق من الأعمدة الجديدة:' AS status;
DESCRIBE office_supplies_requests;

-- عرض البيانات الموجودة للتأكد
SELECT 'عرض البيانات مع الأعمدة الجديدة:' AS status;
SELECT id, receipt_number,
       COALESCE(bold_text, 0) as bold_text,
       COALESCE(text_align, 'right') as text_align,
       COALESCE(font_color, 'black') as font_color
FROM office_supplies_requests
LIMIT 5;
