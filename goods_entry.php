<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'وصل دخول السلع إلى المخزن';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// جلب قائمة الممونين
$stmt = $db->query("SELECT id, full_name FROM suppliers ORDER BY full_name ASC");
$suppliers = $stmt->fetchAll();

// جلب قائمة العملات
$stmt = $db->query("SELECT code, name_ar, symbol FROM currencies WHERE is_active = 1 ORDER BY name_ar ASC");
$currencies = $stmt->fetchAll();

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $receipt_number = sanitizeInput($_POST['receipt_number']);
    $directorate_name = sanitizeInput($_POST['directorate_name']);
    $supplier_id = !empty($_POST['supplier_id']) ? intval($_POST['supplier_id']) : null;
    $receipt_date = $_POST['receipt_date'];
    $item_name = sanitizeInput($_POST['item_name']);
    $item_number = sanitizeInput($_POST['item_number']);
    $inventory_number = sanitizeInput($_POST['inventory_number']);
    $use_item_number = isset($_POST['use_item_number']);
    $unit_price = floatval($_POST['unit_price']);
    $currency_type = sanitizeInput($_POST['currency_type']);
    $total_amount = floatval($_POST['total_amount']);
    $amount_without_tax = floatval($_POST['amount_without_tax']);
    $vat_rate = sanitizeInput($_POST['vat_rate']);
    $vat_amount = floatval($_POST['vat_amount']);
    $total_with_tax = floatval($_POST['total_with_tax']);
    
    // التوقيعات
    $signature_warehouse_manager = isset($_POST['signature_warehouse_manager']);
    $signature_means_chief = isset($_POST['signature_means_chief']);
    $signature_sub_director = isset($_POST['signature_sub_director']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE goods_entry_receipts SET 
                    receipt_number = ?, directorate_name = ?, supplier_id = ?, receipt_date = ?,
                    item_name = ?, item_number = ?, inventory_number = ?, use_item_number = ?,
                    unit_price = ?, currency_type = ?, total_amount = ?, amount_without_tax = ?,
                    vat_rate = ?, vat_amount = ?, total_with_tax = ?,
                    signature_warehouse_manager = ?, signature_means_chief = ?, signature_sub_director = ?
                    WHERE id = ?");
                
                $stmt->execute([
                    $receipt_number, $directorate_name, $supplier_id, $receipt_date,
                    $item_name, $item_number, $inventory_number, $use_item_number,
                    $unit_price, $currency_type, $total_amount, $amount_without_tax,
                    $vat_rate, $vat_amount, $total_with_tax,
                    $signature_warehouse_manager, $signature_means_chief, $signature_sub_director,
                    $_POST['id']
                ]);
                
                $success_message = 'تم تحديث وصل دخول السلع بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO goods_entry_receipts 
                    (receipt_number, directorate_name, supplier_id, receipt_date, item_name, 
                     item_number, inventory_number, use_item_number, unit_price, currency_type, 
                     total_amount, amount_without_tax, vat_rate, vat_amount, total_with_tax,
                     signature_warehouse_manager, signature_means_chief, signature_sub_director) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $receipt_number, $directorate_name, $supplier_id, $receipt_date, $item_name,
                    $item_number, $inventory_number, $use_item_number, $unit_price, $currency_type,
                    $total_amount, $amount_without_tax, $vat_rate, $vat_amount, $total_with_tax,
                    $signature_warehouse_manager, $signature_means_chief, $signature_sub_director
                ]);
                
                $success_message = 'تم إضافة وصل دخول السلع بنجاح';
                
                // تحديث بطاقة المخزون
                updateInventoryCard($db, $item_name, $total_amount, 'entry');
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// دالة تحديث بطاقة المخزون
function updateInventoryCard($db, $item_name, $quantity, $type) {
    try {
        // البحث عن البطاقة الموجودة
        $stmt = $db->prepare("SELECT id, current_stock FROM inventory_cards WHERE item_name = ?");
        $stmt->execute([$item_name]);
        $card = $stmt->fetch();
        
        if ($card) {
            // تحديث الكمية
            $new_stock = $type === 'entry' ? 
                $card['current_stock'] + $quantity : 
                $card['current_stock'] - $quantity;
            
            $stmt = $db->prepare("UPDATE inventory_cards SET current_stock = ? WHERE id = ?");
            $stmt->execute([$new_stock, $card['id']]);
        } else {
            // إنشاء بطاقة جديدة
            $card_number = generateReferenceNumber('INV-');
            $stmt = $db->prepare("INSERT INTO inventory_cards 
                (card_number, item_name, unit_of_measure, current_stock, entry_quantity, entry_date) 
                VALUES (?, ?, 'قطعة', ?, ?, ?)");
            $stmt->execute([$card_number, $item_name, $quantity, $quantity, date('Y-m-d')]);
        }
    } catch (PDOException $e) {
        // تسجيل الخطأ
        error_log("خطأ في تحديث بطاقة المخزون: " . $e->getMessage());
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        $stmt = $db->prepare("DELETE FROM goods_entry_receipts WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف وصل دخول السلع بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT ger.*, s.full_name as supplier_name 
                         FROM goods_entry_receipts ger 
                         LEFT JOIN suppliers s ON ger.supplier_id = s.id 
                         WHERE ger.id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE ger.receipt_number LIKE ? OR ger.item_name LIKE ? OR s.full_name LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT ger.*, s.full_name as supplier_name 
                     FROM goods_entry_receipts ger 
                     LEFT JOIN suppliers s ON ger.supplier_id = s.id 
                     $where_clause 
                     ORDER BY ger.receipt_date DESC, ger.created_at DESC");
$stmt->execute($params);
$receipts = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-arrow-down me-2"></i>
                    وصل دخول السلع إلى المخزن
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- رأس المؤسسة للطباعة -->
                <div class="institution-header d-none d-print-block">
                    <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
                    <h2><?php echo MINISTRY_NAME_AR; ?></h2>
                    <h3><?php echo OFFICE_NAME_AR; ?></h3>
                </div>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate id="goodsEntryForm">
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="receipt_number" class="form-label">رقم الوصل *</label>
                            <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                                   value="<?php echo $edit_data['receipt_number'] ?? generateReferenceNumber('GER-'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال رقم الوصل</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="directorate_name" class="form-label">اسم المديرية *</label>
                            <input type="text" class="form-control" id="directorate_name" name="directorate_name" 
                                   value="<?php echo $edit_data['directorate_name'] ?? 'المديرية الفرعية للادارة العامة'; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المديرية</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="supplier_id" class="form-label">اسم الممون</label>
                            <select class="form-select" id="supplier_id" name="supplier_id">
                                <option value="">اختر الممون</option>
                                <?php foreach ($suppliers as $supplier): ?>
                                    <option value="<?php echo $supplier['id']; ?>" 
                                            <?php echo ($edit_data['supplier_id'] ?? '') == $supplier['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($supplier['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="receipt_date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="receipt_date" name="receipt_date" 
                                   value="<?php echo $edit_data['receipt_date'] ?? date('Y-m-d'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال التاريخ</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="item_name" class="form-label">اسم المادة *</label>
                            <input type="text" class="form-control" id="item_name" name="item_name" 
                                   value="<?php echo $edit_data['item_name'] ?? ''; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المادة</div>
                        </div>
                    </div>
                    
                    <!-- خيارات رقم المادة أو رقم الجرد -->
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="use_item_number" name="use_item_number" 
                                       <?php echo ($edit_data['use_item_number'] ?? true) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="use_item_number">
                                    استخدام رقم المادة (بدلاً من رقم الجرد)
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="item_number" class="form-label">رقم المادة</label>
                            <input type="text" class="form-control" id="item_number" name="item_number" 
                                   value="<?php echo $edit_data['item_number'] ?? ''; ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="inventory_number" class="form-label">رقم الجرد</label>
                            <input type="text" class="form-control" id="inventory_number" name="inventory_number" 
                                   value="<?php echo $edit_data['inventory_number'] ?? ''; ?>">
                        </div>
                    </div>
                    
                    <!-- المعلومات المالية -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">المعلومات المالية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="unit_price" class="form-label">سعر الوحدة *</label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" class="form-control" id="unit_price" name="unit_price" 
                                               value="<?php echo $edit_data['unit_price'] ?? '0'; ?>" required>
                                        <select class="form-select" id="currency_type" name="currency_type" style="max-width: 120px;">
                                            <?php foreach ($currencies as $currency): ?>
                                                <option value="<?php echo $currency['code']; ?>" 
                                                        <?php echo ($edit_data['currency_type'] ?? 'DZD') === $currency['code'] ? 'selected' : ''; ?>>
                                                    <?php echo $currency['symbol']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="invalid-feedback">يرجى إدخال سعر الوحدة</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="total_amount" class="form-label">المبلغ الإجمالي</label>
                                    <input type="number" step="0.01" class="form-control" id="total_amount" name="total_amount" 
                                           value="<?php echo $edit_data['total_amount'] ?? '0'; ?>" readonly>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount_without_tax" class="form-label">المبلغ بدون رسوم</label>
                                    <input type="number" step="0.01" class="form-control" id="amount_without_tax" name="amount_without_tax" 
                                           value="<?php echo $edit_data['amount_without_tax'] ?? '0'; ?>" readonly>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="vat_rate" class="form-label">معدل الرسم على القيمة المضافة</label>
                                    <select class="form-select" id="vat_rate" name="vat_rate">
                                        <option value="15" <?php echo ($edit_data['vat_rate'] ?? '19') === '15' ? 'selected' : ''; ?>>15%</option>
                                        <option value="19" <?php echo ($edit_data['vat_rate'] ?? '19') === '19' ? 'selected' : ''; ?>>19%</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="vat_amount" class="form-label">مبلغ الرسم على القيمة المضافة</label>
                                    <input type="number" step="0.01" class="form-control" id="vat_amount" name="vat_amount" 
                                           value="<?php echo $edit_data['vat_amount'] ?? '0'; ?>" readonly>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="total_with_tax" class="form-label">المبلغ بإحتساب كل الرسوم (دج)</label>
                                    <input type="number" step="0.01" class="form-control" id="total_with_tax" name="total_with_tax" 
                                           value="<?php echo $edit_data['total_with_tax'] ?? '0'; ?>" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- التوقيعات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">التوقيعات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_warehouse_manager" 
                                               name="signature_warehouse_manager" 
                                               <?php echo ($edit_data['signature_warehouse_manager'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_warehouse_manager">
                                            إمضاء وختم المكلف بتسيير المخزن
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_means_chief" 
                                               name="signature_means_chief" 
                                               <?php echo ($edit_data['signature_means_chief'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_means_chief">
                                            إمضاء وختم رئيس مصلحة الوسائل
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_sub_director" 
                                               name="signature_sub_director" 
                                               <?php echo ($edit_data['signature_sub_director'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_sub_director">
                                            إمضاء وختم المدير الفرعي للادارة العامة
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="goods_entry.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-info" onclick="previewPrint()">
                                    <i class="fas fa-eye me-1"></i> معاينة قبل الطباعة
                                </button>
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                            </div>
                            
                            <!-- أزرار الإرسال -->
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary dropdown-toggle" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-share me-1"></i> إرسال
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('gmail')">
                                        <i class="fab fa-google me-2"></i> Gmail
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('outlook')">
                                        <i class="fab fa-microsoft me-2"></i> Outlook
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('yahoo')">
                                        <i class="fab fa-yahoo me-2"></i> Yahoo
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendWhatsApp()">
                                        <i class="fab fa-whatsapp me-2"></i> WhatsApp
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة وصولات دخول السلع
                </h5>

                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2"
                           placeholder="بحث برقم الوصل أو اسم المادة..."
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="goods_entry.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الوصل</th>
                                <th>التاريخ</th>
                                <th>اسم المادة</th>
                                <th>الممون</th>
                                <th>المبلغ الإجمالي</th>
                                <th>العملة</th>
                                <th>التوقيعات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($receipts)): ?>
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($receipts as $receipt): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($receipt['receipt_number']); ?></strong>
                                        </td>
                                        <td><?php echo formatArabicDate($receipt['receipt_date']); ?></td>
                                        <td><?php echo htmlspecialchars($receipt['item_name']); ?></td>
                                        <td><?php echo htmlspecialchars($receipt['supplier_name'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo number_format($receipt['total_with_tax'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $receipt['currency_type']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <?php if ($receipt['signature_warehouse_manager']): ?>
                                                    <span class="badge bg-primary" title="المكلف بتسيير المخزن">م</span>
                                                <?php endif; ?>
                                                <?php if ($receipt['signature_means_chief']): ?>
                                                    <span class="badge bg-warning" title="رئيس مصلحة الوسائل">ر</span>
                                                <?php endif; ?>
                                                <?php if ($receipt['signature_sub_director']): ?>
                                                    <span class="badge bg-success" title="المدير الفرعي">د</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $receipt['id']; ?>"
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteReceipt(<?php echo $receipt['id']; ?>)"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button onclick="printReceipt(<?php echo $receipt['id']; ?>)"
                                                        class="btn btn-outline-info" title="طباعة">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حساب المبالغ تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const unitPrice = document.getElementById('unit_price');
    const totalAmount = document.getElementById('total_amount');
    const amountWithoutTax = document.getElementById('amount_without_tax');
    const vatRate = document.getElementById('vat_rate');
    const vatAmount = document.getElementById('vat_amount');
    const totalWithTax = document.getElementById('total_with_tax');

    function calculateAmounts() {
        const price = parseFloat(unitPrice.value) || 0;
        const rate = parseFloat(vatRate.value) || 19;

        // المبلغ الإجمالي (نفترض كمية = 1 للبساطة)
        const total = price;
        totalAmount.value = total.toFixed(2);

        // المبلغ بدون رسوم
        const withoutTax = total / (1 + (rate / 100));
        amountWithoutTax.value = withoutTax.toFixed(2);

        // مبلغ الرسم
        const vat = total - withoutTax;
        vatAmount.value = vat.toFixed(2);

        // المبلغ النهائي
        totalWithTax.value = total.toFixed(2);
    }

    unitPrice.addEventListener('input', calculateAmounts);
    vatRate.addEventListener('change', calculateAmounts);

    // تفعيل/إلغاء تفعيل حقول رقم المادة/الجرد
    const useItemNumber = document.getElementById('use_item_number');
    const itemNumber = document.getElementById('item_number');
    const inventoryNumber = document.getElementById('inventory_number');

    function toggleNumberFields() {
        if (useItemNumber.checked) {
            itemNumber.disabled = false;
            inventoryNumber.disabled = true;
            inventoryNumber.value = '';
        } else {
            itemNumber.disabled = true;
            itemNumber.value = '';
            inventoryNumber.disabled = false;
        }
    }

    useItemNumber.addEventListener('change', toggleNumberFields);
    toggleNumberFields(); // تطبيق الحالة الأولية
});

// معاينة قبل الطباعة
function previewPrint() {
    const form = document.getElementById('goodsEntryForm');
    const formData = new FormData(form);

    // إنشاء نافذة معاينة
    const previewWindow = window.open('', '_blank', 'width=800,height=600');

    let content = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة وصل دخول السلع</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
            <link href="assets/css/print.css" rel="stylesheet">
            <style>
                body { font-family: 'Arial', sans-serif; }
                .signature-box {
                    border: 2px solid black;
                    height: 80px;
                    margin: 10px 0;
                    text-align: center;
                    line-height: 80px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="institution-header text-center mb-4">
                    <h3>${'<?php echo INSTITUTION_NAME_AR; ?>'}</h3>
                    <h4>${'<?php echo MINISTRY_NAME_AR; ?>'}</h4>
                    <h5>${'<?php echo OFFICE_NAME_AR; ?>'}</h5>
                    <hr>
                </div>

                <h4 class="text-center mb-4">وصل دخول السلع إلى المخزن</h4>

                <div class="row mb-3">
                    <div class="col-6">
                        <strong>رقم الوصل:</strong> ${formData.get('receipt_number')}
                    </div>
                    <div class="col-6">
                        <strong>التاريخ:</strong> ${formData.get('receipt_date')}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <strong>المديرية:</strong> ${formData.get('directorate_name')}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <strong>اسم المادة:</strong> ${formData.get('item_name')}
                    </div>
                </div>

                <table class="table table-bordered">
                    <tr>
                        <td><strong>سعر الوحدة</strong></td>
                        <td>${formData.get('unit_price')} ${formData.get('currency_type')}</td>
                    </tr>
                    <tr>
                        <td><strong>المبلغ بدون رسوم</strong></td>
                        <td>${formData.get('amount_without_tax')} ${formData.get('currency_type')}</td>
                    </tr>
                    <tr>
                        <td><strong>الرسم على القيمة المضافة (${formData.get('vat_rate')}%)</strong></td>
                        <td>${formData.get('vat_amount')} ${formData.get('currency_type')}</td>
                    </tr>
                    <tr class="table-success">
                        <td><strong>المبلغ الإجمالي</strong></td>
                        <td><strong>${formData.get('total_with_tax')} دج</strong></td>
                    </tr>
                </table>

                <div class="row mt-5">
                    ${formData.get('signature_warehouse_manager') ? '<div class="col-4"><div class="signature-box">المكلف بتسيير المخزن</div></div>' : ''}
                    ${formData.get('signature_means_chief') ? '<div class="col-4"><div class="signature-box">رئيس مصلحة الوسائل</div></div>' : ''}
                    ${formData.get('signature_sub_director') ? '<div class="col-4"><div class="signature-box">المدير الفرعي للادارة العامة</div></div>' : ''}
                </div>

                <div class="text-center mt-4">
                    <button onclick="window.print()" class="btn btn-primary">طباعة</button>
                    <button onclick="window.close()" class="btn btn-secondary">إغلاق</button>
                </div>
            </div>
        </body>
        </html>
    `;

    previewWindow.document.write(content);
    previewWindow.document.close();
}

// حذف الوصل
function deleteReceipt(id) {
    confirmDelete('هل أنت متأكد من حذف هذا الوصل؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// طباعة وصل محدد
function printReceipt(id) {
    window.open(`print_receipt.php?id=${id}&type=goods_entry`, '_blank');
}
</script>

<?php include 'includes/footer.php'; ?>
