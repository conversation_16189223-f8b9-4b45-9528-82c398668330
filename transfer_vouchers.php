<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'سند التحويل';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// قائمة الفروع
$branches = [
    'فرع الجزائر', 'فرع وهران', 'فرع باتنة', 'فرع بجاية', 
    'فرع البليدة', 'فرع عنابة', 'فرع أم البواقي', 'فرع سعيدة', 'فرع غرداية'
];

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $receipt_number = sanitizeInput($_POST['receipt_number']);
    $branch_name = sanitizeInput($_POST['branch_name']);
    $transfer_date = $_POST['transfer_date'];
    $item_name = sanitizeInput($_POST['item_name']);
    $quantity = floatval($_POST['quantity']);
    $supply_number = sanitizeInput($_POST['supply_number']);
    $notes = sanitizeInput($_POST['notes']);
    
    // التوقيعات
    $signature_means_chief = isset($_POST['signature_means_chief']);
    $signature_sub_director = isset($_POST['signature_sub_director']);
    $signature_warehouse_manager = isset($_POST['signature_warehouse_manager']);
    $signature_branch_director = isset($_POST['signature_branch_director']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE transfer_vouchers SET 
                    receipt_number = ?, branch_name = ?, transfer_date = ?, 
                    item_name = ?, quantity = ?, supply_number = ?, notes = ?,
                    signature_means_chief = ?, signature_sub_director = ?, 
                    signature_warehouse_manager = ?, signature_branch_director = ?
                    WHERE id = ?");
                
                $stmt->execute([
                    $receipt_number, $branch_name, $transfer_date,
                    $item_name, $quantity, $supply_number, $notes,
                    $signature_means_chief, $signature_sub_director,
                    $signature_warehouse_manager, $signature_branch_director, $_POST['id']
                ]);
                
                $success_message = 'تم تحديث سند التحويل بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO transfer_vouchers 
                    (receipt_number, branch_name, transfer_date, item_name, quantity, 
                     supply_number, notes, signature_means_chief, signature_sub_director, 
                     signature_warehouse_manager, signature_branch_director) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $receipt_number, $branch_name, $transfer_date, $item_name, $quantity,
                    $supply_number, $notes, $signature_means_chief, $signature_sub_director,
                    $signature_warehouse_manager, $signature_branch_director
                ]);
                
                $success_message = 'تم إضافة سند التحويل بنجاح';
                
                // تحديث بطاقة المخزون (خروج)
                updateInventoryCard($db, $item_name, $quantity, 'exit');
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// دالة تحديث بطاقة المخزون
function updateInventoryCard($db, $item_name, $quantity, $type) {
    try {
        $stmt = $db->prepare("SELECT id, current_stock FROM inventory_cards WHERE item_name = ?");
        $stmt->execute([$item_name]);
        $card = $stmt->fetch();
        
        if ($card) {
            $new_stock = $type === 'entry' ? 
                $card['current_stock'] + $quantity : 
                max(0, $card['current_stock'] - $quantity);
            
            $stmt = $db->prepare("UPDATE inventory_cards SET 
                current_stock = ?, 
                exit_quantity = exit_quantity + ?, 
                exit_date = ? 
                WHERE id = ?");
            $stmt->execute([$new_stock, $quantity, date('Y-m-d'), $card['id']]);
        }
    } catch (PDOException $e) {
        error_log("خطأ في تحديث بطاقة المخزون: " . $e->getMessage());
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        $stmt = $db->prepare("DELETE FROM transfer_vouchers WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف سند التحويل بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT * FROM transfer_vouchers WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE receipt_number LIKE ? OR item_name LIKE ? OR branch_name LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT * FROM transfer_vouchers $where_clause ORDER BY transfer_date DESC, created_at DESC");
$stmt->execute($params);
$vouchers = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    سند التحويل
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- رأس المؤسسة للطباعة -->
                <div class="institution-header d-none d-print-block">
                    <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
                    <h2><?php echo MINISTRY_NAME_AR; ?></h2>
                    <h3><?php echo OFFICE_NAME_AR; ?></h3>
                </div>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate id="transferVoucherForm">
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="receipt_number" class="form-label">رقم الوصل *</label>
                            <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                                   value="<?php echo $edit_data['receipt_number'] ?? generateReferenceNumber('TV-'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال رقم الوصل</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="transfer_date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="transfer_date" name="transfer_date" 
                                   value="<?php echo $edit_data['transfer_date'] ?? date('Y-m-d'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال التاريخ</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="branch_name" class="form-label">اسم الفرع *</label>
                            <select class="form-select" id="branch_name" name="branch_name" required>
                                <option value="">اختر الفرع</option>
                                <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch; ?>" 
                                            <?php echo ($edit_data['branch_name'] ?? '') === $branch ? 'selected' : ''; ?>>
                                        <?php echo $branch; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار الفرع</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="item_name" class="form-label">اسم المادة *</label>
                            <input type="text" class="form-control" id="item_name" name="item_name" 
                                   value="<?php echo $edit_data['item_name'] ?? ''; ?>" required
                                   list="items_list">
                            <datalist id="items_list">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </datalist>
                            <div class="invalid-feedback">يرجى إدخال اسم المادة</div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="quantity" class="form-label">الكمية *</label>
                            <input type="number" step="0.01" class="form-control" id="quantity" name="quantity" 
                                   value="<?php echo $edit_data['quantity'] ?? '1'; ?>" required min="0.01">
                            <div class="invalid-feedback">يرجى إدخال الكمية</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="supply_number" class="form-label">رقم اللوازم</label>
                            <input type="text" class="form-control" id="supply_number" name="supply_number" 
                                   value="<?php echo $edit_data['supply_number'] ?? ''; ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="notes" class="form-label">الملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"><?php echo $edit_data['notes'] ?? ''; ?></textarea>
                        </div>
                    </div>
                    
                    <!-- التوقيعات -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">التوقيعات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 col-lg-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_means_chief" 
                                               name="signature_means_chief" 
                                               <?php echo ($edit_data['signature_means_chief'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_means_chief">
                                            رئيس مصلحة الوسائل
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_sub_director" 
                                               name="signature_sub_director" 
                                               <?php echo ($edit_data['signature_sub_director'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_sub_director">
                                            المدير الفرعي للادارة العامة
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_warehouse_manager" 
                                               name="signature_warehouse_manager" 
                                               <?php echo ($edit_data['signature_warehouse_manager'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_warehouse_manager">
                                            المكلف بتسيير المخزن
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="signature_branch_director" 
                                               name="signature_branch_director" 
                                               <?php echo ($edit_data['signature_branch_director'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="signature_branch_director">
                                            مدير الفرع
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="transfer_vouchers.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                                <button type="button" class="btn btn-info" onclick="downloadPDF()">
                                    <i class="fas fa-download me-1"></i> تنزيل
                                </button>
                            </div>
                            
                            <!-- أزرار الإرسال -->
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary dropdown-toggle" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-share me-1"></i> إرسال
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('gmail')">
                                        <i class="fab fa-google me-2"></i> Gmail
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('outlook')">
                                        <i class="fab fa-microsoft me-2"></i> Outlook
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('yahoo')">
                                        <i class="fab fa-yahoo me-2"></i> Yahoo
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendWhatsApp()">
                                        <i class="fab fa-whatsapp me-2"></i> WhatsApp
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة سندات التحويل
                </h5>

                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2"
                           placeholder="بحث برقم الوصل أو اسم المادة أو الفرع..."
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="transfer_vouchers.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الوصل</th>
                                <th>التاريخ</th>
                                <th>الفرع المستلم</th>
                                <th>اسم المادة</th>
                                <th>الكمية</th>
                                <th>التوقيعات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($vouchers)): ?>
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($vouchers as $voucher): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($voucher['receipt_number']); ?></strong>
                                        </td>
                                        <td><?php echo formatArabicDate($voucher['transfer_date']); ?></td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo htmlspecialchars($voucher['branch_name']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($voucher['item_name']); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo number_format($voucher['quantity'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <?php if ($voucher['signature_means_chief']): ?>
                                                    <span class="badge bg-primary" title="رئيس مصلحة الوسائل">ر</span>
                                                <?php endif; ?>
                                                <?php if ($voucher['signature_sub_director']): ?>
                                                    <span class="badge bg-success" title="المدير الفرعي">د</span>
                                                <?php endif; ?>
                                                <?php if ($voucher['signature_warehouse_manager']): ?>
                                                    <span class="badge bg-warning" title="المكلف بتسيير المخزن">م</span>
                                                <?php endif; ?>
                                                <?php if ($voucher['signature_branch_director']): ?>
                                                    <span class="badge bg-danger" title="مدير الفرع">ف</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $voucher['id']; ?>"
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteVoucher(<?php echo $voucher['id']; ?>)"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button onclick="printVoucher(<?php echo $voucher['id']; ?>)"
                                                        class="btn btn-outline-info" title="طباعة">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل قائمة المواد من بطاقات المخزون
document.addEventListener('DOMContentLoaded', function() {
    loadItemsList();

    // مراقبة تغيير اسم المادة للتحقق من المخزون
    const itemNameInput = document.getElementById('item_name');
    itemNameInput.addEventListener('blur', checkItemStock);

    // مراقبة تغيير الفرع لتحديث التوقيع
    const branchSelect = document.getElementById('branch_name');
    branchSelect.addEventListener('change', updateBranchSignature);
});

// تحميل قائمة المواد
function loadItemsList() {
    fetch('api/get_inventory_items.php')
        .then(response => response.json())
        .then(data => {
            const datalist = document.getElementById('items_list');
            datalist.innerHTML = '';

            data.forEach(item => {
                const option = document.createElement('option');
                option.value = item.item_name;
                option.textContent = `${item.item_name} (متوفر: ${item.current_stock})`;
                datalist.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل قائمة المواد:', error);
        });
}

// التحقق من مخزون المادة
function checkItemStock() {
    const itemName = this.value;
    const quantityInput = document.getElementById('quantity');

    if (!itemName) return;

    fetch(`api/check_item_stock.php?item_name=${encodeURIComponent(itemName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                const currentStock = parseFloat(data.current_stock);
                const requestedQuantity = parseFloat(quantityInput.value) || 0;

                if (requestedQuantity > currentStock) {
                    showWarning(`تحذير: الكمية المطلوبة (${requestedQuantity}) أكبر من المتوفر في المخزن (${currentStock})`);
                    quantityInput.style.borderColor = '#ffc107';
                } else {
                    quantityInput.style.borderColor = '';
                }

                // عرض معلومات المخزون
                showStockInfo(currentStock, data.unit_of_measure);
            } else {
                showWarning('هذه المادة غير موجودة في المخزن');
            }
        })
        .catch(error => {
            console.error('خطأ في التحقق من المخزون:', error);
        });
}

// عرض معلومات المخزون
function showStockInfo(stock, unit) {
    let stockInfoDiv = document.getElementById('stock-info');
    if (!stockInfoDiv) {
        stockInfoDiv = document.createElement('div');
        stockInfoDiv.id = 'stock-info';
        stockInfoDiv.className = 'mt-1';
        document.getElementById('quantity').parentNode.appendChild(stockInfoDiv);
    }

    stockInfoDiv.innerHTML = `
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            متوفر في المخزن: ${stock} ${unit}
        </small>
    `;
}

// تحديث توقيع مدير الفرع
function updateBranchSignature() {
    const branchName = this.value;
    const signatureLabel = document.querySelector('label[for="signature_branch_director"]');

    if (branchName) {
        signatureLabel.textContent = `مدير ${branchName}`;
    } else {
        signatureLabel.textContent = 'مدير الفرع';
    }
}

// تنزيل PDF
function downloadPDF() {
    const form = document.getElementById('transferVoucherForm');
    const formData = new FormData(form);

    // إنشاء نموذج مخفي للإرسال
    const hiddenForm = document.createElement('form');
    hiddenForm.method = 'POST';
    hiddenForm.action = 'generate_pdf.php?type=transfer_voucher';
    hiddenForm.style.display = 'none';

    // إضافة البيانات
    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        hiddenForm.appendChild(input);
    }

    document.body.appendChild(hiddenForm);
    hiddenForm.submit();
    document.body.removeChild(hiddenForm);
}

// حذف السند
function deleteVoucher(id) {
    confirmDelete('هل أنت متأكد من حذف هذا السند؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// طباعة سند محدد
function printVoucher(id) {
    window.open(`print_voucher.php?id=${id}&type=transfer_voucher`, '_blank');
}

// التحقق من الكمية عند التغيير
document.getElementById('quantity').addEventListener('input', function() {
    const itemName = document.getElementById('item_name').value;
    if (itemName) {
        checkItemStock.call(document.getElementById('item_name'));
    }
});

// إحصائيات سريعة للفروع
function showBranchStats() {
    fetch('api/get_branch_stats.php')
        .then(response => response.json())
        .then(data => {
            let statsHtml = '<h6>إحصائيات التحويل للفروع:</h6><ul class="list-unstyled">';

            data.forEach(branch => {
                statsHtml += `
                    <li class="mb-1">
                        <span class="badge bg-primary me-2">${branch.branch_name}</span>
                        <small>${branch.total_transfers} تحويل</small>
                    </li>
                `;
            });

            statsHtml += '</ul>';

            Swal.fire({
                title: 'إحصائيات الفروع',
                html: statsHtml,
                icon: 'info',
                confirmButtonText: 'موافق'
            });
        })
        .catch(error => {
            console.error('خطأ في جلب الإحصائيات:', error);
        });
}

// إضافة زر الإحصائيات
document.querySelector('.card-header h5').insertAdjacentHTML('afterend', `
    <button type="button" class="btn btn-sm btn-outline-info mt-2" onclick="showBranchStats()">
        <i class="fas fa-chart-pie me-1"></i> إحصائيات الفروع
    </button>
`);
</script>

<?php include 'includes/footer.php'; ?>
