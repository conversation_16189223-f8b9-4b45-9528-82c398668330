<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // إحصائيات عامة
    $stmt = $db->query("SELECT 
                           COUNT(*) as total,
                           SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                           SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive
                       FROM national_office_branches");
    $general_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إجمالي التحويلات
    $stmt = $db->query("SELECT COUNT(*) as total_transfers FROM transfer_vouchers");
    $transfer_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // أكثر الفروع نشاطاً
    $stmt = $db->query("SELECT 
                           nob.branch_name,
                           COUNT(tv.id) as transfer_count
                       FROM national_office_branches nob
                       LEFT JOIN transfer_vouchers tv ON nob.branch_name = tv.branch_name
                       WHERE nob.is_active = 1
                       GROUP BY nob.branch_name
                       ORDER BY transfer_count DESC
                       LIMIT 5");
    $top_branches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'total' => $general_stats['total'],
            'active' => $general_stats['active'],
            'inactive' => $general_stats['inactive'],
            'total_transfers' => $transfer_stats['total_transfers'],
            'top_branches' => $top_branches
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم'
    ], JSON_UNESCAPED_UNICODE);
}
?>
