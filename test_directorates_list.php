<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قائمة المديريات والمصالح</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; }
        .directorate-item { padding: 8px; border-bottom: 1px solid #eee; }
        .directorate-item:hover { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-building me-2"></i>
                            اختبار قائمة المديريات والمصالح (27 خيار)
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- القائمة المنسدلة -->
                        <div class="mb-4">
                            <label for="directorate_select" class="form-label">اختر المديرية أو المصلحة:</label>
                            <select class="form-select" id="directorate_select" onchange="showSelected()">
                                <option value="">اختر المديرية أو المصلحة</option>
                                <?php
                                // قائمة المديريات والمصالح المحدثة (27 خيار)
                                $directorates_list = [
                                    'مدير الديوان',
                                    'الأمانة الخاصة',
                                    'الأمين العام',
                                    'الأمانة العامة',
                                    'المدير الفرعي للإدارة العامة',
                                    'أمانة المدير الفرعي للإدارة العامة',
                                    'المدير الفرعي للبكالوريا',
                                    'أمانة المدير الفرعي للبكالوريا',
                                    'المدير الفرعي للامتحانات والمسابقات المدرسية',
                                    'المدير الفرعي للامتحانات والمسابقات المهنية',
                                    'المديرية الفرعية للدراسات والتقويم',
                                    'رئيس مصلحة الإدارة العامة',
                                    'رئيس مصلحة الموظفين',
                                    'رئيس مصلحة الإيرادات والنفقات',
                                    'رئيس مصلحة الوسائل',
                                    'رئيس مصلحة الشهادات',
                                    'مكتب العون المحاسب',
                                    'مكتب الوكيل',
                                    'مكتب الالتزامات',
                                    'مكتب الصفقات',
                                    'مكتب دفع الأجور',
                                    'مكتب المخزني',
                                    'الخلية المركزية',
                                    'مكتب الامتحانات المدرسية',
                                    'مكتب الامتحانات المهنية',
                                    'مكتب الإعلام الآلي',
                                    'قاعة الاستقبال والتوجيه'
                                ];

                                foreach ($directorates_list as $directorate) {
                                    echo '<option value="' . htmlspecialchars($directorate) . '">';
                                    echo htmlspecialchars($directorate);
                                    echo '</option>';
                                }
                                ?>
                            </select>
                        </div>

                        <!-- عرض الاختيار -->
                        <div id="selected_result" class="alert alert-info" style="display: none;">
                            <strong>تم اختيار:</strong> <span id="selected_text"></span>
                        </div>

                        <!-- عرض جميع الخيارات -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">جميع المديريات والمصالح المتاحة (<?php echo count($directorates_list); ?> خيار):</h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary">المديريات العليا والفرعية:</h6>
                                        <?php
                                        $high_level = array_slice($directorates_list, 0, 11);
                                        foreach ($high_level as $index => $directorate) {
                                            echo '<div class="directorate-item">';
                                            echo '<span class="badge bg-secondary me-2">' . ($index + 1) . '</span>';
                                            echo htmlspecialchars($directorate);
                                            echo '</div>';
                                        }
                                        ?>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6 class="text-success">المصالح والمكاتب:</h6>
                                        <?php
                                        $departments = array_slice($directorates_list, 11);
                                        foreach ($departments as $index => $directorate) {
                                            echo '<div class="directorate-item">';
                                            echo '<span class="badge bg-secondary me-2">' . ($index + 12) . '</span>';
                                            echo htmlspecialchars($directorate);
                                            echo '</div>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-success">
                                    <h6>إحصائيات القائمة:</h6>
                                    <ul class="mb-0">
                                        <li><strong>إجمالي الخيارات:</strong> <?php echo count($directorates_list); ?> خيار</li>
                                        <li><strong>المديريات العليا:</strong> 4 خيارات</li>
                                        <li><strong>المديريات الفرعية:</strong> 7 خيارات</li>
                                        <li><strong>رؤساء المصالح:</strong> 5 خيارات</li>
                                        <li><strong>المكاتب المتخصصة:</strong> 11 خيار</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الاختبار -->
                        <div class="text-center mt-4">
                            <a href="office_supplies.php" class="btn btn-primary btn-lg me-2">
                                <i class="fas fa-external-link-alt me-2"></i>
                                فتح نظام اللوازم المكتبية
                            </a>
                            <button type="button" class="btn btn-success btn-lg" onclick="testRandomSelection()">
                                <i class="fas fa-random me-2"></i>
                                اختبار اختيار عشوائي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // عرض الاختيار
        function showSelected() {
            const select = document.getElementById('directorate_select');
            const result = document.getElementById('selected_result');
            const text = document.getElementById('selected_text');
            
            if (select.value) {
                text.textContent = select.value;
                result.style.display = 'block';
            } else {
                result.style.display = 'none';
            }
        }

        // اختبار اختيار عشوائي
        function testRandomSelection() {
            const select = document.getElementById('directorate_select');
            const options = select.options;
            const randomIndex = Math.floor(Math.random() * (options.length - 1)) + 1; // تجنب الخيار الفارغ
            
            select.selectedIndex = randomIndex;
            showSelected();
            
            // تمييز الاختيار
            select.style.backgroundColor = '#fff3cd';
            setTimeout(() => {
                select.style.backgroundColor = '';
            }, 2000);
        }

        // تحديد عدد الخيارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const select = document.getElementById('directorate_select');
            const totalOptions = select.options.length - 1; // طرح الخيار الفارغ
            console.log('إجمالي خيارات المديريات:', totalOptions);
        });
    </script>
</body>
</html>
