<?php
require_once '../config/config.php';
require_once '../config/database.php';

$page_title = 'لوحة التقارير والإحصائيات';
$database = new Database();
$db = $database->getConnection();

// جلب الإحصائيات العامة
function getGeneralStats($db) {
    $stats = [];
    
    // إحصائيات بطاقات المخزون
    $stmt = $db->query("SELECT 
                           COUNT(*) as total_items,
                           SUM(current_stock) as total_stock,
                           COUNT(CASE WHEN current_stock <= 10 THEN 1 END) as low_stock_items
                       FROM inventory_cards");
    $stats['inventory'] = $stmt->fetch();
    
    // إحصائيات الممونين
    $stmt = $db->query("SELECT COUNT(*) as total_suppliers FROM suppliers");
    $stats['suppliers'] = $stmt->fetch();
    
    // إحصائيات وصولات دخول السلع
    $stmt = $db->query("SELECT 
                           COUNT(*) as total_receipts,
                           SUM(total_with_tax) as total_value
                       FROM goods_entry_receipts");
    $stats['goods_entry'] = $stmt->fetch();
    
    // إحصائيات طلبات اللوازم المكتبية
    $stmt = $db->query("SELECT 
                           COUNT(*) as total_requests,
                           SUM(quantity) as total_quantity
                       FROM office_supplies_requests");
    $stats['office_supplies'] = $stmt->fetch();
    
    // إحصائيات سندات التحويل
    $stmt = $db->query("SELECT 
                           COUNT(*) as total_vouchers,
                           SUM(quantity) as total_transferred
                       FROM transfer_vouchers");
    $stats['transfers'] = $stmt->fetch();
    
    // إحصائيات الجرد العام
    $stmt = $db->query("SELECT 
                           COUNT(*) as total_items,
                           SUM(value) as total_value
                       FROM general_inventory");
    $stats['general_inventory'] = $stmt->fetch();
    
    // إحصائيات الاستشارات
    $stmt = $db->query("SELECT 
                           COUNT(*) as total_consultations,
                           SUM(total_amount) as total_amount
                       FROM consultations");
    $stats['consultations'] = $stmt->fetch();
    
    // إحصائيات الفروع
    $stmt = $db->query("SELECT 
                           COUNT(*) as total_branches,
                           COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_branches
                       FROM national_office_branches");
    $stats['branches'] = $stmt->fetch();
    
    return $stats;
}

// جلب البيانات للرسوم البيانية
function getChartData($db) {
    $charts = [];
    
    // رسم بياني لدخول السلع حسب الشهر
    $stmt = $db->query("SELECT 
                           DATE_FORMAT(receipt_date, '%Y-%m') as month,
                           COUNT(*) as count,
                           SUM(total_with_tax) as total_value
                       FROM goods_entry_receipts 
                       WHERE receipt_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                       GROUP BY month 
                       ORDER BY month");
    $charts['goods_entry_monthly'] = $stmt->fetchAll();
    
    // رسم بياني للتحويلات حسب الفرع
    $stmt = $db->query("SELECT 
                           branch_name,
                           COUNT(*) as transfer_count,
                           SUM(quantity) as total_quantity
                       FROM transfer_vouchers 
                       GROUP BY branch_name 
                       ORDER BY transfer_count DESC 
                       LIMIT 10");
    $charts['transfers_by_branch'] = $stmt->fetchAll();
    
    // رسم بياني للمخزون المنخفض
    $stmt = $db->query("SELECT 
                           item_name,
                           current_stock,
                           unit_of_measure
                       FROM inventory_cards 
                       WHERE current_stock <= 10 
                       ORDER BY current_stock ASC 
                       LIMIT 10");
    $charts['low_stock_items'] = $stmt->fetchAll();
    
    // رسم بياني للاستشارات حسب البلد
    $stmt = $db->query("SELECT 
                           country,
                           COUNT(*) as consultation_count,
                           SUM(total_amount) as total_amount
                       FROM consultations 
                       GROUP BY country 
                       ORDER BY consultation_count DESC");
    $charts['consultations_by_country'] = $stmt->fetchAll();
    
    return $charts;
}

$stats = getGeneralStats($db);
$charts = getChartData($db);

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    لوحة التقارير والإحصائيات
                </h4>
            </div>
            <div class="card-body">
                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($stats['inventory']['total_items']); ?></h4>
                                        <p class="mb-0">عناصر المخزون</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-boxes fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($stats['suppliers']['total_suppliers']); ?></h4>
                                        <p class="mb-0">الممونين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-truck fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($stats['inventory']['low_stock_items']); ?></h4>
                                        <p class="mb-0">تنبيهات المخزون</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($stats['branches']['active_branches']); ?></h4>
                                        <p class="mb-0">الفروع النشطة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-map-marker-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الرسوم البيانية -->
                <div class="row">
                    <!-- رسم بياني لدخول السلع -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">دخول السلع حسب الشهر</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="goodsEntryChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- رسم بياني للتحويلات حسب الفرع -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">التحويلات حسب الفرع</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="transfersChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- تنبيهات المخزون المنخفض -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">تنبيهات المخزون المنخفض</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($charts['low_stock_items'])): ?>
                                    <div class="text-center text-muted">
                                        <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                        <p>جميع العناصر في المخزون ضمن الحد الآمن</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>اسم المادة</th>
                                                    <th>الكمية المتبقية</th>
                                                    <th>الوحدة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($charts['low_stock_items'] as $item): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($item['item_name']); ?></td>
                                                        <td>
                                                            <span class="badge bg-danger">
                                                                <?php echo $item['current_stock']; ?>
                                                            </span>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($item['unit_of_measure']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الاستشارات حسب البلد -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">الاستشارات حسب البلد</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="consultationsChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- تفاصيل إضافية -->
                <div class="row">
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">إحصائيات مالية</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>قيمة دخول السلع:</strong><br>
                                        <span class="text-success">
                                            <?php echo number_format($stats['goods_entry']['total_value'], 2); ?> دج
                                        </span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>قيمة الجرد العام:</strong><br>
                                        <span class="text-info">
                                            <?php echo number_format($stats['general_inventory']['total_value'], 2); ?> دج
                                        </span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>قيمة الاستشارات:</strong><br>
                                        <span class="text-warning">
                                            <?php echo number_format($stats['consultations']['total_amount'], 2); ?> دج
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">إحصائيات العمليات</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>وصولات دخول السلع:</strong><br>
                                        <span class="badge bg-primary">
                                            <?php echo number_format($stats['goods_entry']['total_receipts']); ?>
                                        </span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>طلبات اللوازم المكتبية:</strong><br>
                                        <span class="badge bg-success">
                                            <?php echo number_format($stats['office_supplies']['total_requests']); ?>
                                        </span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>سندات التحويل:</strong><br>
                                        <span class="badge bg-info">
                                            <?php echo number_format($stats['transfers']['total_vouchers']); ?>
                                        </span>
                                    </li>
                                    <li class="mb-2">
                                        <strong>الاستشارات:</strong><br>
                                        <span class="badge bg-warning">
                                            <?php echo number_format($stats['consultations']['total_consultations']); ?>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">إجراءات سريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="generateFullReport()">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        تقرير شامل
                                    </button>
                                    <button class="btn btn-outline-success" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel me-1"></i>
                                        تصدير Excel
                                    </button>
                                    <button class="btn btn-outline-info" onclick="printDashboard()">
                                        <i class="fas fa-print me-1"></i>
                                        طباعة اللوحة
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="scheduleReport()">
                                        <i class="fas fa-clock me-1"></i>
                                        جدولة التقارير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تضمين Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// بيانات الرسوم البيانية
const goodsEntryData = <?php echo json_encode($charts['goods_entry_monthly']); ?>;
const transfersData = <?php echo json_encode($charts['transfers_by_branch']); ?>;
const consultationsData = <?php echo json_encode($charts['consultations_by_country']); ?>;

// رسم بياني لدخول السلع حسب الشهر
const goodsEntryCtx = document.getElementById('goodsEntryChart').getContext('2d');
new Chart(goodsEntryCtx, {
    type: 'line',
    data: {
        labels: goodsEntryData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('ar-DZ', { year: 'numeric', month: 'long' });
        }),
        datasets: [{
            label: 'عدد الوصولات',
            data: goodsEntryData.map(item => item.count),
            borderColor: '#2c5530',
            backgroundColor: 'rgba(44, 85, 48, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'القيمة الإجمالية (دج)',
            data: goodsEntryData.map(item => item.total_value),
            borderColor: '#4a7c59',
            backgroundColor: 'rgba(74, 124, 89, 0.1)',
            tension: 0.4,
            fill: false,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'تطور دخول السلع خلال الـ 12 شهر الماضية'
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'عدد الوصولات'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'القيمة (دج)'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// رسم بياني للتحويلات حسب الفرع
const transfersCtx = document.getElementById('transfersChart').getContext('2d');
new Chart(transfersCtx, {
    type: 'bar',
    data: {
        labels: transfersData.map(item => item.branch_name),
        datasets: [{
            label: 'عدد التحويلات',
            data: transfersData.map(item => item.transfer_count),
            backgroundColor: [
                '#2c5530', '#4a7c59', '#28a745', '#20c997', '#17a2b8',
                '#6f42c1', '#e83e8c', '#fd7e14', '#ffc107', '#dc3545'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            title: {
                display: true,
                text: 'التحويلات حسب الفرع'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'عدد التحويلات'
                }
            }
        }
    }
});

// رسم بياني للاستشارات حسب البلد
const consultationsCtx = document.getElementById('consultationsChart').getContext('2d');
new Chart(consultationsCtx, {
    type: 'doughnut',
    data: {
        labels: consultationsData.map(item => item.country),
        datasets: [{
            data: consultationsData.map(item => item.consultation_count),
            backgroundColor: [
                '#2c5530', '#4a7c59', '#28a745', '#20c997', '#17a2b8',
                '#6f42c1', '#e83e8c', '#fd7e14', '#ffc107', '#dc3545',
                '#6c757d', '#343a40', '#007bff'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
            },
            title: {
                display: true,
                text: 'توزيع الاستشارات حسب البلد'
            }
        }
    }
});

// تحديث البيانات تلقائياً كل 5 دقائق
setInterval(function() {
    location.reload();
}, 300000); // 5 دقائق

// إنشاء تقرير شامل
function generateFullReport() {
    showLoading();

    fetch('../api/generate_full_report.php')
        .then(response => response.blob())
        .then(blob => {
            hideLoading();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `تقرير_شامل_${new Date().toISOString().split('T')[0]}.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            showSuccess('تم إنشاء التقرير الشامل بنجاح');
        })
        .catch(error => {
            hideLoading();
            console.error('خطأ:', error);
            showError('خطأ في إنشاء التقرير');
        });
}

// تصدير إلى Excel
function exportToExcel() {
    showLoading();

    fetch('../api/export_dashboard_excel.php')
        .then(response => response.blob())
        .then(blob => {
            hideLoading();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `لوحة_التقارير_${new Date().toISOString().split('T')[0]}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            showSuccess('تم تصدير البيانات إلى Excel بنجاح');
        })
        .catch(error => {
            hideLoading();
            console.error('خطأ:', error);
            showError('خطأ في تصدير البيانات');
        });
}

// طباعة اللوحة
function printDashboard() {
    window.print();
}

// جدولة التقارير
function scheduleReport() {
    Swal.fire({
        title: 'جدولة التقارير',
        html: `
            <div class="mb-3">
                <label for="reportType" class="form-label">نوع التقرير:</label>
                <select id="reportType" class="form-select">
                    <option value="daily">تقرير يومي</option>
                    <option value="weekly">تقرير أسبوعي</option>
                    <option value="monthly">تقرير شهري</option>
                </select>
            </div>
            <div class="mb-3">
                <label for="reportTime" class="form-label">وقت الإرسال:</label>
                <input type="time" id="reportTime" class="form-control" value="08:00">
            </div>
            <div class="mb-3">
                <label for="reportEmail" class="form-label">البريد الإلكتروني:</label>
                <input type="email" id="reportEmail" class="form-control" placeholder="<EMAIL>">
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'جدولة',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const reportType = document.getElementById('reportType').value;
            const reportTime = document.getElementById('reportTime').value;
            const reportEmail = document.getElementById('reportEmail').value;

            if (!reportEmail) {
                Swal.showValidationMessage('يرجى إدخال البريد الإلكتروني');
                return false;
            }

            return { reportType, reportTime, reportEmail };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب جدولة التقرير
            fetch('../api/schedule_report.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(result.value)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('تم جدولة التقرير بنجاح');
                } else {
                    showError(data.message || 'خطأ في جدولة التقرير');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showError('خطأ في الاتصال بالخادم');
            });
        }
    });
}

// تحديث الإحصائيات في الوقت الفعلي
function updateRealTimeStats() {
    fetch('../api/get_realtime_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الأرقام في البطاقات
                updateStatCard('total_items', data.stats.inventory.total_items);
                updateStatCard('total_suppliers', data.stats.suppliers.total_suppliers);
                updateStatCard('low_stock_items', data.stats.inventory.low_stock_items);
                updateStatCard('active_branches', data.stats.branches.active_branches);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

// تحديث بطاقة إحصائية
function updateStatCard(type, value) {
    const cards = document.querySelectorAll('.card h4');
    cards.forEach(card => {
        // تحديد البطاقة المناسبة وتحديث القيمة
        if (card.textContent.includes(value.toString())) {
            card.textContent = formatNumber(value);
        }
    });
}

// تنسيق الأرقام
function formatNumber(number) {
    return new Intl.NumberFormat('ar-DZ').format(number);
}

// تحديث الإحصائيات كل دقيقة
setInterval(updateRealTimeStats, 60000);

// إضافة تأثيرات بصرية للبطاقات
document.addEventListener('DOMContentLoaded', function() {
    // تأثير العد التصاعدي للأرقام
    const statCards = document.querySelectorAll('.card h4');
    statCards.forEach(card => {
        const finalValue = parseInt(card.textContent.replace(/,/g, ''));
        if (!isNaN(finalValue)) {
            animateCounter(card, 0, finalValue, 1000);
        }
    });
});

// تحريك العداد
function animateCounter(element, start, end, duration) {
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
        current += increment;
        if (current >= end) {
            current = end;
            clearInterval(timer);
        }
        element.textContent = formatNumber(Math.floor(current));
    }, 16);
}

// إضافة تنبيهات للمخزون المنخفض
function checkLowStockAlerts() {
    const lowStockCount = <?php echo $stats['inventory']['low_stock_items']; ?>;

    if (lowStockCount > 0) {
        // عرض تنبيه في أعلى الصفحة
        const alertHtml = `
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> يوجد ${lowStockCount} عنصر في المخزون بكمية منخفضة.
                <a href="../inventory_cards.php" class="alert-link">عرض التفاصيل</a>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.querySelector('.card-body').insertAdjacentHTML('afterbegin', alertHtml);
    }
}

// تشغيل فحص التنبيهات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', checkLowStockAlerts);
</script>

<style>
/* أنماط خاصة بلوحة التقارير */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card h4 {
    font-weight: bold;
    margin-bottom: 0;
}

.chart-container {
    position: relative;
    height: 300px;
}

@media print {
    .card {
        break-inside: avoid;
        margin-bottom: 20px;
    }

    .btn {
        display: none;
    }

    .chart-container {
        height: 250px;
    }
}

/* تأثيرات الرسوم البيانية */
canvas {
    max-height: 300px;
}

/* تنسيق خاص للإحصائيات */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    color: white;
}

.stat-card .card-body {
    padding: 1.5rem;
}

.stat-card h4 {
    font-size: 2rem;
    font-weight: 700;
}

.stat-card i {
    opacity: 0.8;
}
</style>

<?php include '../includes/footer.php'; ?>
