<?php
// ملف تصدير طلب اللوازم المكتبية - التصميم الجديد
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تضمين الإعدادات
require_once '../config/config.php';

// التحقق من الطريقة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die('يجب استخدام POST method');
}

// التحقق من نوع التصدير
$format = $_GET['format'] ?? 'pdf';
if (!in_array($format, ['pdf', 'docx', 'doc'])) {
    die('نوع ملف غير مدعوم');
}

// استلام البيانات
$data = [
    'receipt_number' => $_POST['receipt_number'] ?? '',
    'receipt_date' => $_POST['receipt_date'] ?? date('Y-m-d'),
    'department' => $_POST['department'] ?? '',
    'beneficiary' => $_POST['beneficiary'] ?? '',
    'supply_number' => $_POST['supply_number'] ?? '',
    'notes' => $_POST['notes'] ?? '',
];

// استلام المواد
$materials = [];
if (isset($_POST['materials']) && is_array($_POST['materials'])) {
    $materials = $_POST['materials'];
} elseif (isset($_POST['item_name']) && is_array($_POST['item_name'])) {
    // تنسيق قديم
    $count = count($_POST['item_name']);
    for ($i = 0; $i < $count; $i++) {
        if (!empty($_POST['item_name'][$i])) {
            $materials[] = [
                'item_name' => $_POST['item_name'][$i],
                'quantity' => $_POST['quantity'][$i] ?? 0,
                'supply_number' => $_POST['supply_number_item'][$i] ?? '',
                'notes' => $_POST['notes_item'][$i] ?? '',
            ];
        }
    }
}

// إنشاء المحتوى
function createNewContent($data, $materials) {
    // إنشاء جدول المواد
    $materialsTableHtml = '<table border="1" cellpadding="8" cellspacing="0" style="width: 100%; border-collapse: collapse; margin-top: 20px; margin-bottom: 20px; direction: rtl; text-align: center;">
        <thead>
            <tr style="background-color: #f2f2f2;">
                <th style="width: 8%; border: 1px solid #000; padding: 8px;">الرقم</th>
                <th style="width: 50%; border: 1px solid #000; padding: 8px;">اسم المادة</th>
                <th style="width: 15%; border: 1px solid #000; padding: 8px;">الكمية</th>
                <th style="width: 12%; border: 1px solid #000; padding: 8px;">رقم اللوازم</th>
                <th style="width: 15%; border: 1px solid #000; padding: 8px;">ملاحظات</th>
            </tr>
        </thead>
        <tbody>';
    
    if (empty($materials)) {
        $materialsTableHtml .= '<tr><td colspan="5" style="border: 1px solid #000; padding: 8px;">لا توجد مواد مسجلة</td></tr>';
    } else {
        foreach ($materials as $index => $material) {
            $materialsTableHtml .= '<tr>
                <td style="border: 1px solid #000; padding: 8px;">' . ($index + 1) . '</td>
                <td style="border: 1px solid #000; padding: 8px; text-align: right; font-weight: bold;">' . htmlspecialchars($material['item_name']) . '</td>
                <td style="border: 1px solid #000; padding: 8px;"><strong>' . number_format($material['quantity'], 0) . '</strong></td>
                <td style="border: 1px solid #000; padding: 8px;">' . htmlspecialchars($material['supply_number'] ?: '-') . '</td>
                <td style="border: 1px solid #000; padding: 8px;">' . htmlspecialchars($material['notes'] ?? '-') . '</td>
            </tr>';
        }
    }
    
    $materialsTableHtml .= '</tbody></table>';
    
    // إنشاء قسم التوقيعات
    $signaturesHtml = '<div style="margin-top: 40px; page-break-inside: avoid;">
        <h3 style="text-align: center; margin-bottom: 30px;">التوقيعات والأختام</h3>
        
        <!-- الصف الأول -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 40px;">
            <div style="width: 45%; border-bottom: 1px solid #000; padding: 20px 10px; text-align: center; min-height: 100px;">
                إمضاء وختم رئيس مصلحة الوسائل
            </div>
            <div style="width: 45%; border-bottom: 1px solid #000; padding: 20px 10px; text-align: center; min-height: 100px;">
                إمضاء وختم المدير الفرعي للإدارة العامة
            </div>
        </div>
        
        <!-- الصف الثاني -->
        <div style="display: flex; justify-content: space-between;">
            <div style="width: 45%; border-bottom: 1px solid #000; padding: 20px 10px; text-align: center; min-height: 100px;">
                إمضاء وختم المكلف بتسيير المخزن
            </div>
            <div style="width: 45%; border-bottom: 1px solid #000; padding: 20px 10px; text-align: center; min-height: 100px;">
                إمضاء وختم المستفيد
            </div>
        </div>
    </div>';
    
    // إنشاء HTML كامل
    return '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>طلب لوازم مكتبية</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #000;
            direction: rtl;
            font-size: 14px;
            font-weight: bold;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px double #000;
            padding-bottom: 10px;
        }
        .institution {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .ministry {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .office {
            font-size: 14px;
            margin-bottom: 5px;
        }
        .title {
            font-size: 22px;
            font-weight: bold;
            margin: 30px 0;
            text-align: center;
            text-decoration: underline;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 15px;
        }
        .info-label {
            font-weight: bold;
            margin-left: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="institution">' . INSTITUTION_NAME_AR . '</div>
        <div class="ministry">' . MINISTRY_NAME_AR . '</div>
        <div class="office">' . OFFICE_NAME_AR . '</div>
    </div>
    
    <div class="title">طلب لوازم مكتبية</div>
    
    <div class="info-section">
        <div class="info-row">
            <div><span class="info-label">رقم الوصل:</span> ' . htmlspecialchars($data['receipt_number']) . '</div>
            <div><span class="info-label">التاريخ:</span> ' . htmlspecialchars($data['receipt_date']) . '</div>
        </div>
        <div class="info-row">
            <div><span class="info-label">القسم/المصلحة:</span> ' . htmlspecialchars($data['department']) . '</div>
            <div><span class="info-label">المستفيد:</span> ' . htmlspecialchars($data['beneficiary']) . '</div>
        </div>
        ' . ($data['supply_number'] ? '<div class="info-row"><div><span class="info-label">رقم اللوازم:</span> ' . htmlspecialchars($data['supply_number']) . '</div></div>' : '') . '
        ' . ($data['notes'] ? '<div class="info-row"><div><span class="info-label">الملاحظات:</span> ' . htmlspecialchars($data['notes']) . '</div></div>' : '') . '
    </div>
    
    ' . $materialsTableHtml . '
    
    ' . $signaturesHtml . '
    
    <div style="margin-top: 30px; text-align: center; border-top: 1px solid #ccc; padding-top: 20px; font-size: 14px; color: #666;">
        تم إنشاء هذا المستند تلقائ<|im_start|> من نظام تسيير المخزن - ' . date('Y-m-d H:i:s') . '
    </div>
</body>
</html>';
}

// إنشاء المحتوى
$content = createNewContent($data, $materials);

// تحديد نوع الملف والتنزيل
$filename = 'طلب_لوازم_مكتبية_' . $data['receipt_number'] . '_' . date('Y-m-d');

// تنظيف المخرجات
while (ob_get_level()) {
    ob_end_clean();
}

// تحديد headers حسب نوع الملف
switch ($format) {
    case 'pdf':
        // تصدير PDF باستخدام المتصفح
        header('Content-Type: text/html; charset=UTF-8');
        echo $content;
        echo '<script>
            window.onload = function() {
                window.print();
            }
        </script>';
        break;

    case 'docx':
    case 'doc':
        // تصدير Word
        header('Content-Type: application/vnd.ms-word; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.doc"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        // إضافة BOM للعربية
        echo "\xEF\xBB\xBF";
        echo $content;
        break;

    default:
        die('نوع ملف غير مدعوم');
}
?><|im_end|>


