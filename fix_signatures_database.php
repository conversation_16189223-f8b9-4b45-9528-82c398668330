<?php
require_once 'config/config.php';
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشكلة التوقيعات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; }</style>";
echo "</head>";
echo "<body>";
echo "<div class='container my-5'>";
echo "<div class='card'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h3 class='mb-0'><i class='fas fa-signature'></i> إصلاح مشكلة التوقيعات</h3>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<div class='alert alert-info'>";
    echo "<h5>🔍 تشخيص المشكلة...</h5>";
    echo "</div>";

    // التحقق من وجود الجدول
    $stmt = $db->query("SHOW TABLES LIKE 'office_supplies_requests'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("جدول office_supplies_requests غير موجود!");
    }
    
    echo "<div class='alert alert-success'>";
    echo "✅ جدول office_supplies_requests موجود";
    echo "</div>";

    // التحقق من الأعمدة الموجودة
    $stmt = $db->query("DESCRIBE office_supplies_requests");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='alert alert-info'>";
    echo "<h6>الأعمدة الموجودة حالياً:</h6>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<ul class='mb-0'>";
    $half = ceil(count($existing_columns) / 2);
    for ($i = 0; $i < $half; $i++) {
        echo "<li>{$existing_columns[$i]}</li>";
    }
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<ul class='mb-0'>";
    for ($i = $half; $i < count($existing_columns); $i++) {
        echo "<li>{$existing_columns[$i]}</li>";
    }
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    // التحقق من أعمدة التوقيعات
    $signature_columns = [
        'signature_means_chief' => "TINYINT(1) DEFAULT 1 COMMENT 'توقيع رئيس مصلحة الوسائل'",
        'signature_sub_director' => "TINYINT(1) DEFAULT 1 COMMENT 'توقيع المدير الفرعي للإدارة العامة'",
        'signature_warehouse_manager' => "TINYINT(1) DEFAULT 1 COMMENT 'توقيع المكلف بتسيير المخزن'",
        'signature_beneficiary' => "TINYINT(1) DEFAULT 1 COMMENT 'توقيع المستفيد'"
    ];

    $missing_signature_columns = [];
    foreach ($signature_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            $missing_signature_columns[$column_name] = $column_definition;
        }
    }

    if (!empty($missing_signature_columns)) {
        echo "<div class='alert alert-warning'>";
        echo "<h5>⚠️ أعمدة التوقيعات المفقودة:</h5>";
        echo "<ul>";
        foreach ($missing_signature_columns as $column_name => $definition) {
            echo "<li><strong>$column_name</strong></li>";
        }
        echo "</ul>";
        echo "</div>";

        // إضافة الأعمدة المفقودة
        foreach ($missing_signature_columns as $column_name => $column_definition) {
            $sql = "ALTER TABLE office_supplies_requests ADD COLUMN $column_name $column_definition";
            $db->exec($sql);
            echo "<div class='alert alert-success'>";
            echo "✅ تم إضافة العمود: <strong>$column_name</strong>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-success'>";
        echo "✅ جميع أعمدة التوقيعات موجودة بالفعل";
        echo "</div>";
    }

    // التحقق من أعمدة التنسيق أيضاً
    $formatting_columns = [
        'bold_text' => "TINYINT(1) DEFAULT 0 COMMENT 'نص عريض'",
        'text_align' => "VARCHAR(10) DEFAULT 'right' COMMENT 'محاذاة النص'",
        'font_color' => "VARCHAR(20) DEFAULT 'black' COMMENT 'لون الخط'"
    ];

    $missing_formatting_columns = [];
    foreach ($formatting_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            $missing_formatting_columns[$column_name] = $column_definition;
        }
    }

    if (!empty($missing_formatting_columns)) {
        echo "<div class='alert alert-info'>";
        echo "<h6>💡 أعمدة التنسيق المفقودة (اختيارية):</h6>";
        echo "<ul>";
        foreach ($missing_formatting_columns as $column_name => $definition) {
            echo "<li><strong>$column_name</strong></li>";
        }
        echo "</ul>";
        echo "</div>";

        // إضافة أعمدة التنسيق
        foreach ($missing_formatting_columns as $column_name => $column_definition) {
            $sql = "ALTER TABLE office_supplies_requests ADD COLUMN $column_name $column_definition";
            $db->exec($sql);
            echo "<div class='alert alert-success'>";
            echo "✅ تم إضافة عمود التنسيق: <strong>$column_name</strong>";
            echo "</div>";
        }
    }

    // التحقق النهائي
    $stmt = $db->query("DESCRIBE office_supplies_requests");
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='alert alert-success'>";
    echo "<h5>🎉 تم الإصلاح بنجاح!</h5>";
    echo "<h6>هيكل الجدول النهائي:</h6>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-bordered'>";
    echo "<thead><tr><th>اسم العمود</th><th>النوع</th><th>افتراضي</th><th>تعليق</th></tr></thead>";
    echo "<tbody>";
    foreach ($final_columns as $column) {
        $highlight = '';
        if (strpos($column['Field'], 'signature_') === 0) {
            $highlight = 'table-warning'; // أعمدة التوقيعات
        } elseif (in_array($column['Field'], ['bold_text', 'text_align', 'font_color'])) {
            $highlight = 'table-info'; // أعمدة التنسيق
        }
        echo "<tr class='$highlight'>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>" . (isset($column['Comment']) ? $column['Comment'] : '') . "</td>";
        echo "</tr>";
    }
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    echo "</div>";

    // اختبار التوقيعات
    echo "<div class='alert alert-info'>";
    echo "<h6>🧪 اختبار التوقيعات:</h6>";
    
    $test_sql = "SELECT COUNT(*) as total FROM office_supplies_requests";
    $stmt = $db->query($test_sql);
    $count = $stmt->fetch()['total'];
    
    if ($count > 0) {
        // تحديث السجلات الموجودة بقيم افتراضية للتوقيعات
        $update_sql = "UPDATE office_supplies_requests 
                      SET signature_means_chief = COALESCE(signature_means_chief, 1),
                          signature_sub_director = COALESCE(signature_sub_director, 1),
                          signature_warehouse_manager = COALESCE(signature_warehouse_manager, 1),
                          signature_beneficiary = COALESCE(signature_beneficiary, 1)
                      WHERE signature_means_chief IS NULL OR signature_sub_director IS NULL 
                         OR signature_warehouse_manager IS NULL OR signature_beneficiary IS NULL";
        $db->exec($update_sql);
        echo "✅ تم تحديث $count سجل موجود بتوقيعات افتراضية";
        echo "<br><small class='text-muted'>جميع التوقيعات مفعلة افتراضياً للسجلات الموجودة</small>";
    } else {
        echo "ℹ️ لا توجد سجلات في الجدول حالياً";
    }
    echo "</div>";

    echo "<div class='alert alert-success'>";
    echo "<h4>🎉 تم إصلاح مشكلة التوقيعات بنجاح!</h4>";
    echo "<p><strong>ما تم إصلاحه:</strong></p>";
    echo "<ul>";
    echo "<li>✅ إضافة أعمدة التوقيعات الأربعة</li>";
    echo "<li>✅ إضافة أعمدة التنسيق (إن لم تكن موجودة)</li>";
    echo "<li>✅ تحديث السجلات الموجودة بقيم افتراضية</li>";
    echo "<li>✅ تحديث ملف الطباعة للتعامل مع الحالتين</li>";
    echo "</ul>";
    echo "<p><strong>النتيجة:</strong> التوقيعات ستظهر الآن في الطباعة حسب اختيارك!</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ خطأ في الإصلاح:</h5>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الحل:</strong> تأكد من صحة إعدادات قاعدة البيانات في ملف config.php</p>";
    echo "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='office_supplies.php' class='btn btn-primary btn-lg me-2'>";
echo "<i class='fas fa-external-link-alt me-2'></i>اختبار النظام الآن";
echo "</a>";
echo "<a href='test_compact_print.html' class='btn btn-success btn-lg'>";
echo "<i class='fas fa-print me-2'></i>اختبار الطباعة";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
