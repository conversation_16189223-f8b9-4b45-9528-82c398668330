<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$print_all = $_GET['print_all'] ?? false;

if (empty($id) && !$print_all) {
    die('معرف البطاقة مطلوب');
}

// جلب بيانات البطاقة أو جميع البطاقات
if ($print_all) {
    $stmt = $db->query("SELECT * FROM inventory_cards ORDER BY item_name ASC");
    $cards = $stmt->fetchAll();
    if (empty($cards)) {
        die('لا توجد بطاقات للطباعة');
    }
} else {
    $stmt = $db->prepare("SELECT * FROM inventory_cards WHERE id = ?");
    $stmt->execute([$id]);
    $card = $stmt->fetch();
    if (!$card) {
        die('البطاقة غير موجودة');
    }
    $cards = [$card];
}

// تحديد حجم الخط حسب عدد البطاقات
$cards_count = count($cards);
$font_size = '14px';
$table_font_size = '12px';
$header_font_size = '18px';
$padding = '8px';

if ($cards_count > 10) {
    $font_size = '11px';
    $table_font_size = '10px';
    $header_font_size = '14px';
    $padding = '6px';
} elseif ($cards_count > 5) {
    $font_size = '12px';
    $table_font_size = '11px';
    $header_font_size = '16px';
    $padding = '7px';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة بطاقة المخزون - <?php echo htmlspecialchars($card['card_number']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 15px;
            direction: rtl;
            font-size: <?php echo $font_size; ?>;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #2c5530;
            padding-bottom: 15px;
        }
        .header h1 {
            color: #2c5530;
            margin: 3px 0;
            font-size: <?php echo $header_font_size; ?>;
        }
        .header h2 {
            color: #4a7c59;
            margin: 3px 0;
            font-size: calc(<?php echo $header_font_size; ?> - 2px);
        }
        .header h3 {
            color: #666;
            margin: 3px 0;
            font-size: calc(<?php echo $header_font_size; ?> - 4px);
        }
        .card-title {
            text-align: center;
            font-size: calc(<?php echo $header_font_size; ?> + 4px);
            font-weight: bold;
            margin: 15px 0;
            color: #2c5530;
            border: 2px solid #2c5530;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .card-item {
            page-break-inside: avoid;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
        }
        .info-section {
            margin: 10px 0;
            padding: <?php echo $padding; ?>;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .info-section h5 {
            color: #2c5530;
            border-bottom: 1px solid #2c5530;
            padding-bottom: 5px;
            margin-bottom: 10px;
            font-size: calc(<?php echo $font_size; ?> + 2px);
        }
        .horizontal-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .horizontal-table th,
        .horizontal-table td {
            border: 1px solid #000;
            padding: <?php echo $padding; ?>;
            text-align: center;
            font-size: <?php echo $table_font_size; ?>;
        }
        .horizontal-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .entry-header {
            background-color: #d4edda !important;
            color: #155724;
        }
        .exit-header {
            background-color: #f8d7da !important;
            color: #721c24;
        }
        .stock-header {
            background-color: #fff3cd !important;
            color: #856404;
        }
        .field-row {
            display: flex;
            margin: 5px 0;
            align-items: center;
        }
        .field-label {
            font-weight: bold;
            width: 150px;
            color: #2c5530;
            font-size: <?php echo $font_size; ?>;
        }
        .field-value {
            flex: 1;
            padding: 4px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            font-size: <?php echo $font_size; ?>;
        }
        .receipt-field {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
            font-weight: bold;
        }
        @media print {
            body { margin: 5mm; font-size: <?php echo $table_font_size; ?>; }
            .no-print { display: none; }
            .card-item { margin-bottom: 15px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
        <h2><?php echo MINISTRY_NAME_AR; ?></h2>
        <h3><?php echo OFFICE_NAME_AR; ?></h3>
    </div>

    <div class="card-title">
        <?php if ($print_all): ?>
            بطاقات المخزون - جميع المواد (<?php echo $cards_count; ?> مادة)
        <?php else: ?>
            بطاقة المخزون
        <?php endif; ?>
    </div>

    <?php foreach ($cards as $index => $card): ?>
    
    <div class="card-item">
        <?php if ($print_all && $index > 0): ?>
            <hr style="border: 2px solid #2c5530; margin: 20px 0;">
        <?php endif; ?>

        <!-- المعلومات الأساسية -->
        <div class="info-section">
            <h5>المعلومات الأساسية <?php if ($print_all): ?>(<?php echo $index + 1; ?>/<?php echo $cards_count; ?>)<?php endif; ?></h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="field-row">
                        <div class="field-label">رقم البطاقة:</div>
                        <div class="field-value"><?php echo htmlspecialchars($card['card_number']); ?></div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="field-row">
                        <div class="field-label">اسم المادة:</div>
                        <div class="field-value"><?php echo htmlspecialchars($card['item_name']); ?></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="field-row">
                        <div class="field-label">وحدة القياس:</div>
                        <div class="field-value"><?php echo htmlspecialchars($card['unit_of_measure']); ?></div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="field-row">
                        <div class="field-label">الممون/المستفيد:</div>
                        <div class="field-value"><?php echo htmlspecialchars($card['supplier_beneficiary'] ?: '-'); ?></div>
                    </div>
                </div>
            </div>
            <!-- رقم الوصل - مع تمييز خاص -->
            <div class="row">
                <div class="col-md-12">
                    <div class="field-row">
                        <div class="field-label">رقم وصل الدخول/الخروج:</div>
                        <div class="field-value receipt-field">
                            <?php echo htmlspecialchars($card['entry_exit_receipt'] ?: 'لم يتم تحديد رقم الوصل'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- جدول الحركات الأفقي -->
        <div class="info-section">
            <h5>حركات المخزون</h5>
            <table class="horizontal-table">
                <thead>
                    <tr>
                        <th colspan="2" class="entry-header">الدخول</th>
                        <th colspan="2" class="exit-header">الخروج</th>
                        <th rowspan="2" class="stock-header">الكمية الموجودة في المخزن</th>
                    </tr>
                    <tr>
                        <th class="entry-header">التاريخ</th>
                        <th class="entry-header">الكمية</th>
                        <th class="exit-header">التاريخ</th>
                        <th class="exit-header">الكمية</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><?php echo $card['entry_date'] ? formatArabicDate($card['entry_date']) : '-'; ?></td>
                        <td><strong><?php echo number_format($card['entry_quantity'], 0); ?></strong></td>
                        <td><?php echo $card['exit_date'] ? formatArabicDate($card['exit_date']) : '-'; ?></td>
                        <td><strong><?php echo number_format($card['exit_quantity'], 0); ?></strong></td>
                        <td style="background-color: <?php echo $card['current_stock'] <= 0 ? '#f8d7da' : ($card['current_stock'] <= 10 ? '#fff3cd' : '#d4edda'); ?>;">
                            <strong style="font-size: calc(<?php echo $font_size; ?> + 2px);"><?php echo number_format($card['current_stock'], 0); ?></strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    
        <!-- الملاحظات -->
        <?php if ($card['notes']): ?>
        <div class="info-section">
            <h5>الملاحظات</h5>
            <div class="field-value" style="min-height: 40px;">
                <?php echo nl2br(htmlspecialchars($card['notes'])); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- معلومات إضافية -->
        <div class="info-section">
            <h5>معلومات النظام</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="field-row">
                        <div class="field-label">تاريخ الإنشاء:</div>
                        <div class="field-value"><?php echo formatArabicDate(date('Y-m-d', strtotime($card['created_at']))); ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="field-row">
                        <div class="field-label">آخر تحديث:</div>
                        <div class="field-value"><?php echo formatArabicDate(date('Y-m-d', strtotime($card['updated_at']))); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
    
    <!-- تذييل -->
    <div style="margin-top: 30px; text-align: center; border-top: 1px solid #ccc; padding-top: 15px; font-size: <?php echo $table_font_size; ?>; color: #666;">
        تم إنشاء هذا المستند تلقائياً من نظام تسيير المخزن - <?php echo date('Y-m-d H:i:s'); ?>
        <?php if ($print_all): ?>
            <br>إجمالي البطاقات المطبوعة: <?php echo $cards_count; ?> بطاقة
        <?php endif; ?>
    </div>

    <!-- أزرار التحكم -->
    <div class="text-center mt-4 no-print">
        <button onclick="window.print()" class="btn btn-primary btn-lg me-2">
            <i class="fas fa-print me-2"></i>طباعة
        </button>
        <button onclick="window.close()" class="btn btn-secondary btn-lg me-2">
            <i class="fas fa-times me-2"></i>إغلاق
        </button>
        <a href="inventory_cards_horizontal.php" class="btn btn-info btn-lg me-2">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
        <?php if (!$print_all): ?>
            <a href="?print_all=1" class="btn btn-warning btn-lg">
                <i class="fas fa-list me-2"></i>طباعة جميع البطاقات
            </a>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
