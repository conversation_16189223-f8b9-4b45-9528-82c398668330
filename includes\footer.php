    </div> <!-- نهاية المحتوى الرئيسي -->

    <!-- تذييل الصفحة -->
    <footer class="bg-light mt-5 py-4 no-print">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-muted"><?php echo OFFICE_NAME_AR; ?></h5>
                    <p class="text-muted small mb-0">
                        نظام تسيير المخزن وإدارة العملاء
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted small mb-0">
                        <i class="fas fa-calendar me-1"></i>
                        تم التطوير في عام <?php echo date('Y'); ?>
                    </p>
                    <p class="text-muted small mb-0">
                        <i class="fas fa-code me-1"></i>
                        تم التطوير باستخدام PHP & MySQL
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // تأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return Swal.fire({
                title: 'تأكيد الحذف',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            });
        }
        
        // عرض رسالة نجاح
        function showSuccess(message) {
            Swal.fire({
                title: 'تم بنجاح!',
                text: message,
                icon: 'success',
                confirmButtonText: 'موافق',
                timer: 3000,
                timerProgressBar: true
            });
        }
        
        // عرض رسالة خطأ
        function showError(message) {
            Swal.fire({
                title: 'خطأ!',
                text: message,
                icon: 'error',
                confirmButtonText: 'موافق'
            });
        }

        // عرض رسالة تحذير
        function showWarning(message) {
            Swal.fire({
                title: 'تحذير!',
                text: message,
                icon: 'warning',
                confirmButtonText: 'موافق'
            });
        }

        // دالة عرض مؤشر التحميل
        function showLoading(message = 'جاري التحميل...') {
            Swal.fire({
                title: message,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }

        // دالة إخفاء مؤشر التحميل
        function hideLoading() {
            Swal.close();
        }
        
        // طباعة الصفحة
        function printPage() {
            window.print();
        }
        
        // تصدير إلى PDF
        function exportToPDF() {
            // سيتم تطوير هذه الوظيفة لاحقاً
            showError('وظيفة التصدير إلى PDF قيد التطوير');
        }
        
        // إرسال عبر البريد الإلكتروني
        function sendEmail(type = 'gmail') {
            const subject = encodeURIComponent(document.title);
            const body = encodeURIComponent('مرفق مستند من نظام تسيير المخزن');
            
            let emailUrl = '';
            switch(type) {
                case 'gmail':
                    emailUrl = `https://mail.google.com/mail/?view=cm&fs=1&su=${subject}&body=${body}`;
                    break;
                case 'outlook':
                    emailUrl = `https://outlook.live.com/mail/0/deeplink/compose?subject=${subject}&body=${body}`;
                    break;
                case 'yahoo':
                    emailUrl = `https://compose.mail.yahoo.com/?subject=${subject}&body=${body}`;
                    break;
            }
            
            if (emailUrl) {
                window.open(emailUrl, '_blank');
            }
        }
        
        // إرسال عبر WhatsApp
        function sendWhatsApp() {
            const text = encodeURIComponent('مستند من نظام تسيير المخزن - ' + document.title);
            const whatsappUrl = `https://wa.me/?text=${text}`;
            window.open(whatsappUrl, '_blank');
        }
        
        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Africa/Algiers'
            };
            
            const timeString = now.toLocaleDateString('ar-DZ', options);
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }
        
        // تحديث الوقت كل ثانية
        setInterval(updateCurrentTime, 1000);
        
        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تفعيل tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // تفعيل popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // إضافة تأثيرات للأزرار
            document.querySelectorAll('.btn').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    let ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);
                    
                    let x = e.clientX - e.target.offsetLeft;
                    let y = e.clientY - e.target.offsetTop;
                    
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
            
            // التحقق من صحة النماذج
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                        showError('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
                    }
                    form.classList.add('was-validated');
                });
            });
        });
        
        // حفظ البيانات تلقائياً
        function autoSave() {
            // سيتم تطوير هذه الوظيفة لاحقاً
            console.log('Auto-save triggered');
        }
        
        // حفظ البيانات كل 5 دقائق
        setInterval(autoSave, 300000);
    </script>
    
    <!-- CSS إضافي للتأثيرات -->
    <style>
        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .btn {
            position: relative;
            overflow: hidden;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .loading {
            pointer-events: none;
            opacity: 0.6;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</body>
</html>
