<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';

if (empty($id)) {
    echo '<div class="alert alert-danger">معرف المادة مطلوب</div>';
    exit;
}

// جلب بيانات المادة
$stmt = $db->prepare("SELECT * FROM warehouse_supplies WHERE id = ?");
$stmt->execute([$id]);
$supply = $stmt->fetch();

if (!$supply) {
    echo '<div class="alert alert-danger">المادة غير موجودة</div>';
    exit;
}

// ألوان الفئات
$category_colors = [
    'لوازم الورق' => 'primary',
    'مستهلكات الطبع والنسخ' => 'success',
    'اللوازم المكتبية' => 'info',
    'اللوازم الصيدلانية والوقائية' => 'warning',
    'لوازم السيارات ولواحقها' => 'danger',
    'الأثاث' => 'dark',
    'خاص بمتفوقي الامتحانات المدرسية' => 'purple'
];

$color_class = $category_colors[$supply['category']] ?? 'secondary';
?>

<div class="container-fluid">
    <!-- رأس المؤسسة -->
    <div class="text-center mb-4 border-bottom pb-3">
        <h4 class="text-primary"><?php echo INSTITUTION_NAME_AR; ?></h4>
        <h5 class="text-secondary"><?php echo MINISTRY_NAME_AR; ?></h5>
        <h6 class="text-muted"><?php echo OFFICE_NAME_AR; ?></h6>
    </div>
    
    <!-- عنوان البطاقة -->
    <div class="text-center mb-4">
        <h3 class="bg-light p-3 rounded border">
            <i class="fas fa-warehouse me-2"></i>
            بطاقة مادة من المخزن
        </h3>
    </div>
    
    <!-- معلومات المادة -->
    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold text-primary" width="40%">اسم المادة:</td>
                            <td><?php echo htmlspecialchars($supply['item_name']); ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-primary">رقم المادة:</td>
                            <td>
                                <span class="badge bg-info text-dark fs-6">
                                    <?php echo htmlspecialchars($supply['item_number']); ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-primary">الرقم القديم:</td>
                            <td>
                                <?php if ($supply['old_item_number']): ?>
                                    <span class="badge bg-secondary">
                                        <?php echo htmlspecialchars($supply['old_item_number']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-primary">الفئة:</td>
                            <td>
                                <span class="badge bg-<?php echo $color_class; ?> text-wrap">
                                    <?php echo htmlspecialchars($supply['category']); ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        الملاحظات والتفاصيل
                    </h6>
                </div>
                <div class="card-body">
                    <?php if ($supply['notes']): ?>
                        <div class="alert alert-light">
                            <i class="fas fa-comment me-2"></i>
                            <?php echo nl2br(htmlspecialchars($supply['notes'])); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-comment-slash fa-2x mb-2"></i>
                            <p>لا توجد ملاحظات مسجلة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معلومات النظام -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        معلومات النظام
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td class="fw-bold text-info" width="40%">تاريخ الإضافة:</td>
                                    <td><?php echo formatArabicDate(date('Y-m-d', strtotime($supply['created_at']))); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-info">وقت الإضافة:</td>
                                    <td><?php echo date('H:i:s', strtotime($supply['created_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td class="fw-bold text-info" width="40%">آخر تحديث:</td>
                                    <td><?php echo formatArabicDate(date('Y-m-d', strtotime($supply['updated_at']))); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-info">وقت التحديث:</td>
                                    <td><?php echo date('H:i:s', strtotime($supply['updated_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تذييل -->
    <div class="text-center mt-4 pt-3 border-top">
        <small class="text-muted">
            تم إنشاء هذا المستند تلقائياً من نظام إدارة المخزن - <?php echo date('Y-m-d H:i:s'); ?>
        </small>
    </div>
</div>

<style>
.bg-purple {
    background-color: #6f42c1 !important;
}
</style>
