<?php
require_once 'config/config.php';
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تحديث قاعدة البيانات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; }</style>";
echo "</head>";
echo "<body>";
echo "<div class='container my-5'>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-database'></i> تحديث قاعدة البيانات - إضافة أعمدة التنسيق</h3>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<div class='alert alert-info'>";
    echo "<h5>بدء عملية التحديث...</h5>";
    echo "</div>";

    // التحقق من وجود الجدول
    $stmt = $db->query("SHOW TABLES LIKE 'office_supplies_requests'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("جدول office_supplies_requests غير موجود!");
    }
    
    echo "<div class='alert alert-success'>";
    echo "✅ جدول office_supplies_requests موجود";
    echo "</div>";

    // التحقق من الأعمدة الموجودة
    $stmt = $db->query("DESCRIBE office_supplies_requests");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='alert alert-info'>";
    echo "<h6>الأعمدة الموجودة حالياً:</h6>";
    echo "<ul>";
    foreach ($existing_columns as $column) {
        echo "<li>$column</li>";
    }
    echo "</ul>";
    echo "</div>";

    // إضافة الأعمدة الجديدة
    $columns_to_add = [
        'bold_text' => "TINYINT(1) DEFAULT 0 COMMENT 'نص عريض (0=عادي، 1=عريض)'",
        'text_align' => "VARCHAR(10) DEFAULT 'right' COMMENT 'محاذاة النص (right, center, left)'",
        'font_color' => "VARCHAR(20) DEFAULT 'black' COMMENT 'لون الخط (black, blue, red, green, brown, purple)'"
    ];

    foreach ($columns_to_add as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            $sql = "ALTER TABLE office_supplies_requests ADD COLUMN $column_name $column_definition";
            $db->exec($sql);
            echo "<div class='alert alert-success'>";
            echo "✅ تم إضافة العمود: <strong>$column_name</strong>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-warning'>";
            echo "⚠️ العمود <strong>$column_name</strong> موجود بالفعل";
            echo "</div>";
        }
    }

    // التحقق من النتيجة النهائية
    $stmt = $db->query("DESCRIBE office_supplies_requests");
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='alert alert-success'>";
    echo "<h5>✅ تم التحديث بنجاح!</h5>";
    echo "<h6>هيكل الجدول النهائي:</h6>";
    echo "<table class='table table-sm table-bordered'>";
    echo "<thead><tr><th>اسم العمود</th><th>النوع</th><th>افتراضي</th><th>تعليق</th></tr></thead>";
    echo "<tbody>";
    foreach ($final_columns as $column) {
        $highlight = in_array($column['Field'], ['bold_text', 'text_align', 'font_color']) ? 'table-success' : '';
        echo "<tr class='$highlight'>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>" . (isset($column['Comment']) ? $column['Comment'] : '') . "</td>";
        echo "</tr>";
    }
    echo "</tbody>";
    echo "</table>";
    echo "</div>";

    // اختبار إدراج بيانات تجريبية
    echo "<div class='alert alert-info'>";
    echo "<h6>اختبار الأعمدة الجديدة:</h6>";
    
    $test_sql = "SELECT COUNT(*) as total FROM office_supplies_requests";
    $stmt = $db->query($test_sql);
    $count = $stmt->fetch()['total'];
    
    if ($count > 0) {
        // تحديث السجلات الموجودة بقيم افتراضية
        $update_sql = "UPDATE office_supplies_requests 
                      SET bold_text = COALESCE(bold_text, 0),
                          text_align = COALESCE(text_align, 'right'),
                          font_color = COALESCE(font_color, 'black')
                      WHERE bold_text IS NULL OR text_align IS NULL OR font_color IS NULL";
        $db->exec($update_sql);
        echo "✅ تم تحديث $count سجل موجود بالقيم الافتراضية";
    } else {
        echo "ℹ️ لا توجد سجلات في الجدول حالياً";
    }
    echo "</div>";

    echo "<div class='alert alert-success'>";
    echo "<h4>🎉 تم التحديث بنجاح!</h4>";
    echo "<p>يمكنك الآن استخدام نظام اللوازم المكتبية مع خيارات التنسيق الجديدة.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ خطأ في التحديث:</h5>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الحل:</strong> تأكد من صحة إعدادات قاعدة البيانات في ملف config.php</p>";
    echo "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='office_supplies.php' class='btn btn-primary btn-lg me-2'>";
echo "<i class='fas fa-external-link-alt me-2'></i>فتح نظام اللوازم المكتبية";
echo "</a>";
echo "<a href='test_office_supplies_final.html' class='btn btn-success btn-lg'>";
echo "<i class='fas fa-test me-2'></i>اختبار النظام";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
