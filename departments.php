<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'إدارة المديريات والمصالح والمكاتب';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// أنواع الوحدات الإدارية
$unit_types = ['مديرية', 'مصلحة', 'مكتب', 'خلية'];

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitizeInput($_POST['name']);
    $type = sanitizeInput($_POST['type']);
    $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
    $is_active = isset($_POST['is_active']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE departments_services_offices SET 
                    name = ?, type = ?, parent_id = ?, is_active = ?
                    WHERE id = ?");
                
                $stmt->execute([$name, $type, $parent_id, $is_active, $_POST['id']]);
                
                $success_message = 'تم تحديث الوحدة الإدارية بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO departments_services_offices 
                    (name, type, parent_id, is_active) 
                    VALUES (?, ?, ?, ?)");
                
                $stmt->execute([$name, $type, $parent_id, $is_active]);
                
                $success_message = 'تم إضافة الوحدة الإدارية بنجاح';
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        // التحقق من وجود وحدات فرعية
        $stmt = $db->prepare("SELECT COUNT(*) FROM departments_services_offices WHERE parent_id = ?");
        $stmt->execute([$id]);
        $child_count = $stmt->fetchColumn();
        
        if ($child_count > 0) {
            $error_message = 'لا يمكن حذف هذه الوحدة لأنها تحتوي على وحدات فرعية';
        } else {
            $stmt = $db->prepare("DELETE FROM departments_services_offices WHERE id = ?");
            $stmt->execute([$id]);
            $success_message = 'تم حذف الوحدة الإدارية بنجاح';
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT dso.*, parent.name as parent_name 
                         FROM departments_services_offices dso 
                         LEFT JOIN departments_services_offices parent ON dso.parent_id = parent.id 
                         WHERE dso.id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب قائمة الوحدات الأساسية للاختيار كوحدة أب
$stmt = $db->query("SELECT id, name, type FROM departments_services_offices 
                   WHERE type IN ('مديرية', 'مصلحة') AND is_active = 1 
                   ORDER BY type, name ASC");
$parent_units = $stmt->fetchAll();

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE dso.name LIKE ? OR dso.type LIKE ?";
    $params = ["%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT dso.*, parent.name as parent_name, parent.type as parent_type,
                     (SELECT COUNT(*) FROM departments_services_offices WHERE parent_id = dso.id) as child_count
                     FROM departments_services_offices dso 
                     LEFT JOIN departments_services_offices parent ON dso.parent_id = parent.id 
                     $where_clause 
                     ORDER BY dso.type, dso.name ASC");
$stmt->execute($params);
$departments = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-sitemap me-2"></i>
                    إدارة المديريات والمصالح والمكاتب
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate>
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الوحدة الإدارية *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo $edit_data['name'] ?? ''; ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم الوحدة الإدارية</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">نوع الوحدة *</label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">اختر نوع الوحدة</option>
                                <?php foreach ($unit_types as $unit_type): ?>
                                    <option value="<?php echo $unit_type; ?>" 
                                            <?php echo ($edit_data['type'] ?? '') === $unit_type ? 'selected' : ''; ?>>
                                        <?php echo $unit_type; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار نوع الوحدة</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="parent_id" class="form-label">الوحدة الأب (اختياري)</label>
                            <select class="form-select" id="parent_id" name="parent_id">
                                <option value="">لا توجد وحدة أب</option>
                                <?php 
                                $current_type = '';
                                foreach ($parent_units as $unit): 
                                    if ($unit['type'] !== $current_type) {
                                        if ($current_type !== '') echo '</optgroup>';
                                        echo '<optgroup label="' . $unit['type'] . '">';
                                        $current_type = $unit['type'];
                                    }
                                ?>
                                    <option value="<?php echo $unit['id']; ?>" 
                                            <?php echo ($edit_data['parent_id'] ?? '') == $unit['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($unit['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                                <?php if ($current_type !== '') echo '</optgroup>'; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3 d-flex align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?php echo ($edit_data['is_active'] ?? true) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">
                                    الوحدة نشطة
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="departments.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-info" onclick="showOrganizationChart()">
                                    <i class="fas fa-sitemap me-1"></i> الهيكل التنظيمي
                                </button>
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الوحدات الإدارية
                </h5>
                
                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2" 
                           placeholder="بحث بالاسم أو النوع..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="departments.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <?php
                    $stats = [];
                    foreach ($unit_types as $type) {
                        $count = count(array_filter($departments, function($dept) use ($type) {
                            return $dept['type'] === $type && $dept['is_active'];
                        }));
                        $stats[$type] = $count;
                    }
                    ?>
                    <?php foreach ($stats as $type => $count): ?>
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5><?php echo $count; ?></h5>
                                    <small><?php echo $type; ?></small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>النوع</th>
                                <th>الوحدة الأب</th>
                                <th>الوحدات الفرعية</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($departments)): ?>
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($departments as $dept): ?>
                                    <tr class="<?php echo !$dept['is_active'] ? 'table-secondary' : ''; ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($dept['name']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $dept['type']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($dept['parent_name']): ?>
                                                <small class="text-muted">
                                                    <span class="badge bg-secondary"><?php echo $dept['parent_type']; ?></span><br>
                                                    <?php echo htmlspecialchars($dept['parent_name']); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($dept['child_count'] > 0): ?>
                                                <span class="badge bg-success"><?php echo $dept['child_count']; ?> وحدة</span>
                                            <?php else: ?>
                                                <span class="text-muted">لا توجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($dept['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatArabicDate($dept['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $dept['id']; ?>" 
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($dept['child_count'] == 0): ?>
                                                    <button onclick="deleteDepartment(<?php echo $dept['id']; ?>)" 
                                                            class="btn btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-outline-secondary" disabled title="لا يمكن الحذف - توجد وحدات فرعية">
                                                        <i class="fas fa-ban"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button onclick="toggleStatus(<?php echo $dept['id']; ?>, <?php echo $dept['is_active'] ? 'false' : 'true'; ?>)" 
                                                        class="btn btn-outline-warning" title="تغيير الحالة">
                                                    <i class="fas fa-toggle-<?php echo $dept['is_active'] ? 'on' : 'off'; ?>"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حذف الوحدة الإدارية
function deleteDepartment(id) {
    confirmDelete('هل أنت متأكد من حذف هذه الوحدة الإدارية؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// تغيير حالة الوحدة
function toggleStatus(id, newStatus) {
    const statusText = newStatus === 'true' ? 'تفعيل' : 'إلغاء تفعيل';

    confirmAction(`هل أنت متأكد من ${statusText} هذه الوحدة؟`).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب AJAX لتغيير الحالة
            fetch('api/toggle_department_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: id,
                    is_active: newStatus === 'true'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('تم تغيير حالة الوحدة بنجاح');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showError(data.message || 'خطأ في تغيير الحالة');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showError('خطأ في الاتصال بالخادم');
            });
        }
    });
}

// عرض الهيكل التنظيمي
function showOrganizationChart() {
    fetch('api/get_organization_chart.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayOrganizationChart(data.chart);
            } else {
                showError('خطأ في جلب الهيكل التنظيمي');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showError('خطأ في الاتصال بالخادم');
        });
}

// عرض الهيكل التنظيمي
function displayOrganizationChart(chartData) {
    let chartHtml = '<div class="organization-chart">';

    function buildNode(node, level = 0) {
        const indent = '&nbsp;'.repeat(level * 4);
        const icon = getTypeIcon(node.type);
        const statusClass = node.is_active ? 'text-success' : 'text-muted';

        let html = `
            <div class="org-node level-${level} ${statusClass}" style="margin-bottom: 5px;">
                ${indent}${icon} <strong>${node.name}</strong>
                <span class="badge bg-info">${node.type}</span>
                ${!node.is_active ? '<span class="badge bg-secondary">غير نشط</span>' : ''}
            </div>
        `;

        if (node.children && node.children.length > 0) {
            node.children.forEach(child => {
                html += buildNode(child, level + 1);
            });
        }

        return html;
    }

    chartData.forEach(rootNode => {
        chartHtml += buildNode(rootNode);
    });

    chartHtml += '</div>';

    Swal.fire({
        title: 'الهيكل التنظيمي',
        html: chartHtml,
        width: '800px',
        showConfirmButton: true,
        confirmButtonText: 'إغلاق',
        customClass: {
            htmlContainer: 'text-start'
        }
    });
}

// الحصول على أيقونة حسب النوع
function getTypeIcon(type) {
    const icons = {
        'مديرية': '<i class="fas fa-building text-primary"></i>',
        'مصلحة': '<i class="fas fa-users text-success"></i>',
        'مكتب': '<i class="fas fa-door-open text-info"></i>',
        'خلية': '<i class="fas fa-circle text-warning"></i>'
    };

    return icons[type] || '<i class="fas fa-folder"></i>';
}

// تحديث قائمة الوحدات الأب عند تغيير النوع
document.getElementById('type').addEventListener('change', function() {
    const selectedType = this.value;
    const parentSelect = document.getElementById('parent_id');

    // إخفاء/إظهار قائمة الوحدات الأب حسب النوع
    if (selectedType === 'مديرية') {
        parentSelect.disabled = true;
        parentSelect.value = '';
    } else {
        parentSelect.disabled = false;
    }
});

// تطبيق القاعدة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    if (typeSelect.value === 'مديرية') {
        document.getElementById('parent_id').disabled = true;
    }
});

// إحصائيات متقدمة
function showAdvancedStats() {
    fetch('api/get_department_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let statsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إحصائيات عامة:</h6>
                            <ul class="list-unstyled">
                                <li><strong>إجمالي الوحدات:</strong> ${data.stats.total}</li>
                                <li><strong>الوحدات النشطة:</strong> ${data.stats.active}</li>
                                <li><strong>الوحدات غير النشطة:</strong> ${data.stats.inactive}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>حسب النوع:</h6>
                            <ul class="list-unstyled">
                `;

                Object.entries(data.stats.by_type).forEach(([type, count]) => {
                    statsHtml += `<li><strong>${type}:</strong> ${count}</li>`;
                });

                statsHtml += `
                            </ul>
                        </div>
                    </div>
                `;

                Swal.fire({
                    title: 'إحصائيات مفصلة',
                    html: statsHtml,
                    icon: 'info',
                    confirmButtonText: 'موافق'
                });
            } else {
                showError('خطأ في جلب الإحصائيات');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showError('خطأ في الاتصال بالخادم');
        });
}

// إضافة زر الإحصائيات المتقدمة
document.querySelector('.card-header h5').insertAdjacentHTML('afterend', `
    <button type="button" class="btn btn-sm btn-outline-info mt-2" onclick="showAdvancedStats()">
        <i class="fas fa-chart-pie me-1"></i> إحصائيات مفصلة
    </button>
`);

// تصدير الهيكل التنظيمي
function exportOrganizationChart() {
    window.open('export/organization_chart.php', '_blank');
}

// إضافة زر التصدير
document.querySelector('.btn-group[role="group"]').insertAdjacentHTML('beforeend', `
    <button type="button" class="btn btn-info" onclick="exportOrganizationChart()">
        <i class="fas fa-download me-1"></i> تصدير الهيكل
    </button>
`);
</script>

<style>
.organization-chart {
    font-family: 'Noto Sans Arabic', sans-serif;
    line-height: 1.8;
}

.org-node {
    padding: 2px 0;
    border-left: 2px solid #e9ecef;
    margin-left: 10px;
}

.org-node.level-0 {
    border-left: none;
    margin-left: 0;
    font-size: 1.1em;
}

.org-node.level-1 {
    border-left-color: #007bff;
}

.org-node.level-2 {
    border-left-color: #28a745;
}

.org-node.level-3 {
    border-left-color: #ffc107;
}
</style>

<?php include 'includes/footer.php'; ?>
