<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$font_size = $_GET['font_size'] ?? '14';

if (empty($id)) {
    die('معرف الطلب مطلوب');
}

// جلب بيانات الطلب
$stmt = $db->prepare("SELECT * FROM office_supplies_requests WHERE id = ?");
$stmt->execute([$id]);
$request = $stmt->fetch();

if (!$request) {
    die('الطلب غير موجود');
}

// فك تشفير المواد
$materials = json_decode($request['materials'], true) ?? [];

// أحجام الخط المتاحة مع النص العريض
$available_sizes = ['12BOLD', '14BOLD', '16BOLD', '18BOLD', '20BOLD'];
if (!in_array($font_size, $available_sizes)) {
    $font_size = '14BOLD';
}

// استخراج الحجم الرقمي
$numeric_size = (int) str_replace('BOLD', '', $font_size);

// حساب الأحجام المختلفة
$base_size = $numeric_size . 'px';
$header_size = ($numeric_size + 4) . 'px';
$title_size = ($numeric_size + 6) . 'px';
$table_size = ($numeric_size - 1) . 'px';
$small_size = ($numeric_size - 2) . 'px';

// خيارات التنسيق - النص عريض دائماً
$bold_text = true; // دائماً عريض
$text_align = $request['text_align'] ?? 'right';
$font_color = $request['font_color'] ?? 'black';

// تحويل الألوان
$color_map = [
    'black' => '#000000',
    'blue' => '#0066cc',
    'red' => '#cc0000',
    'green' => '#006600',
    'brown' => '#8B4513',
    'purple' => '#800080'
];
$text_color = $color_map[$font_color] ?? '#000000';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة طلب اللوازم المكتبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 15px;
            direction: rtl;
            font-size: <?php echo $base_size; ?>;
            line-height: 1.3;
            color: <?php echo $text_color; ?>;
            font-weight: bold; /* النص عريض دائماً */
        }
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 3px solid #000000 !important;
            padding-bottom: 15px;
            background-color: white !important;
        }
        .header h1 {
            color: #000000 !important;
            margin: 8px 0;
            font-size: <?php echo ($numeric_size + 6) . 'px'; ?>;
            font-weight: 900 !important; /* أقصى درجة سُمك */
            font-family: 'Arial Black', Arial, sans-serif !important;
            text-shadow: 1px 1px 0px #000000 !important;
            letter-spacing: 1px;
        }
        .header h2 {
            color: #000000 !important;
            margin: 8px 0;
            font-size: <?php echo ($numeric_size + 2) . 'px'; ?>;
            font-weight: 900 !important; /* أقصى درجة سُمك */
            font-family: 'Arial Black', Arial, sans-serif !important;
            text-shadow: 1px 1px 0px #000000 !important;
            letter-spacing: 0.5px;
        }
        .header h3 {
            color: #000000 !important;
            margin: 8px 0;
            font-size: <?php echo $numeric_size . 'px'; ?>;
            font-weight: 900 !important; /* أقصى درجة سُمك */
            font-family: 'Arial Black', Arial, sans-serif !important;
            text-shadow: 1px 1px 0px #000000 !important;
            letter-spacing: 0.5px;
        }
        .form-title {
            text-align: center;
            font-size: <?php echo $title_size; ?>;
            font-weight: bold;
            margin: 10px 0;
            color: #2c5530;
            border: 2px solid #2c5530;
            padding: 8px;
            background-color: #f8f9fa;
        }
        .info-section {
            margin: 8px 0;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .info-row {
            display: flex;
            margin: 4px 0;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            width: 140px;
            color: #2c5530;
            font-size: <?php echo $small_size; ?>;
        }
        .info-value {
            flex: 1;
            padding: 3px;
            border-bottom: 1px solid #ddd;
            font-size: <?php echo $small_size; ?>;
        }
        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 8px 0;
        }
        .materials-table th,
        .materials-table td {
            border: 1px solid #000;
            padding: 4px;
            text-align: center;
            font-size: <?php echo $small_size; ?>;
            vertical-align: middle;
        }
        .materials-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .materials-table td.item-name {
            text-align: <?php echo $text_align; ?>;
            font-weight: bold; /* النص عريض دائماً */
        }
        .materials-table td.notes {
            text-align: <?php echo $text_align; ?>;
            font-size: <?php echo $small_size; ?>;
            font-weight: bold; /* النص عريض للملاحظات */
        }
        .signatures-section {
            margin-top: 10px; /* تقليل المسافة بين الجدول والتوقيعات */
            page-break-inside: avoid;
        }
        .signatures-title {
            text-align: center;
            font-size: <?php echo $small_size; ?>;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c5530;
        }
        .signature-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 100px; /* زيادة المسافة بين الصفوف بشكل كبير */
            padding: 40px 0; /* زيادة الحشو بشكل كبير */
            width: 100%;
        }
        .signature-box {
            width: 35%; /* تقليل العرض لزيادة المسافة */
            border: none;
            padding: 80px 20px; /* زيادة المسافة للإمضاءات بشكل كبير */
            min-height: 150px; /* زيادة الارتفاع بشكل كبير */
            display: flex;
            align-items: center;
            font-size: <?php echo $small_size; ?>;
            font-weight: bold;
            background-color: transparent;
            margin: 40px 0; /* زيادة المسافة بين الصناديق بشكل كبير */
        }
        .signature-box.right-align {
            justify-content: flex-end; /* محاذاة أقصى اليمين */
            text-align: right;
        }
        .signature-box.left-align {
            justify-content: flex-start; /* محاذاة أقصى اليسار */
            text-align: left;
        }
        @media print {
            body {
                margin: 5mm;
                font-size: <?php echo ($numeric_size - 2) . 'px'; ?>;
                line-height: 1.2;
                font-weight: bold; /* النص عريض للطباعة */
            }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
            .header {
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 2px solid #000000 !important;
            }
            .header h1, .header h2, .header h3 {
                color: #000000 !important;
                font-weight: 900 !important;
                font-family: 'Arial Black', Arial, sans-serif !important;
                text-shadow: none !important; /* إزالة الظل في الطباعة */
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .form-title { margin: 5px 0; padding: 5px; }
            .info-section { margin: 4px 0; padding: 3px; }
            .materials-table th, .materials-table td {
                padding: 2px;
                font-size: <?php echo ($numeric_size - 3) . 'px'; ?>;
                font-weight: bold; /* النص عريض للجدول */
            }
            .signature-box {
                padding: 60px 15px; /* زيادة المسافة للطباعة بشكل كبير */
                min-height: 120px; /* زيادة الارتفاع للطباعة بشكل كبير */
                font-size: <?php echo ($numeric_size - 3) . 'px'; ?>;
                margin: 30px 0; /* زيادة المسافة بشكل كبير */
                width: 30%; /* تقليل العرض لزيادة المسافة */
            }
            .signature-box.right-align {
                justify-content: flex-end !important;
                text-align: right !important;
            }
            .signature-box.left-align {
                justify-content: flex-start !important;
                text-align: left !important;
            }
            .signatures-section { margin-top: 15px; } /* تقليل المسافة بين الجدول والتوقيعات */
            .signature-row {
                margin-bottom: 80px; /* زيادة المسافة بين الصفوف بشكل كبير */
                padding: 30px 0; /* زيادة الحشو بشكل كبير */
                justify-content: space-between; /* توزيع المسافة */
            }
        }
        .footer {
            margin-top: 15px;
            text-align: center;
            border-top: 1px solid #ccc;
            padding-top: 8px;
            font-size: <?php echo ($font_size - 4) . 'px'; ?>;
            color: #666;
        }
        .font-size-selector {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- اختيار حجم الخط -->
    <div class="font-size-selector no-print">
        <label for="fontSizeSelect" class="form-label">حجم الخط:</label>
        <select id="fontSizeSelect" class="form-select form-select-sm" onchange="changeFontSize(this.value)">
            <option value="12BOLD" <?php echo $font_size == '12BOLD' ? 'selected' : ''; ?>>12px BOLD</option>
            <option value="14BOLD" <?php echo $font_size == '14BOLD' ? 'selected' : ''; ?>>14px BOLD</option>
            <option value="16BOLD" <?php echo $font_size == '16BOLD' ? 'selected' : ''; ?>>16px BOLD</option>
            <option value="18BOLD" <?php echo $font_size == '18BOLD' ? 'selected' : ''; ?>>18px BOLD</option>
            <option value="20BOLD" <?php echo $font_size == '20BOLD' ? 'selected' : ''; ?>>20px BOLD</option>
        </select>
    </div>

    <div class="header">
        <h1 style="font-weight: bold; color: #000;">الجمهورية الجزائرية الديمقراطية الشعبية</h1>
        <h2 style="font-weight: bold; color: #000;">وزارة التربية الوطنية</h2>
        <h3 style="font-weight: bold; color: #000;">الديوان الوطني للامتحانات والمسابقات</h3>
    </div>

    <div class="form-title">
        طلب واستلام اللوازم المكتبية
    </div>
    
    <!-- معلومات الطلب -->
    <div class="info-section">
        <div class="row">
            <div class="col-md-6">
                <div class="info-row">
                    <div class="info-label">رقم الوصل:</div>
                    <div class="info-value"><?php echo htmlspecialchars($request['receipt_number']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">التاريخ:</div>
                    <div class="info-value"><?php echo formatArabicDate($request['request_date']); ?></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-row">
                    <div class="info-label">المديرية أو المصلحة:</div>
                    <div class="info-value"><?php echo htmlspecialchars($request['beneficiary_directorate']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">اسم المستلم:</div>
                    <div class="info-value"><?php echo htmlspecialchars($request['recipient_name']); ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- جدول المواد -->
    <table class="materials-table">
        <thead>
            <tr>
                <th width="6%">الرقم</th>
                <th width="40%">اسم المادة</th>
                <th width="10%">الكمية</th>
                <th width="12%">رقم اللوازم</th>
                <th width="32%">الملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($materials)): ?>
                <tr>
                    <td colspan="5">لا توجد مواد مسجلة</td>
                </tr>
            <?php else: ?>
                <?php foreach ($materials as $index => $material): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td class="item-name">
                            <?php echo htmlspecialchars($material['item_name']); ?>
                        </td>
                        <td>
                            <strong><?php echo number_format($material['quantity'], 0); ?></strong>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($material['supply_number'] ?: '-'); ?>
                        </td>
                        <td class="notes">
                            <?php echo htmlspecialchars($material['notes'] ?? '-'); ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    
    <!-- الملاحظات العامة -->
    <?php if ($request['notes']): ?>
        <div class="info-section">
            <div class="info-row">
                <div class="info-label">الملاحظات العامة:</div>
                <div class="info-value"><?php echo nl2br(htmlspecialchars($request['notes'])); ?></div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- التوقيعات المختارة فقط -->
    <?php
    $selected_signatures = [];

    // التحقق من وجود أعمدة التوقيعات
    $has_signature_columns = isset($request['signature_means_chief']) ||
                            isset($request['signature_sub_director']) ||
                            isset($request['signature_warehouse_manager']) ||
                            isset($request['signature_beneficiary']);

    // ترتيب التوقيعات حسب المطلوب
    // الصف الأول: رئيس مصلحة الوسائل (يسار) - المدير الفرعي (يمين)
    // الصف الثاني: المكلف بتسيير المخزن (يسار) - المستفيد (يمين)

    if ($has_signature_columns) {
        // استخدام التوقيعات المحفوظة مع الترتيب المطلوب
        $signatures_ordered = [];

        // الصف الأول
        if (!empty($request['signature_means_chief'])) $signatures_ordered[] = 'إمضاء وختم رئيس مصلحة الوسائل';
        if (!empty($request['signature_sub_director'])) $signatures_ordered[] = 'إمضاء وختم المدير الفرعي للإدارة العامة';

        // الصف الثاني
        if (!empty($request['signature_warehouse_manager'])) $signatures_ordered[] = 'إمضاء وختم المكلف بتسيير المخزن';
        if (!empty($request['signature_beneficiary'])) $signatures_ordered[] = 'إمضاء وختم المستفيد';

        $selected_signatures = $signatures_ordered;
    } else {
        // عرض التوقيعات الافتراضية بالترتيب المطلوب
        $selected_signatures = [
            'إمضاء وختم رئيس مصلحة الوسائل',
            'إمضاء وختم المدير الفرعي للإدارة العامة',
            'إمضاء وختم المكلف بتسيير المخزن',
            'إمضاء وختم المستفيد'
        ];
    }

    if (!empty($selected_signatures)): ?>
    <div class="signatures-section">
        <div class="signatures-title">
            التوقيعات والأختام
            <?php if (!$has_signature_columns): ?>
                <br><small style="color: #dc3545; font-size: 10px;">(عرض افتراضي - يرجى تحديث قاعدة البيانات)</small>
            <?php endif; ?>
        </div>

        <?php
        // تقسيم التوقيعات إلى صفوف (2 في كل صف)
        $signature_rows = array_chunk($selected_signatures, 2);
        foreach ($signature_rows as $row_index => $row): ?>
        <div class="signature-row">
            <?php
            // تحديد محاذاة التوقيعات - الأول يمين والثاني يسار
            foreach ($row as $index => $signature):
                $align_class = ($index == 0) ? 'right-align' : 'left-align';
            ?>
                <div class="signature-box <?php echo $align_class; ?>">
                    <?php echo $signature; ?>
                </div>

                <?php if ($index == 0 && count($row) > 1): ?>
                    <!-- مسافة كبيرة بين التوقيعات في نفس الصف -->
                    <div style="width: 30%; min-width: 250px;"></div>
                <?php endif; ?>
            <?php endforeach; ?>

            <?php if (count($row) == 1): ?>
                <!-- إضافة مربع فارغ إذا كان هناك توقيع واحد فقط في الصف -->
                <div style="width: 30%; min-width: 250px;"></div>
                <div class="signature-box left-align" style="border: none;"></div>
            <?php endif; ?>
        </div>

        <?php if ($row_index < count($signature_rows) - 1): ?>
            <!-- مسافة إضافية طويلة بين الصفوف للإمضاءات والتواقيع -->
            <div style="height: 60px; margin: 30px 0; border-bottom: 1px dotted #ccc;"></div>
        <?php endif; ?>

        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    

    
    <!-- أزرار التحكم -->
    <div class="text-center mt-4 no-print">
        <div class="btn-group mb-3" role="group">
            <button onclick="window.print()" class="btn btn-primary btn-lg">
                <i class="fas fa-print me-2"></i>طباعة
            </button>
            <a href="export_office_supplies_pdf.php?id=<?php echo $id; ?>&font_size=<?php echo $font_size; ?>"
               class="btn btn-danger btn-lg" target="_blank">
                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
            </a>
            <a href="export_office_supplies_doc.php?id=<?php echo $id; ?>&font_size=<?php echo $font_size; ?>"
               class="btn btn-success btn-lg" target="_blank">
                <i class="fas fa-file-word me-2"></i>تصدير DOC
            </a>
        </div>

        <!-- أدوات التحكم في التنسيق -->
        <div class="card mb-3" style="max-width: 800px; margin: 0 auto;">
            <div class="card-header bg-light">
                <h6 class="mb-0"><i class="fas fa-tools me-2"></i>🎛️ أدوات التحكم في التنسيق</h6>
            </div>
            <div class="card-body">
                <!-- تباعد الأسطر -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label fw-bold">📏 تباعد الأسطر:</label>
                    </div>
                    <div class="col-md-9">
                        <div class="btn-group" role="group">
                            <button onclick="changeLineSpacing(1.0)" class="btn btn-outline-secondary btn-sm">1.0</button>
                            <button onclick="changeLineSpacing(1.15)" class="btn btn-outline-secondary btn-sm">1.15</button>
                            <button onclick="changeLineSpacing(1.5)" class="btn btn-outline-secondary btn-sm">1.5</button>
                            <button onclick="changeLineSpacing(2.0)" class="btn btn-outline-secondary btn-sm">2.0</button>
                        </div>
                    </div>
                </div>

                <!-- المسافة بين الجدول والتوقيعات -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label fw-bold">📐 المسافة بين الجدول والتوقيعات:</label>
                    </div>
                    <div class="col-md-9">
                        <div class="btn-group" role="group">
                            <button onclick="changeTableSignatureSpacing(5)" class="btn btn-outline-info btn-sm">قريب جداً (5px)</button>
                            <button onclick="changeTableSignatureSpacing(15)" class="btn btn-outline-info btn-sm active">قريب (15px)</button>
                            <button onclick="changeTableSignatureSpacing(30)" class="btn btn-outline-info btn-sm">متوسط (30px)</button>
                            <button onclick="changeTableSignatureSpacing(50)" class="btn btn-outline-info btn-sm">بعيد (50px)</button>
                        </div>
                    </div>
                </div>

                <!-- هوامش الصفحة -->
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label fw-bold">📄 هوامش الصفحة:</label>
                    </div>
                    <div class="col-md-9">
                        <div class="btn-group" role="group">
                            <button onclick="changePageMargins('10mm')" class="btn btn-outline-warning btn-sm">ضيق (10mm)</button>
                            <button onclick="changePageMargins('15mm')" class="btn btn-outline-warning btn-sm">متوسط (15mm)</button>
                            <button onclick="changePageMargins('20mm')" class="btn btn-outline-warning btn-sm">عادي (20mm)</button>
                            <button onclick="changePageMargins('25mm')" class="btn btn-outline-warning btn-sm">واسع (25mm)</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="btn-group" role="group">
            <button onclick="window.close()" class="btn btn-secondary btn-lg me-2">
                <i class="fas fa-times me-2"></i>إغلاق
            </button>
            <a href="office_supplies.php" class="btn btn-info btn-lg">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <script>
        // تغيير حجم الخط
        function changeFontSize(size) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('font_size', size);
            window.location.href = currentUrl.toString();
        }

        // تغيير تباعد الأسطر
        function changeLineSpacing(spacing) {
            document.body.style.lineHeight = spacing;
            // تحديث جميع العناصر النصية
            const textElements = document.querySelectorAll('p, div, td, th, span, .info-section');
            textElements.forEach(element => {
                element.style.lineHeight = spacing;
            });

            // إظهار رسالة تأكيد
            showNotification('تم تغيير تباعد الأسطر إلى ' + spacing);
        }

        // تغيير المسافة بين الجدول والتوقيعات
        function changeTableSignatureSpacing(spacing) {
            const signaturesSection = document.querySelector('.signatures-section');
            if (signaturesSection) {
                signaturesSection.style.marginTop = spacing + 'px';
            }

            // تحديث المسافة في جميع أقسام التوقيعات
            const signatureSections = document.querySelectorAll('.signatures-section, .signature-row');
            signatureSections.forEach(section => {
                section.style.marginTop = spacing + 'px';
            });

            showNotification('تم تغيير المسافة بين الجدول والتوقيعات إلى ' + spacing + 'px');
        }

        // تغيير هوامش الصفحة
        function changePageMargins(margin) {
            document.body.style.margin = margin;
            document.body.style.padding = margin;

            // تحديث هوامش الطباعة
            const style = document.createElement('style');
            style.innerHTML = `
                @media print {
                    body {
                        margin: ${margin} !important;
                        padding: ${margin} !important;
                    }
                    @page {
                        margin: ${margin} !important;
                    }
                }
            `;
            document.head.appendChild(style);

            showNotification('تم تغيير هوامش الصفحة إلى ' + margin);
        }

        // إظهار رسالة تأكيد
        function showNotification(message) {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div class="alert alert-success alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // تفعيل الأزرار عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات بصرية للأزرار
            const buttons = document.querySelectorAll('.btn-outline-secondary, .btn-outline-info, .btn-outline-warning');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    // إزالة التفعيل من الأزرار الأخرى في نفس المجموعة
                    const siblings = this.parentElement.querySelectorAll('button');
                    siblings.forEach(sibling => {
                        sibling.classList.remove('active');
                    });

                    // تفعيل الزر الحالي
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
