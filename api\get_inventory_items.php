<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->query("SELECT DISTINCT item_name, current_stock 
                       FROM inventory_cards 
                       WHERE current_stock > 0 
                       ORDER BY item_name ASC");
    
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($items, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم'], JSON_UNESCAPED_UNICODE);
}
?>
