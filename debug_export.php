<?php
// ملف تشخيص مشاكل التصدير
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>تشخيص نظام التصدير</h2>";

// اختبار تضمين config.php
echo "<h3>1. اختبار تضمين config.php:</h3>";
try {
    require_once 'config/config.php';
    echo "✅ تم تضمين config.php بنجاح<br>";
    
    // اختبار دالة sanitizeInput
    if (function_exists('sanitizeInput')) {
        echo "✅ دالة sanitizeInput موجودة<br>";
        $test = sanitizeInput("test <script>alert('xss')</script>");
        echo "✅ اختبار sanitizeInput: " . $test . "<br>";
    } else {
        echo "❌ دالة sanitizeInput غير موجودة<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في تضمين config.php: " . $e->getMessage() . "<br>";
}

// اختبار الثوابت
echo "<h3>2. اختبار الثوابت:</h3>";
$constants = ['INSTITUTION_NAME_AR', 'MINISTRY_NAME_AR', 'OFFICE_NAME_AR'];
foreach ($constants as $const) {
    if (defined($const)) {
        echo "✅ {$const}: " . constant($const) . "<br>";
    } else {
        echo "❌ {$const} غير معرف<br>";
    }
}

// اختبار بيانات POST
echo "<h3>3. اختبار بيانات POST:</h3>";
$testData = [
    'receipt_number' => 'TEST-001',
    'request_date' => date('Y-m-d'),
    'beneficiary_directorate' => 'مديرية الامتحانات',
    'recipient_name' => 'أحمد محمد',
    'item_name' => 'أوراق A4',
    'quantity' => '100',
    'supply_number' => 'SUP-123',
    'notes' => 'طلب عاجل',
    'signature_warehouse_manager' => 'on',
    'signature_means_chief' => 'on',
    'signature_beneficiary' => 'on'
];

foreach ($testData as $key => $value) {
    echo "✅ {$key}: {$value}<br>";
}

// اختبار دالة generateHTML مباشرة
echo "<h3>4. اختبار إنشاء HTML:</h3>";

// محاكاة البيانات
$data = [
    'receipt_number' => sanitizeInput($testData['receipt_number']),
    'request_date' => $testData['request_date'],
    'beneficiary_directorate' => sanitizeInput($testData['beneficiary_directorate']),
    'recipient_name' => sanitizeInput($testData['recipient_name']),
    'item_name' => sanitizeInput($testData['item_name']),
    'quantity' => floatval($testData['quantity']),
    'supply_number' => sanitizeInput($testData['supply_number']),
    'notes' => sanitizeInput($testData['notes']),
    'signature_warehouse_manager' => isset($testData['signature_warehouse_manager']),
    'signature_means_chief' => isset($testData['signature_means_chief']),
    'signature_sub_director' => false,
    'signature_beneficiary' => isset($testData['signature_beneficiary'])
];

$data['formatted_date'] = date('d/m/Y', strtotime($data['request_date']));

// دالة generateHTML مبسطة للاختبار
function generateTestHTML($data) {
    $signaturesHtml = '';
    
    if ($data['signature_warehouse_manager'] || $data['signature_means_chief'] || 
        $data['signature_sub_director'] || $data['signature_beneficiary']) {
        
        $signaturesHtml = '<div style="margin-top: 50px; border: 1px solid #ccc; padding: 20px;">';
        $signaturesHtml .= '<h4>التوقيعات:</h4>';
        
        if ($data['signature_warehouse_manager']) {
            $signaturesHtml .= '<p>✅ إمضاء وختم المكلف بتسيير المخزن</p>';
        }
        
        if ($data['signature_means_chief']) {
            $signaturesHtml .= '<p>✅ إمضاء وختم رئيس مصلحة الوسائل</p>';
        }
        
        if ($data['signature_sub_director']) {
            $signaturesHtml .= '<p>✅ إمضاء وختم المدير الفرعي للإدارة العامة</p>';
        }
        
        if ($data['signature_beneficiary']) {
            $signaturesHtml .= '<p>✅ إمضاء المستفيد</p>';
        }
        
        $signaturesHtml .= '</div>';
    }
    
    return '
    <div style="border: 2px solid #2c5530; padding: 20px; margin: 20px; font-family: Arial;">
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c5530;">' . INSTITUTION_NAME_AR . '</h1>
            <h2 style="color: #4a7c59;">' . MINISTRY_NAME_AR . '</h2>
            <h3 style="color: #666;">' . OFFICE_NAME_AR . '</h3>
            <hr>
            <h2>طلب واستلام اللوازم المكتبية</h2>
        </div>
        
        <div style="margin: 20px 0;">
            <p><strong>رقم الوصل:</strong> ' . htmlspecialchars($data['receipt_number']) . '</p>
            <p><strong>التاريخ:</strong> ' . $data['formatted_date'] . '</p>
            <p><strong>المديرية المستفيدة:</strong> ' . htmlspecialchars($data['beneficiary_directorate']) . '</p>
            <p><strong>اسم المستلم:</strong> ' . htmlspecialchars($data['recipient_name']) . '</p>
            <p><strong>اسم المادة:</strong> ' . htmlspecialchars($data['item_name']) . '</p>
            <p><strong>الكمية:</strong> ' . $data['quantity'] . '</p>
            ' . ($data['supply_number'] ? '<p><strong>رقم اللوازم:</strong> ' . htmlspecialchars($data['supply_number']) . '</p>' : '') . '
            ' . ($data['notes'] ? '<p><strong>الملاحظات:</strong> ' . htmlspecialchars($data['notes']) . '</p>' : '') . '
        </div>
        
        ' . $signaturesHtml . '
    </div>';
}

try {
    $html = generateTestHTML($data);
    echo "✅ تم إنشاء HTML بنجاح<br>";
    echo "<h4>معاينة:</h4>";
    echo $html;
    
    echo "<h3>5. روابط الاختبار:</h3>";
    echo '<p><a href="debug_export.php?action=download&format=pdf" target="_blank">اختبار تنزيل PDF</a></p>';
    echo '<p><a href="debug_export.php?action=download&format=docx" target="_blank">اختبار تنزيل DOCX</a></p>';
    echo '<p><a href="debug_export.php?action=download&format=doc" target="_blank">اختبار تنزيل DOC</a></p>';
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء HTML: " . $e->getMessage() . "<br>";
}

// معالجة التنزيل
if (isset($_GET['action']) && $_GET['action'] === 'download') {
    $format = $_GET['format'] ?? 'pdf';
    
    // محاكاة POST
    $_POST = $testData;
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    echo "<h3>محاولة التنزيل بصيغة: {$format}</h3>";
    
    try {
        // تضمين ملف التصدير
        include 'export/office_supplies_export.php';
    } catch (Exception $e) {
        echo "❌ خطأ في التصدير: " . $e->getMessage() . "<br>";
    }
}
?>
