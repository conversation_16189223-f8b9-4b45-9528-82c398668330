<?php
// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/config.php';

// التحقق من نوع التصدير
$format = $_GET['format'] ?? 'pdf';
$allowed_formats = ['pdf', 'docx', 'doc'];

if (!in_array($format, $allowed_formats)) {
    http_response_code(400);
    die('نوع ملف غير مدعوم: ' . htmlspecialchars($format));
}

// التحقق من وجود البيانات
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    die('طريقة غير مسموحة. يجب استخدام POST');
}

// جلب البيانات من النموذج
$data = [
    'receipt_number' => sanitizeInput($_POST['receipt_number'] ?? ''),
    'request_date' => $_POST['request_date'] ?? '',
    'beneficiary_directorate' => sanitizeInput($_POST['beneficiary_directorate'] ?? ''),
    'recipient_name' => sanitizeInput($_POST['recipient_name'] ?? ''),
    'item_name' => sanitizeInput($_POST['item_name'] ?? ''),
    'quantity' => floatval($_POST['quantity'] ?? 0),
    'supply_number' => sanitizeInput($_POST['supply_number'] ?? ''),
    'notes' => sanitizeInput($_POST['notes'] ?? ''),
    'signature_warehouse_manager' => isset($_POST['signature_warehouse_manager']),
    'signature_means_chief' => isset($_POST['signature_means_chief']),
    'signature_sub_director' => isset($_POST['signature_sub_director']),
    'signature_beneficiary' => isset($_POST['signature_beneficiary'])
];

// التحقق من البيانات الأساسية
if (empty($data['receipt_number'])) {
    http_response_code(400);
    die('رقم الوصل مطلوب');
}

if (empty($data['item_name'])) {
    http_response_code(400);
    die('اسم المادة مطلوب');
}

// تسجيل محاولة التصدير
error_log("محاولة تصدير: النوع={$format}, الرقم={$data['receipt_number']}");

// تنسيق التاريخ
if ($data['request_date']) {
    $data['formatted_date'] = date('d/m/Y', strtotime($data['request_date']));
} else {
    $data['formatted_date'] = date('d/m/Y');
}

// اختيار طريقة التصدير حسب النوع
try {
    switch ($format) {
        case 'pdf':
            exportToPDF($data);
            break;
        case 'docx':
            exportToDocx($data);
            break;
        case 'doc':
            exportToDoc($data);
            break;
        default:
            throw new Exception('نوع ملف غير مدعوم');
    }
} catch (Exception $e) {
    http_response_code(500);
    error_log('خطأ في التصدير: ' . $e->getMessage());
    die('خطأ في إنشاء الملف: ' . $e->getMessage());
}

// دالة تصدير PDF
function exportToPDF($data) {
    // استخدام HTML بسيط للـ PDF (أكثر استقراراً)
    exportToPDFSimple($data);
}

// دالة تصدير PDF بسيطة (بدون مكتبات خارجية)
function exportToPDFSimple($data) {
    try {
        $filename = 'طلب_لوازم_مكتبية_' . $data['receipt_number'] . '_' . date('Y-m-d') . '.html';

        // تنظيف أي مخرجات سابقة
        while (ob_get_level()) {
            ob_end_clean();
        }

        // إعداد headers للتنزيل
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Expires: 0');

        $html = generateHTML($data);

        if (empty($html)) {
            throw new Exception('فشل في إنشاء محتوى HTML');
        }

        echo $html;
        exit;

    } catch (Exception $e) {
        error_log('خطأ في تصدير PDF: ' . $e->getMessage());
        http_response_code(500);
        die('خطأ في إنشاء ملف PDF: ' . $e->getMessage());
    }
}

// دالة تصدير DOCX
function exportToDocx($data) {
    // استخدام PHPWord إذا كان متاحاً
    if (class_exists('PhpOffice\PhpWord\PhpWord')) {
        exportToDocxAdvanced($data);
    } else {
        exportToDocxSimple($data);
    }
}

// دالة تصدير DOCX متقدمة
function exportToDocxAdvanced($data) {
    require_once '../vendor/autoload.php';
    
    $phpWord = new \PhpOffice\PhpWord\PhpWord();
    $phpWord->setDefaultFontName('Arial');
    $phpWord->setDefaultFontSize(12);
    
    $section = $phpWord->addSection();
    
    // رأس المؤسسة
    $headerStyle = ['bold' => true, 'size' => 16, 'color' => '2c5530'];
    $section->addText(INSTITUTION_NAME_AR, $headerStyle, ['alignment' => 'center']);
    $section->addText(MINISTRY_NAME_AR, ['bold' => true, 'size' => 14], ['alignment' => 'center']);
    $section->addText(OFFICE_NAME_AR, ['bold' => true, 'size' => 12], ['alignment' => 'center']);
    
    $section->addTextBreak(2);
    
    // عنوان الوثيقة
    $section->addText('طلب واستلام اللوازم المكتبية', ['bold' => true, 'size' => 18], ['alignment' => 'center']);
    $section->addTextBreak(2);
    
    // البيانات
    $section->addText('رقم الوصل: ' . $data['receipt_number'], ['bold' => true]);
    $section->addText('التاريخ: ' . $data['formatted_date']);
    $section->addText('المديرية الفرعية أو المصلحة المستفيدة: ' . $data['beneficiary_directorate']);
    $section->addText('اسم ولقب المستلم: ' . $data['recipient_name']);
    $section->addText('اسم المادة: ' . $data['item_name']);
    $section->addText('الكمية: ' . $data['quantity']);
    
    if ($data['supply_number']) {
        $section->addText('رقم اللوازم: ' . $data['supply_number']);
    }
    
    if ($data['notes']) {
        $section->addTextBreak();
        $section->addText('الملاحظات: ' . $data['notes']);
    }
    
    // التوقيعات
    $section->addTextBreak(3);
    addSignatures($section, $data);
    
    $filename = 'طلب_لوازم_مكتبية_' . $data['receipt_number'] . '_' . date('Y-m-d') . '.docx';
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $writer = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    $writer->save('php://output');
}

// دالة تصدير DOCX بسيطة
function exportToDocxSimple($data) {
    $content = generateDocContent($data);

    $extension = $_GET['format'] === 'docx' ? 'docx' : 'doc';
    $filename = 'طلب_لوازم_مكتبية_' . $data['receipt_number'] . '_' . date('Y-m-d') . '.' . $extension;

    // تنظيف أي مخرجات سابقة
    if (ob_get_level()) {
        ob_end_clean();
    }

    if ($extension === 'docx') {
        header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    } else {
        header('Content-Type: application/msword');
    }

    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    echo $content;
    exit;
}

// دالة تصدير DOC
function exportToDoc($data) {
    exportToDocxSimple($data); // نفس الطريقة للـ DOC
}

// دالة إنشاء محتوى HTML
function generateHTML($data) {
    $signaturesHtml = '';
    
    if ($data['signature_warehouse_manager'] || $data['signature_means_chief'] || 
        $data['signature_sub_director'] || $data['signature_beneficiary']) {
        
        $signaturesHtml = '<div style="margin-top: 50px;">';
        $signaturesHtml .= '<table width="100%" style="border-collapse: collapse;">';
        $signaturesHtml .= '<tr>';
        
        if ($data['signature_warehouse_manager']) {
            $signaturesHtml .= '<td style="text-align: center; padding: 20px; border: 1px solid #000; width: 33%;">';
            $signaturesHtml .= '<div style="height: 60px; border-bottom: 2px solid #000; margin-bottom: 10px;"></div>';
            $signaturesHtml .= '<strong>إمضاء وختم المكلف بتسيير المخزن</strong>';
            $signaturesHtml .= '</td>';
        }
        
        if ($data['signature_means_chief']) {
            $signaturesHtml .= '<td style="text-align: center; padding: 20px; border: 1px solid #000; width: 33%;">';
            $signaturesHtml .= '<div style="height: 60px; border-bottom: 2px solid #000; margin-bottom: 10px;"></div>';
            $signaturesHtml .= '<strong>إمضاء وختم رئيس مصلحة الوسائل</strong>';
            $signaturesHtml .= '</td>';
        }
        
        if ($data['signature_sub_director']) {
            $signaturesHtml .= '<td style="text-align: center; padding: 20px; border: 1px solid #000; width: 33%;">';
            $signaturesHtml .= '<div style="height: 60px; border-bottom: 2px solid #000; margin-bottom: 10px;"></div>';
            $signaturesHtml .= '<strong>إمضاء وختم المدير الفرعي للإدارة العامة</strong>';
            $signaturesHtml .= '</td>';
        }
        
        $signaturesHtml .= '</tr>';
        
        if ($data['signature_beneficiary']) {
            $signaturesHtml .= '<tr>';
            $signaturesHtml .= '<td colspan="3" style="text-align: center; padding: 20px; border: 1px solid #000;">';
            $signaturesHtml .= '<div style="height: 60px; border-bottom: 2px solid #000; margin-bottom: 10px;"></div>';
            $signaturesHtml .= '<strong>إمضاء المستفيد</strong>';
            $signaturesHtml .= '</td>';
            $signaturesHtml .= '</tr>';
        }
        
        $signaturesHtml .= '</table>';
        $signaturesHtml .= '</div>';
    }
    
    return '
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>طلب واستلام اللوازم المكتبية</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #2c5530; margin: 5px 0; }
            .header h2 { color: #4a7c59; margin: 5px 0; }
            .header h3 { color: #666; margin: 5px 0; }
            .title { text-align: center; font-size: 24px; font-weight: bold; margin: 30px 0; }
            .content { margin: 20px 0; }
            .field { margin: 10px 0; }
            .field strong { display: inline-block; width: 200px; }
            table { width: 100%; border-collapse: collapse; }
            td { padding: 10px; border: 1px solid #000; text-align: center; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>' . INSTITUTION_NAME_AR . '</h1>
            <h2>' . MINISTRY_NAME_AR . '</h2>
            <h3>' . OFFICE_NAME_AR . '</h3>
            <hr>
        </div>
        
        <div class="title">طلب واستلام اللوازم المكتبية</div>
        
        <div class="content">
            <div class="field"><strong>رقم الوصل:</strong> ' . htmlspecialchars($data['receipt_number']) . '</div>
            <div class="field"><strong>التاريخ:</strong> ' . $data['formatted_date'] . '</div>
            <div class="field"><strong>المديرية الفرعية أو المصلحة المستفيدة:</strong> ' . htmlspecialchars($data['beneficiary_directorate']) . '</div>
            <div class="field"><strong>اسم ولقب المستلم:</strong> ' . htmlspecialchars($data['recipient_name']) . '</div>
            <div class="field"><strong>اسم المادة:</strong> ' . htmlspecialchars($data['item_name']) . '</div>
            <div class="field"><strong>الكمية:</strong> ' . $data['quantity'] . '</div>
            ' . ($data['supply_number'] ? '<div class="field"><strong>رقم اللوازم:</strong> ' . htmlspecialchars($data['supply_number']) . '</div>' : '') . '
            ' . ($data['notes'] ? '<div class="field"><strong>الملاحظات:</strong> ' . htmlspecialchars($data['notes']) . '</div>' : '') . '
        </div>
        
        ' . $signaturesHtml . '
    </body>
    </html>';
}

// دالة إنشاء محتوى DOC
function generateDocContent($data) {
    $content = generateHTML($data);
    
    // تحويل HTML إلى تنسيق Word
    $content = str_replace(['<html>', '</html>', '<head>', '</head>', '<body>', '</body>'], '', $content);
    
    return $content;
}

// دالة إضافة التوقيعات لـ DOCX
function addSignatures($section, $data) {
    if ($data['signature_warehouse_manager']) {
        $section->addText('إمضاء وختم المكلف بتسيير المخزن: ________________________');
        $section->addTextBreak();
    }
    
    if ($data['signature_means_chief']) {
        $section->addText('إمضاء وختم رئيس مصلحة الوسائل: ________________________');
        $section->addTextBreak();
    }
    
    if ($data['signature_sub_director']) {
        $section->addText('إمضاء وختم المدير الفرعي للإدارة العامة: ________________________');
        $section->addTextBreak();
    }
    
    if ($data['signature_beneficiary']) {
        $section->addText('إمضاء المستفيد: ________________________');
        $section->addTextBreak();
    }
}
?>
