-- تحديث جدول طلب واستلام اللوازم المكتبية لدعم المواد المتعددة
-- يجب تشغيل هذا الملف لتحديث الجدول الموجود

-- إضافة عمود materials لحفظ المواد كـ JSON
ALTER TABLE office_supplies_requests 
ADD COLUMN materials JSON AFTER request_date;

-- إضافة عمود unit للوحدة (إذا لم يكن موجوداً)
ALTER TABLE office_supplies_requests 
ADD COLUMN unit VARCHAR(50) AFTER quantity;

-- نقل البيانات الموجودة من الأعمدة القديمة إلى العمود الجديد
UPDATE office_supplies_requests 
SET materials = JSON_ARRAY(
    JSON_OBJECT(
        'item_name', item_name,
        'quantity', quantity,
        'unit', COALESCE(unit, ''),
        'supply_number', COALESCE(supply_number, '')
    )
)
WHERE materials IS NULL;

-- إن<PERSON><PERSON><PERSON> جدول جديد محسن (اختياري - للمشاريع الجديدة)
CREATE TABLE IF NOT EXISTS office_supplies_requests_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_number VARCHAR(50) UNIQUE NOT NULL,
    beneficiary_directorate VARCHAR(255) NOT NULL,
    recipient_name VARCHAR(255) NOT NULL,
    request_date DATE NOT NULL,
    materials JSON NOT NULL COMMENT 'مصفوفة JSON للمواد المتعددة',
    notes TEXT,
    signature_means_chief BOOLEAN DEFAULT FALSE,
    signature_sub_director BOOLEAN DEFAULT FALSE,
    signature_warehouse_manager BOOLEAN DEFAULT FALSE,
    signature_beneficiary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_receipt_number (receipt_number),
    INDEX idx_beneficiary_directorate (beneficiary_directorate),
    INDEX idx_request_date (request_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول المديريات والمصالح (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS directorates_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    directorate_name VARCHAR(255) NOT NULL UNIQUE,
    directorate_type ENUM('مديرية', 'مصلحة') DEFAULT 'مديرية',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_directorate_name (directorate_name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج البيانات الافتراضية للمديريات والمصالح
INSERT IGNORE INTO directorates_services (directorate_name, directorate_type) VALUES
('مديرية الامتحانات', 'مديرية'),
('مديرية المسابقات', 'مديرية'),
('مديرية الإدارة العامة', 'مديرية'),
('مديرية الوسائل', 'مديرية'),
('مديرية التكنولوجيا', 'مديرية'),
('مصلحة المحاسبة', 'مصلحة'),
('مصلحة الموارد البشرية', 'مصلحة'),
('مصلحة الصيانة', 'مصلحة'),
('مصلحة الأمن', 'مصلحة'),
('مصلحة الاتصالات', 'مصلحة'),
('مصلحة الأرشيف', 'مصلحة'),
('مصلحة النقل', 'مصلحة');

-- التحقق من البيانات المحدثة
SELECT 
    id,
    receipt_number,
    beneficiary_directorate,
    recipient_name,
    request_date,
    materials,
    created_at
FROM office_supplies_requests 
ORDER BY created_at DESC 
LIMIT 5;

-- عرض المديريات والمصالح
SELECT * FROM directorates_services WHERE is_active = 1 ORDER BY directorate_name;
