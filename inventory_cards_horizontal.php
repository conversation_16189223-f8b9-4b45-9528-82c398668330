<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'بطاقة المخزون - التصميم الأفقي';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $card_number = sanitizeInput($_POST['card_number']);
    $item_name = sanitizeInput($_POST['item_name']);
    $unit_of_measure = sanitizeInput($_POST['unit_of_measure']);
    $entry_exit_receipt = sanitizeInput($_POST['entry_exit_receipt']);
    $supplier_beneficiary = sanitizeInput($_POST['supplier_beneficiary']);
    $entry_date = $_POST['entry_date'];
    $entry_quantity = floatval($_POST['entry_quantity']);
    $exit_date = $_POST['exit_date'];
    $exit_quantity = floatval($_POST['exit_quantity']);
    $current_stock = floatval($_POST['current_stock']);
    $notes = sanitizeInput($_POST['notes']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE inventory_cards SET 
                    card_number = ?, item_name = ?, unit_of_measure = ?, 
                    entry_exit_receipt = ?, supplier_beneficiary = ?, 
                    entry_date = ?, entry_quantity = ?, exit_date = ?, 
                    exit_quantity = ?, current_stock = ?, notes = ?
                    WHERE id = ?");
                
                $stmt->execute([
                    $card_number, $item_name, $unit_of_measure,
                    $entry_exit_receipt, $supplier_beneficiary,
                    $entry_date, $entry_quantity, $exit_date,
                    $exit_quantity, $current_stock, $notes, $_POST['id']
                ]);
                
                $success_message = 'تم تحديث بطاقة المخزون بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO inventory_cards 
                    (card_number, item_name, unit_of_measure, entry_exit_receipt, 
                     supplier_beneficiary, entry_date, entry_quantity, exit_date, 
                     exit_quantity, current_stock, notes) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $card_number, $item_name, $unit_of_measure,
                    $entry_exit_receipt, $supplier_beneficiary,
                    $entry_date, $entry_quantity, $exit_date,
                    $exit_quantity, $current_stock, $notes
                ]);
                
                $success_message = 'تم إضافة بطاقة المخزون بنجاح';
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        $stmt = $db->prepare("DELETE FROM inventory_cards WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف بطاقة المخزون بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT * FROM inventory_cards WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE item_name LIKE ? OR card_number LIKE ?";
    $params = ["%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT * FROM inventory_cards $where_clause ORDER BY created_at DESC");
$stmt->execute($params);
$cards = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>
                    بطاقة المخزون - التصميم الأفقي الجديد
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- رأس المؤسسة للطباعة -->
                <div class="institution-header d-none d-print-block">
                    <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
                    <h2><?php echo MINISTRY_NAME_AR; ?></h2>
                    <h3><?php echo OFFICE_NAME_AR; ?></h3>
                </div>

                <!-- نموذج الإدخال الأفقي -->
                <form method="POST" class="needs-validation" novalidate id="inventoryCardForm">
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <!-- الصف الأول: معلومات أساسية -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="card_number" class="form-label">رقم بطاقة المخزون *</label>
                                    <input type="text" class="form-control" id="card_number" name="card_number" 
                                           value="<?php echo $edit_data['card_number'] ?? generateReferenceNumber('INV-'); ?>" required>
                                    <div class="invalid-feedback">يرجى إدخال رقم بطاقة المخزون</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="item_name" class="form-label">اسم المادة *</label>
                                    <input type="text" class="form-control" id="item_name" name="item_name" 
                                           value="<?php echo $edit_data['item_name'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">يرجى إدخال اسم المادة</div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="unit_of_measure" class="form-label">وحدة القياس *</label>
                                    <select class="form-select" id="unit_of_measure" name="unit_of_measure" required>
                                        <option value="">اختر الوحدة</option>
                                        <option value="قطعة" <?php echo ($edit_data['unit_of_measure'] ?? '') == 'قطعة' ? 'selected' : ''; ?>>قطعة</option>
                                        <option value="علبة" <?php echo ($edit_data['unit_of_measure'] ?? '') == 'علبة' ? 'selected' : ''; ?>>علبة</option>
                                        <option value="كيلوغرام" <?php echo ($edit_data['unit_of_measure'] ?? '') == 'كيلوغرام' ? 'selected' : ''; ?>>كيلوغرام</option>
                                        <option value="لتر" <?php echo ($edit_data['unit_of_measure'] ?? '') == 'لتر' ? 'selected' : ''; ?>>لتر</option>
                                        <option value="متر" <?php echo ($edit_data['unit_of_measure'] ?? '') == 'متر' ? 'selected' : ''; ?>>متر</option>
                                        <option value="رزمة" <?php echo ($edit_data['unit_of_measure'] ?? '') == 'رزمة' ? 'selected' : ''; ?>>رزمة</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار وحدة القياس</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الصف الثاني: معلومات الدخول والخروج -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-exchange-alt me-2"></i>
                                معلومات الدخول والخروج
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="entry_exit_receipt" class="form-label">رقم وصل الدخول/الخروج</label>
                                    <input type="text" class="form-control" id="entry_exit_receipt" name="entry_exit_receipt" 
                                           value="<?php echo $edit_data['entry_exit_receipt'] ?? ''; ?>" 
                                           placeholder="أدخل رقم الوصل">
                                </div>
                                
                                <div class="col-md-8 mb-3">
                                    <label for="supplier_beneficiary" class="form-label">الممون/المستفيد</label>
                                    <input type="text" class="form-control" id="supplier_beneficiary" name="supplier_beneficiary" 
                                           value="<?php echo $edit_data['supplier_beneficiary'] ?? ''; ?>">
                                </div>
                            </div>
                            
                            <!-- جدول الدخول والخروج الأفقي -->
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th colspan="2" class="text-center bg-success text-white">الدخول</th>
                                            <th colspan="2" class="text-center bg-danger text-white">الخروج</th>
                                            <th rowspan="2" class="text-center bg-warning text-dark align-middle">الكمية الموجودة في المخزن</th>
                                        </tr>
                                        <tr>
                                            <th class="text-center">التاريخ</th>
                                            <th class="text-center">الكمية</th>
                                            <th class="text-center">التاريخ</th>
                                            <th class="text-center">الكمية</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <input type="date" class="form-control" id="entry_date" name="entry_date" 
                                                       value="<?php echo $edit_data['entry_date'] ?? ''; ?>">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control" id="entry_quantity" name="entry_quantity" 
                                                       value="<?php echo $edit_data['entry_quantity'] ?? '0'; ?>" step="0.01" min="0">
                                            </td>
                                            <td>
                                                <input type="date" class="form-control" id="exit_date" name="exit_date" 
                                                       value="<?php echo $edit_data['exit_date'] ?? ''; ?>">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control" id="exit_quantity" name="exit_quantity" 
                                                       value="<?php echo $edit_data['exit_quantity'] ?? '0'; ?>" step="0.01" min="0">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control bg-warning" id="current_stock" name="current_stock"
                                                       value="<?php echo intval($edit_data['current_stock'] ?? 0); ?>" min="0" step="1"
                                                       title="الكمية الموجودة في المخزن - أدخل العدد يدوياً"
                                                       placeholder="أدخل الكمية">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- الصف الثالث: الملاحظات والأزرار -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-sticky-note me-2"></i>
                                الملاحظات والإجراءات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="notes" class="form-label">الملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $edit_data['notes'] ?? ''; ?></textarea>
                                </div>

                                <div class="col-md-4 mb-3 d-flex align-items-end">
                                    <div class="w-100">
                                        <?php if ($edit_data): ?>
                                            <button type="submit" name="update" class="btn btn-primary btn-lg w-100 mb-2">
                                                <i class="fas fa-save me-2"></i> تحديث البطاقة
                                            </button>
                                            <a href="inventory_cards_horizontal.php" class="btn btn-secondary w-100">
                                                <i class="fas fa-times me-2"></i> إلغاء
                                            </a>
                                        <?php else: ?>
                                            <button type="submit" name="save" class="btn btn-success btn-lg w-100 mb-2">
                                                <i class="fas fa-save me-2"></i> حفظ البطاقة
                                            </button>
                                            <button type="reset" class="btn btn-secondary w-100" onclick="clearForm()">
                                                <i class="fas fa-undo me-2"></i> مسح
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات الأفقي -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    بطاقات المخزون - العرض الأفقي
                </h5>

                <div class="d-flex gap-2">
                    <!-- البحث -->
                    <form method="GET" class="d-flex">
                        <input type="text" name="search" class="form-control me-2"
                               placeholder="بحث بالاسم أو رقم البطاقة..."
                               value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if ($search): ?>
                            <a href="inventory_cards_horizontal.php" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </form>

                    <!-- زر طباعة جميع البطاقات -->
                    <?php if (!empty($cards)): ?>
                        <a href="print_inventory_card.php?print_all=1" target="_blank" class="btn btn-warning">
                            <i class="fas fa-print me-2"></i>طباعة جميع البطاقات
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th width="10%">رقم البطاقة</th>
                                <th width="20%">اسم المادة</th>
                                <th width="8%">الوحدة</th>
                                <th width="12%">الممون/المستفيد</th>
                                <th width="8%">دخول</th>
                                <th width="8%">خروج</th>
                                <th width="8%">الرصيد</th>
                                <th width="10%">تاريخ آخر حركة</th>
                                <th width="16%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($cards)): ?>
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بطاقات مخزون
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($cards as $card): ?>
                                    <tr>
                                        <td>
                                            <strong class="text-primary"><?php echo htmlspecialchars($card['card_number']); ?></strong>
                                        </td>
                                        <td>
                                            <div class="fw-bold"><?php echo htmlspecialchars($card['item_name']); ?></div>
                                            <?php if ($card['notes']): ?>
                                                <small class="text-muted"><?php echo htmlspecialchars(substr($card['notes'], 0, 50)) . (strlen($card['notes']) > 50 ? '...' : ''); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($card['unit_of_measure']); ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($card['supplier_beneficiary'] ?: '-'); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo number_format($card['entry_quantity'], 0); ?>
                                            </span>
                                            <?php if ($card['entry_date']): ?>
                                                <br><small class="text-muted"><?php echo formatArabicDate($card['entry_date']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">
                                                <?php echo number_format($card['exit_quantity'], 0); ?>
                                            </span>
                                            <?php if ($card['exit_date']): ?>
                                                <br><small class="text-muted"><?php echo formatArabicDate($card['exit_date']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $stock_class = 'bg-warning';
                                            if ($card['current_stock'] <= 0) {
                                                $stock_class = 'bg-danger';
                                            } elseif ($card['current_stock'] > 100) {
                                                $stock_class = 'bg-success';
                                            }
                                            ?>
                                            <span class="badge <?php echo $stock_class; ?> fs-6">
                                                <?php echo number_format($card['current_stock'], 0); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $last_date = $card['exit_date'] ?: $card['entry_date'];
                                            echo $last_date ? formatArabicDate($last_date) : '-';
                                            ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $card['id']; ?>"
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteCard(<?php echo $card['id']; ?>)"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button onclick="viewCard(<?php echo $card['id']; ?>)"
                                                        class="btn btn-outline-info" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button onclick="printCard(<?php echo $card['id']; ?>)"
                                                        class="btn btn-outline-secondary" title="طباعة">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة -->
                <?php if (!empty($cards)): ?>
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5><?php echo count($cards); ?></h5>
                                    <small>إجمالي البطاقات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h5><?php echo count(array_filter($cards, fn($c) => $c['current_stock'] > 0)); ?></h5>
                                    <small>مواد متوفرة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h5><?php echo count(array_filter($cards, fn($c) => $c['current_stock'] <= 10 && $c['current_stock'] > 0)); ?></h5>
                                    <small>مواد قليلة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h5><?php echo count(array_filter($cards, fn($c) => $c['current_stock'] <= 0)); ?></h5>
                                    <small>مواد منتهية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// تحسين حقل الكمية الموجودة في المخزن
document.addEventListener('DOMContentLoaded', function() {
    const currentStock = document.getElementById('current_stock');

    // تحديث لون الخلفية حسب الكمية المدخلة يدوياً
    function updateStockColor() {
        const value = parseInt(currentStock.value) || 0;

        // إزالة جميع الكلاسات السابقة
        currentStock.className = 'form-control ';

        if (value <= 0) {
            currentStock.className += 'bg-danger text-white';
        } else if (value <= 10) {
            currentStock.className += 'bg-warning';
        } else {
            currentStock.className += 'bg-success text-white';
        }
    }

    // منع إدخال النقاط والفواصل
    currentStock.addEventListener('input', function(e) {
        // إزالة أي نقاط أو فواصل
        let value = e.target.value.replace(/[.,]/g, '');

        // التأكد من أن القيمة رقم صحيح موجب
        if (value && !isNaN(value)) {
            value = Math.max(0, parseInt(value));
        } else {
            value = '';
        }

        e.target.value = value;
        updateStockColor();
    });

    // منع إدخال النقاط والفواصل من لوحة المفاتيح
    currentStock.addEventListener('keypress', function(e) {
        // منع النقطة والفاصلة والعلامات الأخرى
        if (e.key === '.' || e.key === ',' || e.key === '-' || e.key === '+' || e.key === 'e' || e.key === 'E') {
            e.preventDefault();
        }
    });

    // تحديث اللون عند التحميل
    updateStockColor();
});

// مسح النموذج
function clearForm() {
    document.getElementById('inventoryCardForm').reset();
    const currentStock = document.getElementById('current_stock');
    currentStock.value = '';
    currentStock.className = 'form-control bg-warning';
}

// حذف البطاقة
function deleteCard(id) {
    confirmDelete('هل أنت متأكد من حذف هذه البطاقة؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// عرض تفاصيل البطاقة
function viewCard(id) {
    window.location.href = `?action=edit&id=${id}`;
}

// طباعة البطاقة
function printCard(id) {
    window.open(`print_inventory_card.php?id=${id}`, '_blank');
}
</script>

<?php include 'includes/footer.php'; ?>
