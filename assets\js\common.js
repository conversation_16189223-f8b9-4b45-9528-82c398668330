// ملف الجافا سكريبت المشترك لنظام تسيير المخزن

// دالة عرض رسالة نجاح
function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: 'نجح!',
        text: message,
        confirmButtonText: 'موافق',
        timer: 3000,
        timerProgressBar: true
    });
}

// دالة عرض رسالة خطأ
function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'خطأ!',
        text: message,
        confirmButtonText: 'موافق'
    });
}

// دالة عرض رسالة تحذير
function showWarning(message) {
    Swal.fire({
        icon: 'warning',
        title: 'تحذير!',
        text: message,
        confirmButtonText: 'موافق'
    });
}

// دالة عرض رسالة معلومات
function showInfo(message) {
    Swal.fire({
        icon: 'info',
        title: 'معلومات',
        text: message,
        confirmButtonText: 'موافق'
    });
}

// دالة تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return Swal.fire({
        title: 'تأكيد الحذف',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    });
}

// دالة تأكيد إجراء
function confirmAction(message) {
    return Swal.fire({
        title: 'تأكيد الإجراء',
        text: message,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم',
        cancelButtonText: 'إلغاء'
    });
}

// دالة طباعة الصفحة
function printPage() {
    window.print();
}

// دالة إرسال عبر Gmail
function sendEmail(provider) {
    let emailUrl = '';
    const subject = encodeURIComponent('وثيقة من نظام تسيير المخزن');
    const body = encodeURIComponent('مرفق وثيقة من نظام تسيير المخزن الخاص بالديوان الوطني للامتحانات والمسابقات');
    
    switch(provider) {
        case 'gmail':
            emailUrl = `https://mail.google.com/mail/?view=cm&fs=1&su=${subject}&body=${body}`;
            break;
        case 'outlook':
            emailUrl = `https://outlook.live.com/mail/0/deeplink/compose?subject=${subject}&body=${body}`;
            break;
        case 'yahoo':
            emailUrl = `https://compose.mail.yahoo.com/?subject=${subject}&body=${body}`;
            break;
        default:
            emailUrl = `mailto:?subject=${subject}&body=${body}`;
    }
    
    window.open(emailUrl, '_blank');
}

// دالة إرسال عبر WhatsApp
function sendWhatsApp() {
    const phoneNumber = '+213555123456'; // رقم افتراضي
    const message = 'مرحباً، أرسل لك وثيقة من نظام تسيير المخزن';
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

// دالة عرض مؤشر التحميل
function showLoading(message = 'جاري التحميل...') {
    Swal.fire({
        title: message,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
}

// دالة إخفاء مؤشر التحميل
function hideLoading() {
    Swal.close();
}

// دالة تنسيق الأرقام بالعربية
function formatArabicNumber(number) {
    return new Intl.NumberFormat('ar-DZ').format(number);
}

// دالة التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دالة التحقق من صحة رقم الهاتف الجزائري
function isValidPhone(phone) {
    const phoneRegex = /^(\+213|0)[5-7][0-9]{8}$/;
    return phoneRegex.test(phone);
}

// دالة تفعيل التحقق من صحة النماذج
function enableFormValidation() {
    // Bootstrap form validation
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                showWarning('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

// دالة تحديث عداد الأحرف
function updateCharacterCount(inputId, counterId, maxLength) {
    const input = document.getElementById(inputId);
    const counter = document.getElementById(counterId);
    
    if (input && counter) {
        input.addEventListener('input', function() {
            const currentLength = this.value.length;
            counter.textContent = `${currentLength}/${maxLength}`;
            
            if (currentLength > maxLength * 0.9) {
                counter.classList.add('text-warning');
            } else {
                counter.classList.remove('text-warning');
            }
            
            if (currentLength >= maxLength) {
                counter.classList.add('text-danger');
            } else {
                counter.classList.remove('text-danger');
            }
        });
    }
}

// دالة إضافة تأثيرات التحميل للأزرار
function addButtonLoadingEffect() {
    const buttons = document.querySelectorAll('button[type="submit"]');
    
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري المعالجة...';
            this.disabled = true;
            
            // إعادة تفعيل الزر بعد 3 ثوان
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 3000);
        });
    });
}

// دالة تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل التحقق من صحة النماذج
    enableFormValidation();
    
    // إضافة تأثيرات التحميل للأزرار
    addButtonLoadingEffect();
    
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// دالة تحديث الوقت في الوقت الفعلي
function updateRealTime() {
    const timeElements = document.querySelectorAll('.real-time');
    
    timeElements.forEach(element => {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Africa/Algiers'
        };
        
        element.textContent = now.toLocaleDateString('ar-DZ', options);
    });
}

// تحديث الوقت كل ثانية
setInterval(updateRealTime, 1000);

// دالة حفظ البيانات محلياً
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات محلياً:', error);
        return false;
    }
}

// دالة جلب البيانات المحفوظة محلياً
function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('خطأ في جلب البيانات المحفوظة:', error);
        return null;
    }
}

// دالة حذف البيانات المحفوظة محلياً
function removeFromLocalStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('خطأ في حذف البيانات المحفوظة:', error);
        return false;
    }
}
