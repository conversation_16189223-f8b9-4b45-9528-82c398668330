<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حل مشكلة قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
        }
        .fix-card { 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
        }
        .error-badge { 
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
        }
        .step-card {
            border-left: 4px solid #28a745;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="fix-card p-5">
            <!-- العنوان -->
            <div class="text-center mb-4">
                <div class="error-badge mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    خطأ في قاعدة البيانات
                </div>
                <h2 class="text-danger">
                    <i class="fas fa-database me-2"></i>
                    حل مشكلة: Column 'bold_text' not found
                </h2>
                <p class="text-muted">الأعمدة الجديدة لخيارات التنسيق غير موجودة في قاعدة البيانات</p>
            </div>

            <!-- وصف المشكلة -->
            <div class="alert alert-danger">
                <h5><i class="fas fa-bug me-2"></i>المشكلة:</h5>
                <p class="mb-0">
                    <strong>رسالة الخطأ:</strong> 
                    <code>SQLSTATE[42S22]: Column not found: 1054 Champ 'bold_text' inconnu dans field list</code>
                </p>
                <p class="mb-0 mt-2">
                    <strong>السبب:</strong> الأعمدة الجديدة (bold_text, text_align, font_color) لم يتم إضافتها لقاعدة البيانات بعد.
                </p>
            </div>

            <!-- الحلول -->
            <div class="row mb-4">
                <div class="col-12">
                    <h4 class="text-success mb-3">
                        <i class="fas fa-tools me-2"></i>
                        الحلول المتاحة:
                    </h4>
                </div>
            </div>

            <!-- الحل الأول - تلقائي -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="step-card p-4">
                        <h5 class="text-success">
                            <i class="fas fa-magic me-2"></i>
                            الحل الأول: التحديث التلقائي (الأسهل)
                        </h5>
                        <p>استخدم أداة التحديث التلقائي لإضافة الأعمدة المطلوبة:</p>
                        <div class="text-center">
                            <a href="update_database.php" class="btn btn-success btn-lg">
                                <i class="fas fa-database me-2"></i>
                                تحديث قاعدة البيانات تلقائياً
                            </a>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            هذا الحل سيضيف الأعمدة المطلوبة تلقائياً ويحدث البيانات الموجودة
                        </small>
                    </div>
                </div>
            </div>

            <!-- الحل الثاني - يدوي -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="step-card p-4">
                        <h5 class="text-primary">
                            <i class="fas fa-code me-2"></i>
                            الحل الثاني: التحديث اليدوي
                        </h5>
                        <p>تشغيل الأوامر التالية في phpMyAdmin أو أي أداة إدارة قواعد البيانات:</p>
                        
                        <div class="bg-dark text-light p-3 rounded">
                            <code>
                                -- إضافة عمود النص العريض<br>
                                ALTER TABLE office_supplies_requests <br>
                                ADD COLUMN bold_text TINYINT(1) DEFAULT 0;<br><br>
                                
                                -- إضافة عمود محاذاة النص<br>
                                ALTER TABLE office_supplies_requests <br>
                                ADD COLUMN text_align VARCHAR(10) DEFAULT 'right';<br><br>
                                
                                -- إضافة عمود لون الخط<br>
                                ALTER TABLE office_supplies_requests <br>
                                ADD COLUMN font_color VARCHAR(20) DEFAULT 'black';
                            </code>
                        </div>
                        
                        <div class="mt-3">
                            <a href="update_office_supplies_table.sql" class="btn btn-primary" download>
                                <i class="fas fa-download me-2"></i>
                                تحميل ملف SQL
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الحل الثالث - مؤقت -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="step-card p-4">
                        <h5 class="text-warning">
                            <i class="fas fa-clock me-2"></i>
                            الحل الثالث: الاستخدام بدون خيارات التنسيق (مؤقت)
                        </h5>
                        <p>النظام سيعمل بدون خيارات التنسيق حتى يتم تحديث قاعدة البيانات:</p>
                        <ul>
                            <li>✅ جميع الميزات الأساسية تعمل</li>
                            <li>✅ قائمة المديريات محدثة (27 خيار)</li>
                            <li>✅ جدول المواد مع خانة الملاحظات</li>
                            <li>✅ التوقيعات الأفقية</li>
                            <li>❌ خيارات التنسيق (Bold, محاذاة، ألوان) مخفية مؤقتاً</li>
                        </ul>
                        <div class="text-center">
                            <a href="office_supplies.php" class="btn btn-warning btn-lg">
                                <i class="fas fa-external-link-alt me-2"></i>
                                استخدام النظام بدون خيارات التنسيق
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- خطوات التحقق -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-check-circle me-2"></i>للتحقق من نجاح التحديث:</h6>
                        <ol class="mb-0">
                            <li>افتح نظام اللوازم المكتبية</li>
                            <li>إذا لم تظهر رسالة تحذير صفراء، فالتحديث نجح</li>
                            <li>ابحث عن قسم "خيارات التنسيق للطباعة" في النموذج</li>
                            <li>جرب حفظ طلب جديد - يجب أن يعمل بدون أخطاء</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- أزرار سريعة -->
            <div class="text-center">
                <h5 class="mb-3">إجراءات سريعة:</h5>
                <div class="btn-group-vertical btn-group-lg">
                    <a href="update_database.php" class="btn btn-success btn-lg mb-2">
                        <i class="fas fa-magic me-2"></i>
                        حل المشكلة تلقائياً
                    </a>
                    <a href="office_supplies.php" class="btn btn-primary btn-lg mb-2">
                        <i class="fas fa-external-link-alt me-2"></i>
                        فتح النظام (مع التحذير)
                    </a>
                    <a href="test_office_supplies_final.html" class="btn btn-info btn-lg">
                        <i class="fas fa-test me-2"></i>
                        اختبار النظام
                    </a>
                </div>
            </div>

            <!-- تذييل -->
            <div class="text-center mt-4 pt-3 border-top">
                <p class="text-muted">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>نصيحة:</strong> استخدم الحل الأول (التحديث التلقائي) للحصول على أفضل تجربة
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
