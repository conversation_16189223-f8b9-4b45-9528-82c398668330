/* نظام تسيير المخزن - الأنماط المخصصة */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* المتغيرات الأساسية */
:root {
    --primary-color: #2c5530;
    --secondary-color: #4a7c59;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
    color: #333;
}

/* رأس المؤسسة */
.institution-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.institution-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.institution-header h1,
.institution-header h2,
.institution-header h3 {
    position: relative;
    z-index: 1;
    margin: 5px 0;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.institution-header h1 {
    font-size: 1.8rem;
    font-weight: 700;
}

.institution-header h2 {
    font-size: 1.4rem;
    font-weight: 600;
}

.institution-header h3 {
    font-size: 1.2rem;
    font-weight: 500;
}

/* شريط التنقل */
.navbar {
    background-color: #ffffff;
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.navbar-brand {
    font-weight: 600;
    color: var(--primary-color) !important;
    font-size: 1.3rem;
}

.nav-link {
    color: #495057 !important;
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(44, 85, 48, 0.1);
}

.nav-link.active {
    color: var(--primary-color) !important;
    font-weight: 600;
    background-color: rgba(44, 85, 48, 0.15);
}

/* البطاقات */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
    border: none;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: none;
    padding: 10px 20px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
    background: linear-gradient(135deg, #1e3a21 0%, #3a6b47 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f39c12 100%);
    color: #333;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #3498db 100%);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background: white;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    padding: 15px 10px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(44, 85, 48, 0.05);
}

.table td {
    padding: 12px 10px;
    vertical-align: middle;
    text-align: center;
}

/* النماذج */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #ced4da;
    transition: var(--transition);
    padding: 10px 15px;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    border-left: 4px solid var(--success-color);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);
    border-left: 4px solid var(--danger-color);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(243, 156, 18, 0.1) 100%);
    border-left: 4px solid var(--warning-color);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(52, 152, 219, 0.1) 100%);
    border-left: 4px solid var(--info-color);
    color: #0c5460;
}

/* الشارات */
.badge {
    border-radius: 6px;
    font-size: 0.8rem;
    padding: 6px 12px;
}

/* التذييل */
footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    margin-top: 50px;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* تأثير الموجة للأزرار */
.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .institution-header h1 {
        font-size: 1.4rem;
    }
    
    .institution-header h2 {
        font-size: 1.2rem;
    }
    
    .institution-header h3 {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}

/* طباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .institution-header {
        background: none !important;
        color: black !important;
        border-bottom: 2px solid black;
        box-shadow: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .card-header {
        background: none !important;
        color: black !important;
        border-bottom: 1px solid #ddd;
    }
    
    body {
        background: white !important;
    }
    
    .table {
        box-shadow: none;
    }
    
    .btn {
        display: none;
    }
}
