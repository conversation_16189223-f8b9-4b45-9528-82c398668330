<?php
// ملف اختبار وظائف التصدير
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

// إنشاء بيانات تجريبية للاختبار
$test_data = [
    'id' => 999,
    'receipt_number' => 'TEST-2024-001',
    'request_date' => date('Y-m-d'),
    'beneficiary_directorate' => 'مديرية الاختبارات والتقييم',
    'recipient_name' => 'أحمد محمد علي',
    'materials' => json_encode([
        [
            'item_name' => 'أقلام حبر جاف زرقاء',
            'quantity' => 50,
            'supply_number' => 'PEN-001',
            'notes' => 'للاستخدام في الامتحانات'
        ],
        [
            'item_name' => 'ورق A4 أبيض',
            'quantity' => 100,
            'supply_number' => 'PAPER-002',
            'notes' => 'جودة عالية للطباعة'
        ],
        [
            'item_name' => 'مجلدات بلاستيكية',
            'quantity' => 25,
            'supply_number' => 'FOLDER-003',
            'notes' => 'ألوان متنوعة'
        ]
    ]),
    'notes' => 'هذا طلب تجريبي لاختبار وظائف التصدير الجديدة. يرجى التأكد من عمل جميع الخيارات بشكل صحيح.'
];

// حفظ البيانات التجريبية في قاعدة البيانات
try {
    $stmt = $db->prepare("INSERT INTO office_supplies_requests (id, receipt_number, request_date, beneficiary_directorate, recipient_name, materials, notes) VALUES (?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE receipt_number = VALUES(receipt_number), request_date = VALUES(request_date), beneficiary_directorate = VALUES(beneficiary_directorate), recipient_name = VALUES(recipient_name), materials = VALUES(materials), notes = VALUES(notes)");
    
    $stmt->execute([
        $test_data['id'],
        $test_data['receipt_number'],
        $test_data['request_date'],
        $test_data['beneficiary_directorate'],
        $test_data['recipient_name'],
        $test_data['materials'],
        $test_data['notes']
    ]);
    
    $test_created = true;
} catch (Exception $e) {
    $test_created = false;
    $error_message = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف التصدير - نظام اللوازم المكتبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 900px;
        }
        .test-header {
            background: linear-gradient(45deg, #2c5530, #4a7c59);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #2c5530;
        }
        .btn-test {
            margin: 5px;
            padding: 12px 25px;
            font-weight: bold;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .test-data {
            background: #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="test-header">
                <h1><i class="fas fa-vial me-3"></i>اختبار وظائف التصدير</h1>
                <p class="mb-0">اختبار شامل لجميع خيارات التصدير الجديدة</p>
            </div>

            <!-- حالة إنشاء البيانات التجريبية -->
            <div class="test-section">
                <h4><i class="fas fa-database me-2"></i>حالة البيانات التجريبية</h4>
                <?php if ($test_created): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <span class="status-success">تم إنشاء البيانات التجريبية بنجاح!</span>
                    </div>
                    <div class="test-data">
                        <strong>معرف الطلب:</strong> <?php echo $test_data['id']; ?><br>
                        <strong>رقم الوصل:</strong> <?php echo $test_data['receipt_number']; ?><br>
                        <strong>المديرية:</strong> <?php echo $test_data['beneficiary_directorate']; ?><br>
                        <strong>المستلم:</strong> <?php echo $test_data['recipient_name']; ?><br>
                        <strong>عدد المواد:</strong> <?php echo count(json_decode($test_data['materials'], true)); ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span class="status-error">فشل في إنشاء البيانات التجريبية!</span>
                        <br><small><?php echo $error_message ?? 'خطأ غير معروف'; ?></small>
                    </div>
                <?php endif; ?>
            </div>

            <!-- اختبار الطباعة العادية -->
            <div class="test-section">
                <h4><i class="fas fa-print me-2"></i>اختبار الطباعة العادية</h4>
                <p>اختبر صفحة الطباعة المحسنة مع التحديثات الجديدة</p>
                <div class="text-center">
                    <?php foreach (['12BOLD', '14BOLD', '16BOLD', '18BOLD', '20BOLD'] as $font): ?>
                        <a href="print_office_supplies_enhanced.php?id=<?php echo $test_data['id']; ?>&font_size=<?php echo $font; ?>" 
                           target="_blank" class="btn btn-primary btn-test">
                            <i class="fas fa-print me-2"></i><?php echo $font; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- اختبار تصدير PDF -->
            <div class="test-section">
                <h4><i class="fas fa-file-pdf me-2 text-danger"></i>اختبار تصدير PDF</h4>
                <p>اختبر وظيفة التصدير إلى PDF المحسنة</p>
                <div class="text-center">
                    <?php foreach (['12BOLD', '14BOLD', '16BOLD', '18BOLD', '20BOLD'] as $font): ?>
                        <a href="export_office_supplies_pdf.php?id=<?php echo $test_data['id']; ?>&font_size=<?php echo $font; ?>" 
                           target="_blank" class="btn btn-danger btn-test">
                            <i class="fas fa-file-pdf me-2"></i>PDF <?php echo $font; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- اختبار تصدير DOC -->
            <div class="test-section">
                <h4><i class="fas fa-file-word me-2 text-success"></i>اختبار تصدير DOC</h4>
                <p>اختبر وظيفة التصدير إلى DOC المحسنة</p>
                <div class="text-center">
                    <?php foreach (['12BOLD', '14BOLD', '16BOLD', '18BOLD', '20BOLD'] as $font): ?>
                        <a href="export_office_supplies_doc.php?id=<?php echo $test_data['id']; ?>&font_size=<?php echo $font; ?>" 
                           target="_blank" class="btn btn-success btn-test">
                            <i class="fas fa-file-word me-2"></i>DOC <?php echo $font; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- تعليمات الاختبار -->
            <div class="test-section">
                <h4><i class="fas fa-info-circle me-2 text-info"></i>تعليمات الاختبار</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>✅ ما يجب التحقق منه:</h6>
                        <ul>
                            <li>وضوح النص العربي</li>
                            <li>صحة تنسيق الجدول</li>
                            <li>محاذاة التوقيعات</li>
                            <li>وضوح رأس المؤسسة</li>
                            <li>عمل أزرار التصدير</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🔍 مشاكل محلولة:</h6>
                        <ul>
                            <li>الكتابة غير المفهومة في DOC</li>
                            <li>الملف الفارغ في PDF</li>
                            <li>عدم وضوح رأس المؤسسة</li>
                            <li>عدم محاذاة التوقيعات</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <a href="test_office_supplies_updated.html" class="btn btn-info btn-lg me-2">
                    <i class="fas fa-arrow-left me-2"></i>العودة لصفحة الاختبار
                </a>
                <a href="office_supplies.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-home me-2"></i>العودة للنظام
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
