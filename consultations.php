<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'الاستشارات';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// قائمة البلدان
$countries = [
    'الجزائر', 'ألمانيا', 'أنجلترا', 'أمريكا', 'اليابان', 'روسيا', 
    'سنغافورة', 'تركيا', 'لبنان', 'النمسا', 'موزمبيق', 'زمبابوي', 'زامبيا'
];

// جلب قائمة العملات
$stmt = $db->query("SELECT code, name_ar, symbol FROM currencies WHERE is_active = 1 ORDER BY name_ar ASC");
$currencies = $stmt->fetchAll();

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $consultation_number = sanitizeInput($_POST['consultation_number']);
    $consultation_date = $_POST['consultation_date'];
    $country = sanitizeInput($_POST['country']);
    $custom_message = sanitizeInput($_POST['custom_message']);
    $currency_type = sanitizeInput($_POST['currency_type']);
    $vat_rate = sanitizeInput($_POST['vat_rate']);
    $signature_enabled = isset($_POST['signature_enabled']);
    
    // معالجة عناصر الجدول
    $items = [];
    if (isset($_POST['items'])) {
        foreach ($_POST['items'] as $item) {
            if (!empty($item['designation'])) {
                $items[] = [
                    'designation' => sanitizeInput($item['designation']),
                    'quantity' => floatval($item['quantity']),
                    'unit_price' => floatval($item['unit_price']),
                    'total_amount' => floatval($item['total_amount']),
                    'amount_without_tax' => floatval($item['amount_without_tax']),
                    'vat_amount' => floatval($item['vat_amount']),
                    'total_with_tax' => floatval($item['total_with_tax'])
                ];
            }
        }
    }
    
    $total_amount = array_sum(array_column($items, 'total_with_tax'));
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE consultations SET 
                    consultation_number = ?, consultation_date = ?, country = ?, 
                    custom_message = ?, items = ?, total_amount = ?, currency_type = ?, 
                    vat_rate = ?, signature_enabled = ?
                    WHERE id = ?");
                
                $stmt->execute([
                    $consultation_number, $consultation_date, $country,
                    $custom_message, json_encode($items, JSON_UNESCAPED_UNICODE), $total_amount, $currency_type,
                    $vat_rate, $signature_enabled, $_POST['id']
                ]);
                
                $success_message = 'تم تحديث الاستشارة بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO consultations 
                    (consultation_number, consultation_date, country, custom_message, 
                     items, total_amount, currency_type, vat_rate, signature_enabled) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $consultation_number, $consultation_date, $country, $custom_message,
                    json_encode($items, JSON_UNESCAPED_UNICODE), $total_amount, $currency_type,
                    $vat_rate, $signature_enabled
                ]);
                
                $success_message = 'تم إضافة الاستشارة بنجاح';
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        $stmt = $db->prepare("DELETE FROM consultations WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف الاستشارة بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT * FROM consultations WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
    if ($edit_data && $edit_data['items']) {
        $edit_data['items'] = json_decode($edit_data['items'], true);
    }
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE consultation_number LIKE ? OR country LIKE ?";
    $params = ["%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT * FROM consultations $where_clause ORDER BY consultation_date DESC, created_at DESC");
$stmt->execute($params);
$consultations = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-file-contract me-2"></i>
                    الاستشارات
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- رأس المؤسسة للطباعة -->
                <div class="institution-header d-none d-print-block">
                    <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
                    <h2><?php echo MINISTRY_NAME_AR; ?></h2>
                    <h3><?php echo OFFICE_NAME_AR; ?></h3>
                </div>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate id="consultationForm">
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="consultation_number" class="form-label">رقم الاستشارة *</label>
                            <input type="text" class="form-control" id="consultation_number" name="consultation_number" 
                                   value="<?php echo $edit_data['consultation_number'] ?? generateReferenceNumber('CONS-'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال رقم الاستشارة</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="consultation_date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="consultation_date" name="consultation_date" 
                                   value="<?php echo $edit_data['consultation_date'] ?? date('Y-m-d'); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال التاريخ</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="country" class="form-label">البلد *</label>
                            <select class="form-select" id="country" name="country" required>
                                <option value="">اختر البلد</option>
                                <?php foreach ($countries as $country_name): ?>
                                    <option value="<?php echo $country_name; ?>" 
                                            <?php echo ($edit_data['country'] ?? 'الجزائر') === $country_name ? 'selected' : ''; ?>>
                                        <?php echo $country_name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار البلد</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="currency_type" class="form-label">العملة *</label>
                            <select class="form-select" id="currency_type" name="currency_type" required>
                                <?php foreach ($currencies as $currency): ?>
                                    <option value="<?php echo $currency['code']; ?>" 
                                            <?php echo ($edit_data['currency_type'] ?? 'DZD') === $currency['code'] ? 'selected' : ''; ?>>
                                        <?php echo $currency['name_ar'] . ' (' . $currency['symbol'] . ')'; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار العملة</div>
                        </div>
                    </div>
                    
                    <!-- الرسالة المخصصة -->
                    <div class="mb-4">
                        <label for="custom_message" class="form-label">الرسالة المخصصة</label>
                        <textarea class="form-control" id="custom_message" name="custom_message" rows="4" 
                                  placeholder="اكتب رسالة مخصصة للاستشارة..."><?php echo $edit_data['custom_message'] ?? ''; ?></textarea>
                    </div>
                    
                    <!-- جدول العناصر -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">عناصر الاستشارة</h6>
                            <button type="button" class="btn btn-sm btn-success" onclick="addItem()">
                                <i class="fas fa-plus me-1"></i> إضافة عنصر
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="itemsTable">
                                    <thead>
                                        <tr>
                                            <th width="5%">الرقم</th>
                                            <th width="25%">التعيين</th>
                                            <th width="10%">الكمية</th>
                                            <th width="15%">سعر الوحدة</th>
                                            <th width="15%">المبلغ</th>
                                            <th width="15%">بدون رسوم</th>
                                            <th width="10%">الرسم</th>
                                            <th width="15%">الإجمالي</th>
                                            <th width="5%">حذف</th>
                                        </tr>
                                    </thead>
                                    <tbody id="itemsTableBody">
                                        <?php if ($edit_data && $edit_data['items']): ?>
                                            <?php foreach ($edit_data['items'] as $index => $item): ?>
                                                <tr>
                                                    <td><?php echo $index + 1; ?></td>
                                                    <td><input type="text" class="form-control" name="items[<?php echo $index; ?>][designation]" value="<?php echo htmlspecialchars($item['designation']); ?>" required></td>
                                                    <td><input type="number" step="0.01" class="form-control quantity" name="items[<?php echo $index; ?>][quantity]" value="<?php echo $item['quantity']; ?>" required></td>
                                                    <td><input type="number" step="0.01" class="form-control unit-price" name="items[<?php echo $index; ?>][unit_price]" value="<?php echo $item['unit_price']; ?>" required></td>
                                                    <td><input type="number" step="0.01" class="form-control total-amount" name="items[<?php echo $index; ?>][total_amount]" value="<?php echo $item['total_amount']; ?>" readonly></td>
                                                    <td><input type="number" step="0.01" class="form-control amount-without-tax" name="items[<?php echo $index; ?>][amount_without_tax]" value="<?php echo $item['amount_without_tax']; ?>" readonly></td>
                                                    <td><input type="number" step="0.01" class="form-control vat-amount" name="items[<?php echo $index; ?>][vat_amount]" value="<?php echo $item['vat_amount']; ?>" readonly></td>
                                                    <td><input type="number" step="0.01" class="form-control total-with-tax" name="items[<?php echo $index; ?>][total_with_tax]" value="<?php echo $item['total_with_tax']; ?>" readonly></td>
                                                    <td><button type="button" class="btn btn-sm btn-danger" onclick="removeItem(this)"><i class="fas fa-trash"></i></button></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <td colspan="7"><strong>الإجمالي العام:</strong></td>
                                            <td><strong><span id="grandTotal">0.00</span></strong></td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إعدادات إضافية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="vat_rate" class="form-label">معدل الرسم على القيمة المضافة</label>
                            <select class="form-select" id="vat_rate" name="vat_rate">
                                <option value="15" <?php echo ($edit_data['vat_rate'] ?? '19') === '15' ? 'selected' : ''; ?>>15%</option>
                                <option value="19" <?php echo ($edit_data['vat_rate'] ?? '19') === '19' ? 'selected' : ''; ?>>19%</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3 d-flex align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="signature_enabled" name="signature_enabled" 
                                       <?php echo ($edit_data['signature_enabled'] ?? false) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="signature_enabled">
                                    تفعيل التوقيع
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="consultations.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary" onclick="clearForm()">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group me-2" role="group">
                                <button type="button" class="btn btn-info" onclick="previewConsultation()">
                                    <i class="fas fa-eye me-1"></i> معاينة قبل الطباعة
                                </button>
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                            </div>
                            
                            <!-- أزرار الإرسال -->
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary dropdown-toggle" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-share me-1"></i> إرسال
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('gmail')">
                                        <i class="fab fa-google me-2"></i> Gmail
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('outlook')">
                                        <i class="fab fa-microsoft me-2"></i> Outlook
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendEmail('yahoo')">
                                        <i class="fab fa-yahoo me-2"></i> Yahoo
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendWhatsApp()">
                                        <i class="fab fa-whatsapp me-2"></i> WhatsApp
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الاستشارات
                </h5>

                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2"
                           placeholder="بحث برقم الاستشارة أو البلد..."
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="consultations.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الاستشارة</th>
                                <th>التاريخ</th>
                                <th>البلد</th>
                                <th>العملة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>عدد العناصر</th>
                                <th>التوقيع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($consultations)): ?>
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($consultations as $consultation): ?>
                                    <?php
                                    $items = json_decode($consultation['items'], true) ?: [];
                                    $items_count = count($items);
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($consultation['consultation_number']); ?></strong>
                                        </td>
                                        <td><?php echo formatArabicDate($consultation['consultation_date']); ?></td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo htmlspecialchars($consultation['country']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $consultation['currency_type']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo number_format($consultation['total_amount'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo $items_count; ?> عنصر</span>
                                        </td>
                                        <td>
                                            <?php if ($consultation['signature_enabled']): ?>
                                                <span class="badge bg-success">مفعل</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير مفعل</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $consultation['id']; ?>"
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteConsultation(<?php echo $consultation['id']; ?>)"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button onclick="viewConsultation(<?php echo $consultation['id']; ?>)"
                                                        class="btn btn-outline-info" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button onclick="printConsultation(<?php echo $consultation['id']; ?>)"
                                                        class="btn btn-outline-success" title="طباعة">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let itemCounter = <?php echo $edit_data && $edit_data['items'] ? count($edit_data['items']) : 0; ?>;

// إضافة عنصر جديد
function addItem() {
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');

    row.innerHTML = `
        <td>${itemCounter + 1}</td>
        <td><input type="text" class="form-control" name="items[${itemCounter}][designation]" required></td>
        <td><input type="number" step="0.01" class="form-control quantity" name="items[${itemCounter}][quantity]" value="1" required></td>
        <td><input type="number" step="0.01" class="form-control unit-price" name="items[${itemCounter}][unit_price]" value="0" required></td>
        <td><input type="number" step="0.01" class="form-control total-amount" name="items[${itemCounter}][total_amount]" value="0" readonly></td>
        <td><input type="number" step="0.01" class="form-control amount-without-tax" name="items[${itemCounter}][amount_without_tax]" value="0" readonly></td>
        <td><input type="number" step="0.01" class="form-control vat-amount" name="items[${itemCounter}][vat_amount]" value="0" readonly></td>
        <td><input type="number" step="0.01" class="form-control total-with-tax" name="items[${itemCounter}][total_with_tax]" value="0" readonly></td>
        <td><button type="button" class="btn btn-sm btn-danger" onclick="removeItem(this)"><i class="fas fa-trash"></i></button></td>
    `;

    tbody.appendChild(row);
    itemCounter++;

    // إضافة مستمعي الأحداث للحقول الجديدة
    attachCalculationEvents(row);
    updateRowNumbers();
}

// حذف عنصر
function removeItem(button) {
    const row = button.closest('tr');
    row.remove();
    updateRowNumbers();
    calculateGrandTotal();
}

// تحديث أرقام الصفوف
function updateRowNumbers() {
    const rows = document.querySelectorAll('#itemsTableBody tr');
    rows.forEach((row, index) => {
        row.cells[0].textContent = index + 1;
    });
}

// إرفاق أحداث الحساب
function attachCalculationEvents(row) {
    const quantityInput = row.querySelector('.quantity');
    const unitPriceInput = row.querySelector('.unit-price');

    quantityInput.addEventListener('input', () => calculateRowTotal(row));
    unitPriceInput.addEventListener('input', () => calculateRowTotal(row));
}

// حساب إجمالي الصف
function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
    const unitPrice = parseFloat(row.querySelector('.unit-price').value) || 0;
    const vatRate = parseFloat(document.getElementById('vat_rate').value) || 19;

    const totalAmount = quantity * unitPrice;
    const amountWithoutTax = totalAmount / (1 + (vatRate / 100));
    const vatAmount = totalAmount - amountWithoutTax;

    row.querySelector('.total-amount').value = totalAmount.toFixed(2);
    row.querySelector('.amount-without-tax').value = amountWithoutTax.toFixed(2);
    row.querySelector('.vat-amount').value = vatAmount.toFixed(2);
    row.querySelector('.total-with-tax').value = totalAmount.toFixed(2);

    calculateGrandTotal();
}

// حساب الإجمالي العام
function calculateGrandTotal() {
    const totalWithTaxInputs = document.querySelectorAll('.total-with-tax');
    let grandTotal = 0;

    totalWithTaxInputs.forEach(input => {
        grandTotal += parseFloat(input.value) || 0;
    });

    document.getElementById('grandTotal').textContent = grandTotal.toFixed(2);
}

// تهيئة الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إرفاق أحداث الحساب للصفوف الموجودة
    document.querySelectorAll('#itemsTableBody tr').forEach(row => {
        attachCalculationEvents(row);
    });

    // حساب الإجمالي العام
    calculateGrandTotal();

    // مراقبة تغيير معدل الضريبة
    document.getElementById('vat_rate').addEventListener('change', function() {
        document.querySelectorAll('#itemsTableBody tr').forEach(row => {
            calculateRowTotal(row);
        });
    });
});

// مسح النموذج
function clearForm() {
    document.getElementById('itemsTableBody').innerHTML = '';
    itemCounter = 0;
    calculateGrandTotal();
}

// معاينة الاستشارة
function previewConsultation() {
    const form = document.getElementById('consultationForm');
    const formData = new FormData(form);

    // بناء جدول العناصر للمعاينة
    let itemsHtml = '<table class="table table-bordered"><thead><tr><th>الرقم</th><th>التعيين</th><th>الكمية</th><th>سعر الوحدة</th><th>الإجمالي</th></tr></thead><tbody>';

    const rows = document.querySelectorAll('#itemsTableBody tr');
    rows.forEach((row, index) => {
        const designation = row.querySelector('input[name*="[designation]"]').value;
        const quantity = row.querySelector('input[name*="[quantity]"]').value;
        const unitPrice = row.querySelector('input[name*="[unit_price]"]').value;
        const total = row.querySelector('input[name*="[total_with_tax]"]').value;

        if (designation) {
            itemsHtml += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${designation}</td>
                    <td>${quantity}</td>
                    <td>${unitPrice} ${formData.get('currency_type')}</td>
                    <td>${total} ${formData.get('currency_type')}</td>
                </tr>
            `;
        }
    });

    itemsHtml += `</tbody><tfoot><tr class="table-success"><td colspan="4"><strong>الإجمالي العام:</strong></td><td><strong>${document.getElementById('grandTotal').textContent} ${formData.get('currency_type')}</strong></td></tr></tfoot></table>`;

    const previewContent = `
        <div class="text-center mb-4">
            <h3>${'<?php echo INSTITUTION_NAME_AR; ?>'}</h3>
            <h4>${'<?php echo MINISTRY_NAME_AR; ?>'}</h4>
            <h5>${'<?php echo OFFICE_NAME_AR; ?>'}</h5>
            <hr>
            <h4>استشارة رقم: ${formData.get('consultation_number')}</h4>
        </div>

        <div class="row mb-3">
            <div class="col-6"><strong>التاريخ:</strong> ${formData.get('consultation_date')}</div>
            <div class="col-6"><strong>البلد:</strong> ${formData.get('country')}</div>
        </div>

        ${formData.get('custom_message') ? `<div class="mb-3"><strong>الرسالة:</strong><br>${formData.get('custom_message')}</div>` : ''}

        <h6>تفاصيل الاستشارة:</h6>
        ${itemsHtml}

        ${formData.get('signature_enabled') ? '<div class="mt-4 text-center"><div style="border: 2px solid black; height: 80px; line-height: 80px;">مكان التوقيع</div></div>' : ''}
    `;

    // إنشاء نافذة معاينة
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>معاينة الاستشارة</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
            <link href="assets/css/print.css" rel="stylesheet">
        </head>
        <body>
            <div class="container">
                ${previewContent}
                <div class="text-center mt-4 no-print">
                    <button onclick="window.print()" class="btn btn-primary">طباعة</button>
                    <button onclick="window.close()" class="btn btn-secondary">إغلاق</button>
                </div>
            </div>
        </body>
        </html>
    `);
    previewWindow.document.close();
}

// حذف الاستشارة
function deleteConsultation(id) {
    confirmDelete('هل أنت متأكد من حذف هذه الاستشارة؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// عرض تفاصيل الاستشارة
function viewConsultation(id) {
    fetch(`api/get_consultation.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const consultation = data.consultation;
                const items = JSON.parse(consultation.items || '[]');

                let itemsHtml = '<table class="table table-sm table-bordered"><thead><tr><th>التعيين</th><th>الكمية</th><th>سعر الوحدة</th><th>الإجمالي</th></tr></thead><tbody>';

                items.forEach(item => {
                    itemsHtml += `
                        <tr>
                            <td>${item.designation}</td>
                            <td>${item.quantity}</td>
                            <td>${item.unit_price} ${consultation.currency_type}</td>
                            <td>${item.total_with_tax} ${consultation.currency_type}</td>
                        </tr>
                    `;
                });

                itemsHtml += `</tbody><tfoot><tr class="table-success"><td colspan="3"><strong>الإجمالي:</strong></td><td><strong>${consultation.total_amount} ${consultation.currency_type}</strong></td></tr></tfoot></table>`;

                const detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>رقم الاستشارة:</strong> ${consultation.consultation_number}</p>
                            <p><strong>التاريخ:</strong> ${consultation.consultation_date}</p>
                            <p><strong>البلد:</strong> ${consultation.country}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>العملة:</strong> ${consultation.currency_type}</p>
                            <p><strong>معدل الضريبة:</strong> ${consultation.vat_rate}%</p>
                            <p><strong>التوقيع:</strong> ${consultation.signature_enabled ? 'مفعل' : 'غير مفعل'}</p>
                        </div>
                    </div>
                    ${consultation.custom_message ? `<div class="mt-3"><strong>الرسالة:</strong><br>${consultation.custom_message}</div>` : ''}
                    <div class="mt-3"><strong>العناصر:</strong></div>
                    ${itemsHtml}
                `;

                Swal.fire({
                    title: 'تفاصيل الاستشارة',
                    html: detailsHtml,
                    width: '800px',
                    confirmButtonText: 'إغلاق'
                });
            } else {
                showError('خطأ في جلب تفاصيل الاستشارة');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showError('خطأ في الاتصال بالخادم');
        });
}

// طباعة استشارة محددة
function printConsultation(id) {
    window.open(`print_consultation.php?id=${id}`, '_blank');
}
</script>

<?php include 'includes/footer.php'; ?>
