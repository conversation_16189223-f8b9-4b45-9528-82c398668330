<?php
// ملف اختبار التنزيل
require_once 'config/config.php';

// بيانات تجريبية
$data = [
    'receipt_number' => 'TEST-001',
    'request_date' => date('Y-m-d'),
    'beneficiary_directorate' => 'مديرية الامتحانات',
    'recipient_name' => 'أحمد محمد',
    'item_name' => 'أوراق A4',
    'quantity' => 100,
    'supply_number' => 'SUP-123',
    'notes' => 'طلب عاجل',
    'signature_warehouse_manager' => true,
    'signature_means_chief' => true,
    'signature_sub_director' => false,
    'signature_beneficiary' => true
];

$format = $_GET['format'] ?? 'html';

// تنسيق التاريخ
$data['formatted_date'] = date('d/m/Y', strtotime($data['request_date']));

// إنشاء محتوى HTML
function generateTestHTML($data) {
    $signaturesHtml = '';
    
    if ($data['signature_warehouse_manager'] || $data['signature_means_chief'] || 
        $data['signature_sub_director'] || $data['signature_beneficiary']) {
        
        $signaturesHtml = '<div style="margin-top: 50px;">';
        $signaturesHtml .= '<table width="100%" style="border-collapse: collapse;">';
        $signaturesHtml .= '<tr>';
        
        if ($data['signature_warehouse_manager']) {
            $signaturesHtml .= '<td style="text-align: center; padding: 20px; border: 1px solid #000; width: 33%;">';
            $signaturesHtml .= '<div style="height: 60px; border-bottom: 2px solid #000; margin-bottom: 10px;"></div>';
            $signaturesHtml .= '<strong>إمضاء وختم المكلف بتسيير المخزن</strong>';
            $signaturesHtml .= '</td>';
        }
        
        if ($data['signature_means_chief']) {
            $signaturesHtml .= '<td style="text-align: center; padding: 20px; border: 1px solid #000; width: 33%;">';
            $signaturesHtml .= '<div style="height: 60px; border-bottom: 2px solid #000; margin-bottom: 10px;"></div>';
            $signaturesHtml .= '<strong>إمضاء وختم رئيس مصلحة الوسائل</strong>';
            $signaturesHtml .= '</td>';
        }
        
        if ($data['signature_sub_director']) {
            $signaturesHtml .= '<td style="text-align: center; padding: 20px; border: 1px solid #000; width: 33%;">';
            $signaturesHtml .= '<div style="height: 60px; border-bottom: 2px solid #000; margin-bottom: 10px;"></div>';
            $signaturesHtml .= '<strong>إمضاء وختم المدير الفرعي للإدارة العامة</strong>';
            $signaturesHtml .= '</td>';
        }
        
        $signaturesHtml .= '</tr>';
        
        if ($data['signature_beneficiary']) {
            $signaturesHtml .= '<tr>';
            $signaturesHtml .= '<td colspan="3" style="text-align: center; padding: 20px; border: 1px solid #000;">';
            $signaturesHtml .= '<div style="height: 60px; border-bottom: 2px solid #000; margin-bottom: 10px;"></div>';
            $signaturesHtml .= '<strong>إمضاء المستفيد</strong>';
            $signaturesHtml .= '</td>';
            $signaturesHtml .= '</tr>';
        }
        
        $signaturesHtml .= '</table>';
        $signaturesHtml .= '</div>';
    }
    
    return '
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>طلب واستلام اللوازم المكتبية</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #2c5530; margin: 5px 0; }
            .header h2 { color: #4a7c59; margin: 5px 0; }
            .header h3 { color: #666; margin: 5px 0; }
            .title { text-align: center; font-size: 24px; font-weight: bold; margin: 30px 0; }
            .content { margin: 20px 0; }
            .field { margin: 10px 0; }
            .field strong { display: inline-block; width: 200px; }
            table { width: 100%; border-collapse: collapse; }
            td { padding: 10px; border: 1px solid #000; text-align: center; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>' . INSTITUTION_NAME_AR . '</h1>
            <h2>' . MINISTRY_NAME_AR . '</h2>
            <h3>' . OFFICE_NAME_AR . '</h3>
            <hr>
        </div>
        
        <div class="title">طلب واستلام اللوازم المكتبية</div>
        
        <div class="content">
            <div class="field"><strong>رقم الوصل:</strong> ' . htmlspecialchars($data['receipt_number']) . '</div>
            <div class="field"><strong>التاريخ:</strong> ' . $data['formatted_date'] . '</div>
            <div class="field"><strong>المديرية الفرعية أو المصلحة المستفيدة:</strong> ' . htmlspecialchars($data['beneficiary_directorate']) . '</div>
            <div class="field"><strong>اسم ولقب المستلم:</strong> ' . htmlspecialchars($data['recipient_name']) . '</div>
            <div class="field"><strong>اسم المادة:</strong> ' . htmlspecialchars($data['item_name']) . '</div>
            <div class="field"><strong>الكمية:</strong> ' . $data['quantity'] . '</div>
            ' . ($data['supply_number'] ? '<div class="field"><strong>رقم اللوازم:</strong> ' . htmlspecialchars($data['supply_number']) . '</div>' : '') . '
            ' . ($data['notes'] ? '<div class="field"><strong>الملاحظات:</strong> ' . htmlspecialchars($data['notes']) . '</div>' : '') . '
        </div>
        
        ' . $signaturesHtml . '
    </body>
    </html>';
}

// معالجة التنزيل حسب النوع
switch ($format) {
    case 'pdf':
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="طلب_لوازم_مكتبية_' . $data['receipt_number'] . '.html"');
        echo generateTestHTML($data);
        break;
        
    case 'docx':
    case 'doc':
        $content = generateTestHTML($data);
        $filename = 'طلب_لوازم_مكتبية_' . $data['receipt_number'] . '.' . $format;
        
        header('Content-Type: application/msword; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        
        echo $content;
        break;
        
    default:
        echo generateTestHTML($data);
}
?>
