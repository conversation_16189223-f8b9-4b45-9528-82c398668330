<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الهوامش والتوقيعات المحسنة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-file-signature me-2"></i>
                            اختبار الهوامش والتوقيعات المحسنة
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>التحسينات الجديدة:</h5>
                            <ul class="mb-0">
                                <li>✅ هوامش أكبر للطباعة (60mm من كل جانب)</li>
                                <li>✅ مساحات توقيع أكبر (120px ارتفاع)</li>
                                <li>✅ خطوط توقيع واضحة مع خلفية مميزة</li>
                                <li>✅ عرض الأرقام بدون فواصل أو نقاط عشرية</li>
                                <li>✅ تنسيق محسن للطباعة</li>
                                <li>✅ خطوط أكبر وأوضح</li>
                            </ul>
                        </div>
                        
                        <form id="testForm" method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="receipt_number" class="form-label">رقم الوصل *</label>
                                    <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                                           value="MARGIN-TEST-001" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="request_date" class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="request_date" name="request_date" 
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="beneficiary_directorate" class="form-label">المديرية المستفيدة *</label>
                                <input type="text" class="form-control" id="beneficiary_directorate" name="beneficiary_directorate" 
                                       value="مديرية الامتحانات والمسابقات" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="recipient_name" class="form-label">اسم المستلم *</label>
                                    <input type="text" class="form-control" id="recipient_name" name="recipient_name" 
                                           value="محمد أحمد بن علي" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="item_name" class="form-label">اسم المادة *</label>
                                    <input type="text" class="form-control" id="item_name" name="item_name" 
                                           value="أوراق A4 بيضاء" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="quantity" class="form-label">الكمية *</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           value="1500" min="1" required>
                                    <div class="form-text">سيتم عرض الرقم بدون فواصل: 1500</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="supply_number" class="form-label">رقم اللوازم</label>
                                    <input type="text" class="form-control" id="supply_number" name="supply_number" 
                                           value="SUP-2025-MARGIN-001">
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="notes" class="form-label">الملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">طلب عاجل للامتحانات النهائية - يرجى التسليم في أقرب وقت ممكن مع مراعاة جودة الورق المطلوبة للطباعة الرسمية.</textarea>
                            </div>
                            
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-signature me-2"></i>
                                        التوقيعات المحسنة (أفقية مع مساحات أكبر)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="signature_warehouse_manager" 
                                                       name="signature_warehouse_manager" checked>
                                                <label class="form-check-label" for="signature_warehouse_manager">
                                                    <strong>إمضاء وختم المكلف بتسيير المخزن</strong>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="signature_means_chief" 
                                                       name="signature_means_chief" checked>
                                                <label class="form-check-label" for="signature_means_chief">
                                                    <strong>إمضاء وختم رئيس مصلحة الوسائل</strong>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="signature_sub_director" 
                                                       name="signature_sub_director" checked>
                                                <label class="form-check-label" for="signature_sub_director">
                                                    <strong>إمضاء وختم المدير الفرعي للإدارة العامة</strong>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row mt-3">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="signature_beneficiary" 
                                                       name="signature_beneficiary" checked>
                                                <label class="form-check-label" for="signature_beneficiary">
                                                    <strong>إمضاء المستفيد</strong>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-info mt-3">
                                        <small>
                                            <i class="fas fa-info-circle me-1"></i>
                                            سيتم عرض التوقيعات في صفوف أفقية مع مساحات كبيرة للإمضاء والختم
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <h5 class="mb-3">اختبار التنزيل مع التحسينات:</h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-danger btn-lg" onclick="downloadFile('pdf')">
                                        <i class="fas fa-file-pdf me-2"></i> PDF محسن
                                    </button>
                                    <button type="button" class="btn btn-primary btn-lg" onclick="downloadFile('docx')">
                                        <i class="fas fa-file-word me-2"></i> DOCX محسن
                                    </button>
                                    <button type="button" class="btn btn-info btn-lg" onclick="downloadFile('doc')">
                                        <i class="fas fa-file-word me-2"></i> DOC محسن
                                    </button>
                                </div>
                                
                                <div class="mt-3">
                                    <button type="button" class="btn btn-success" onclick="previewDocument()">
                                        <i class="fas fa-eye me-2"></i> معاينة التحسينات
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-ruler me-2"></i>
                            مواصفات التحسينات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الهوامش والمساحات:</h6>
                                <ul class="list-unstyled">
                                    <li>📏 <strong>الهوامش:</strong> 60mm من كل جانب</li>
                                    <li>📐 <strong>مساحة التوقيع:</strong> 120px ارتفاع</li>
                                    <li>📝 <strong>حجم الخط:</strong> 18px للمحتوى، 16px للتوقيعات</li>
                                    <li>📄 <strong>تباعد الأسطر:</strong> 1.8 للوضوح</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>التنسيق والألوان:</h6>
                                <ul class="list-unstyled">
                                    <li>🎨 <strong>لون الرأس:</strong> #2c5530 (أخضر داكن)</li>
                                    <li>🖼️ <strong>خلفية التوقيع:</strong> #fafafa مع حدود منقطة</li>
                                    <li>📊 <strong>عرض الأرقام:</strong> بدون فواصل أو نقاط</li>
                                    <li>🖨️ <strong>تحسين الطباعة:</strong> هوامش 20mm للطباعة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function downloadFile(format) {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            // عرض مؤشر التحميل
            Swal.fire({
                title: `جاري إنشاء ملف ${format.toUpperCase()} محسن...`,
                html: 'مع الهوامش الكبيرة ومساحات التوقيع المحسنة',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // إنشاء نموذج مخفي للإرسال
            const hiddenForm = document.createElement('form');
            hiddenForm.method = 'POST';
            hiddenForm.action = `export/office_supplies_export_simple.php?format=${format}`;
            hiddenForm.style.display = 'none';
            
            // إضافة البيانات
            for (let [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                hiddenForm.appendChild(input);
            }
            
            document.body.appendChild(hiddenForm);
            hiddenForm.submit();
            document.body.removeChild(hiddenForm);
            
            // إخفاء مؤشر التحميل
            setTimeout(() => {
                Swal.close();
                Swal.fire({
                    icon: 'success',
                    title: 'تم إنشاء الملف!',
                    html: `
                        <div class="text-start">
                            <p>✅ ملف ${format.toUpperCase()} تم إنشاؤه بنجاح</p>
                            <p>✅ هوامش محسنة للطباعة</p>
                            <p>✅ مساحات توقيع كبيرة</p>
                            <p>✅ أرقام بدون فواصل</p>
                        </div>
                    `,
                    timer: 3000,
                    timerProgressBar: true
                });
            }, 2000);
        }
        
        function previewDocument() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            const previewHtml = `
                <div style="font-family: Arial; direction: rtl; text-align: right; padding: 20px; border: 2px solid #2c5530;">
                    <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #2c5530; padding-bottom: 20px;">
                        <h2 style="color: #2c5530; margin: 5px 0;">الجمهورية الجزائرية الديمقراطية الشعبية</h2>
                        <h3 style="color: #4a7c59; margin: 5px 0;">وزارة التربية الوطنية</h3>
                        <h4 style="color: #666; margin: 5px 0;">الديوان الوطني للامتحانات والمسابقات</h4>
                    </div>
                    
                    <div style="text-align: center; font-size: 24px; font-weight: bold; margin: 30px 0; color: #2c5530; border: 2px solid #2c5530; padding: 15px; background-color: #f8f9fa;">
                        طلب واستلام اللوازم المكتبية
                    </div>
                    
                    <div style="background-color: #fafafa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <p style="margin: 15px 0; padding: 10px 0; border-bottom: 1px dotted #999;"><strong style="color: #2c5530;">رقم الوصل:</strong> ${formData.get('receipt_number')}</p>
                        <p style="margin: 15px 0; padding: 10px 0; border-bottom: 1px dotted #999;"><strong style="color: #2c5530;">الكمية:</strong> ${parseInt(formData.get('quantity'))}</p>
                    </div>
                    
                    <div style="margin-top: 50px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="text-align: center; padding: 30px; border: 3px solid #000; width: 33%; vertical-align: top;">
                                    <div style="height: 80px; background-color: #fafafa; border: 1px dashed #ccc; margin-bottom: 15px;"></div>
                                    <div style="border-top: 3px solid #000; padding-top: 15px; font-weight: bold; color: #2c5530;">
                                        إمضاء وختم المكلف بتسيير المخزن
                                    </div>
                                </td>
                                <td style="text-align: center; padding: 30px; border: 3px solid #000; width: 33%; vertical-align: top;">
                                    <div style="height: 80px; background-color: #fafafa; border: 1px dashed #ccc; margin-bottom: 15px;"></div>
                                    <div style="border-top: 3px solid #000; padding-top: 15px; font-weight: bold; color: #2c5530;">
                                        إمضاء وختم رئيس مصلحة الوسائل
                                    </div>
                                </td>
                                <td style="text-align: center; padding: 30px; border: 3px solid #000; width: 33%; vertical-align: top;">
                                    <div style="height: 80px; background-color: #fafafa; border: 1px dashed #ccc; margin-bottom: 15px;"></div>
                                    <div style="border-top: 3px solid #000; padding-top: 15px; font-weight: bold; color: #2c5530;">
                                        إمضاء وختم المدير الفرعي للإدارة العامة
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" style="text-align: center; padding: 30px; border: 3px solid #000; vertical-align: top;">
                                    <div style="height: 80px; background-color: #fafafa; border: 1px dashed #ccc; margin-bottom: 15px;"></div>
                                    <div style="border-top: 3px solid #000; padding-top: 15px; font-weight: bold; color: #2c5530;">
                                        إمضاء المستفيد
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            `;
            
            Swal.fire({
                title: 'معاينة التحسينات',
                html: previewHtml,
                width: '900px',
                showConfirmButton: true,
                confirmButtonText: 'ممتاز!',
                customClass: {
                    htmlContainer: 'text-start'
                }
            });
        }
        
        // تحديث معاينة الكمية عند التغيير
        document.getElementById('quantity').addEventListener('input', function() {
            const value = parseInt(this.value) || 0;
            const preview = this.parentNode.querySelector('.form-text');
            preview.textContent = `سيتم عرض الرقم بدون فواصل: ${value}`;
        });
    </script>
</body>
</html>
