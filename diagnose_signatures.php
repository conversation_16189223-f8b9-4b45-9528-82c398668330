<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص مشكلة التوقيعات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; }</style>";
echo "</head>";
echo "<body>";
echo "<div class='container my-5'>";

echo "<h2 class='text-center mb-4'>🔍 تشخيص شامل لمشكلة التوقيعات</h2>";

try {
    // 1. فحص الجدول
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-primary text-white'><h5>1. فحص جدول office_supplies_requests</h5></div>";
    echo "<div class='card-body'>";
    
    $stmt = $db->query("DESCRIBE office_supplies_requests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table class='table table-sm'>";
    echo "<thead><tr><th>اسم العمود</th><th>النوع</th><th>افتراضي</th><th>حالة التوقيع</th></tr></thead>";
    echo "<tbody>";
    
    $signature_columns = ['signature_means_chief', 'signature_sub_director', 'signature_warehouse_manager', 'signature_beneficiary'];
    $found_signatures = [];
    
    foreach ($columns as $column) {
        $is_signature = in_array($column['Field'], $signature_columns);
        if ($is_signature) {
            $found_signatures[] = $column['Field'];
        }
        
        $class = $is_signature ? 'table-warning' : '';
        echo "<tr class='$class'>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>" . ($is_signature ? '✅ عمود توقيع' : '') . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    $missing_signatures = array_diff($signature_columns, $found_signatures);
    
    if (empty($missing_signatures)) {
        echo "<div class='alert alert-success'>✅ جميع أعمدة التوقيعات موجودة!</div>";
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<strong>❌ أعمدة التوقيعات المفقودة:</strong><br>";
        foreach ($missing_signatures as $missing) {
            echo "• $missing<br>";
        }
        echo "</div>";
    }
    
    echo "</div></div>";

    // 2. فحص البيانات الموجودة
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-info text-white'><h5>2. فحص البيانات الموجودة</h5></div>";
    echo "<div class='card-body'>";
    
    $stmt = $db->query("SELECT COUNT(*) as total FROM office_supplies_requests");
    $total = $stmt->fetch()['total'];
    
    echo "<p><strong>إجمالي السجلات:</strong> $total</p>";
    
    if ($total > 0) {
        // عرض آخر 3 سجلات
        $sql = "SELECT id, receipt_number";
        foreach ($found_signatures as $sig) {
            $sql .= ", $sig";
        }
        $sql .= " FROM office_supplies_requests ORDER BY id DESC LIMIT 3";
        
        $stmt = $db->query($sql);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h6>آخر 3 سجلات:</h6>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>ID</th><th>رقم الوصل</th>";
        foreach ($found_signatures as $sig) {
            echo "<th>$sig</th>";
        }
        echo "</tr></thead>";
        echo "<tbody>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>{$record['id']}</td>";
            echo "<td>{$record['receipt_number']}</td>";
            foreach ($found_signatures as $sig) {
                $value = $record[$sig] ?? 'NULL';
                $class = $value ? 'text-success' : 'text-danger';
                echo "<td class='$class'>$value</td>";
            }
            echo "</tr>";
        }
        echo "</tbody></table>";
    }
    
    echo "</div></div>";

    // 3. اختبار إدراج
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-warning text-dark'><h5>3. اختبار إدراج توقيعات</h5></div>";
    echo "<div class='card-body'>";
    
    if (!empty($found_signatures)) {
        try {
            $test_sql = "INSERT INTO office_supplies_requests (receipt_number, beneficiary_directorate, recipient_name, request_date, materials, notes";
            foreach ($found_signatures as $sig) {
                $test_sql .= ", $sig";
            }
            $test_sql .= ") VALUES (?, ?, ?, ?, ?, ?";
            foreach ($found_signatures as $sig) {
                $test_sql .= ", ?";
            }
            $test_sql .= ")";
            
            $stmt = $db->prepare($test_sql);
            $test_data = [
                'TEST-' . date('YmdHis'),
                'مكتب الاختبار',
                'اختبار التوقيعات',
                date('Y-m-d'),
                '[]',
                'اختبار'
            ];
            foreach ($found_signatures as $sig) {
                $test_data[] = 1; // تفعيل جميع التوقيعات
            }
            
            $stmt->execute($test_data);
            $test_id = $db->lastInsertId();
            
            echo "<div class='alert alert-success'>✅ تم إدراج سجل اختبار بنجاح! ID: $test_id</div>";
            
            // التحقق من السجل المدرج
            $check_sql = "SELECT id, receipt_number";
            foreach ($found_signatures as $sig) {
                $check_sql .= ", $sig";
            }
            $check_sql .= " FROM office_supplies_requests WHERE id = ?";
            
            $stmt = $db->prepare($check_sql);
            $stmt->execute([$test_id]);
            $test_record = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<h6>السجل المدرج:</h6>";
            echo "<table class='table table-sm'>";
            foreach ($test_record as $key => $value) {
                $class = in_array($key, $found_signatures) ? 'table-success' : '';
                echo "<tr class='$class'><td><strong>$key</strong></td><td>$value</td></tr>";
            }
            echo "</table>";
            
            // حذف السجل التجريبي
            $db->prepare("DELETE FROM office_supplies_requests WHERE id = ?")->execute([$test_id]);
            echo "<small class='text-muted'>تم حذف السجل التجريبي</small>";
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ فشل اختبار الإدراج: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠️ لا يمكن اختبار الإدراج - أعمدة التوقيعات مفقودة</div>";
    }
    
    echo "</div></div>";

    // 4. اختبار ملف الطباعة
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-success text-white'><h5>4. اختبار ملف الطباعة</h5></div>";
    echo "<div class='card-body'>";
    
    if ($total > 0) {
        // جلب آخر سجل
        $stmt = $db->query("SELECT * FROM office_supplies_requests ORDER BY id DESC LIMIT 1");
        $last_record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h6>آخر سجل في قاعدة البيانات:</h6>";
        echo "<p><strong>ID:</strong> {$last_record['id']}</p>";
        echo "<p><strong>رقم الوصل:</strong> {$last_record['receipt_number']}</p>";
        
        echo "<h6>حالة التوقيعات:</h6>";
        $signatures_status = [];
        foreach ($signature_columns as $sig) {
            $value = isset($last_record[$sig]) ? $last_record[$sig] : 'غير موجود';
            $signatures_status[$sig] = $value;
            $class = $value === 'غير موجود' ? 'text-danger' : ($value ? 'text-success' : 'text-warning');
            echo "<p class='$class'><strong>$sig:</strong> $value</p>";
        }
        
        echo "<div class='mt-3'>";
        echo "<a href='print_office_supplies_enhanced.php?id={$last_record['id']}' target='_blank' class='btn btn-primary'>";
        echo "اختبار الطباعة لهذا السجل";
        echo "</a>";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-info'>ℹ️ لا توجد سجلات لاختبار الطباعة</div>";
    }
    
    echo "</div></div>";

    // 5. التوصيات
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-dark text-white'><h5>5. التوصيات والحلول</h5></div>";
    echo "<div class='card-body'>";
    
    if (!empty($missing_signatures)) {
        echo "<div class='alert alert-danger'>";
        echo "<h6>🚨 مشكلة مؤكدة: أعمدة التوقيعات مفقودة</h6>";
        echo "<p><strong>الحل:</strong></p>";
        echo "<ol>";
        echo "<li><a href='fix_signatures_database.php' class='btn btn-success btn-sm'>تشغيل أداة الإصلاح التلقائي</a></li>";
        echo "<li>أو تشغيل الأوامر SQL يدوياً في phpMyAdmin</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success'>";
        echo "<h6>✅ أعمدة التوقيعات موجودة</h6>";
        echo "<p>المشكلة قد تكون في:</p>";
        echo "<ul>";
        echo "<li>عدم حفظ التوقيعات بشكل صحيح</li>";
        echo "<li>مشكلة في ملف الطباعة</li>";
        echo "<li>مشكلة في النموذج</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "</div></div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ خطأ في التشخيص:</h5>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div class='text-center'>";
echo "<a href='office_supplies.php' class='btn btn-primary me-2'>العودة للنظام</a>";
echo "<a href='fix_signatures_database.php' class='btn btn-success'>إصلاح المشكلة</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
