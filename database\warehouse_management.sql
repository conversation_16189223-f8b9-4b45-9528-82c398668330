-- نظام تسيير المخزن وإدارة العملاء
-- الديوان الوطني للامتحانات والمسابقات
-- قاعدة البيانات الرئيسية

CREATE DATABASE IF NOT EXISTS warehouse_management 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE warehouse_management;

-- الجدول الأول: بطاقة المخزون
CREATE TABLE inventory_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    card_number VARCHAR(50) UNIQUE NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    unit_of_measure VARCHAR(50) NOT NULL,
    entry_exit_receipt VARCHAR(100),
    supplier_beneficiary VARCHAR(255),
    entry_date DATE,
    entry_quantity DECIMAL(10,2) DEFAULT 0,
    exit_date DATE,
    exit_quantity DECIMAL(10,2) DEFAULT 0,
    current_stock DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_item_name (item_name),
    INDEX idx_card_number (card_number)
);

-- الجدول الثاني: الممونين
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    commercial_register VARCHAR(100),
    bank_postal_account VARCHAR(100),
    tax_id VARCHAR(50),
    statistical_id VARCHAR(50),
    address TEXT,
    phone VARCHAR(20),
    fax VARCHAR(20),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_full_name (full_name),
    INDEX idx_email (email)
);

-- الجدول الثالث: وصل دخول السلع إلى المخزن
CREATE TABLE goods_entry_receipts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_number VARCHAR(50) UNIQUE NOT NULL,
    directorate_name VARCHAR(255) DEFAULT 'المديرية الفرعية للادارة العامة',
    supplier_id INT,
    receipt_date DATE NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    item_number VARCHAR(50),
    inventory_number VARCHAR(50),
    use_item_number BOOLEAN DEFAULT TRUE,
    unit_price DECIMAL(10,2) NOT NULL,
    currency_type VARCHAR(10) DEFAULT 'DZD',
    total_amount DECIMAL(10,2) NOT NULL,
    amount_without_tax DECIMAL(10,2) NOT NULL,
    vat_rate ENUM('15', '19') DEFAULT '19',
    vat_amount DECIMAL(10,2) NOT NULL,
    total_with_tax DECIMAL(10,2) NOT NULL,
    signature_warehouse_manager BOOLEAN DEFAULT FALSE,
    signature_means_chief BOOLEAN DEFAULT FALSE,
    signature_sub_director BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
    INDEX idx_receipt_number (receipt_number),
    INDEX idx_receipt_date (receipt_date)
);

-- الجدول الرابع: طلب واستلام اللوازم المكتبية
CREATE TABLE office_supplies_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_number VARCHAR(50) UNIQUE NOT NULL,
    beneficiary_directorate VARCHAR(255) NOT NULL,
    recipient_name VARCHAR(255) NOT NULL,
    request_date DATE NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    supply_number VARCHAR(50),
    notes TEXT,
    signature_means_chief BOOLEAN DEFAULT FALSE,
    signature_sub_director BOOLEAN DEFAULT FALSE,
    signature_warehouse_manager BOOLEAN DEFAULT FALSE,
    signature_beneficiary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_receipt_number (receipt_number),
    INDEX idx_request_date (request_date)
);

-- الجدول الخامس: سند التحويل
CREATE TABLE transfer_vouchers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    receipt_number VARCHAR(50) UNIQUE NOT NULL,
    branch_name ENUM(
        'فرع الجزائر', 'فرع وهران', 'فرع باتنة', 'فرع بجاية', 
        'فرع البليدة', 'فرع عنابة', 'فرع أم البواقي', 'فرع سعيدة', 'فرع غرداية'
    ) NOT NULL,
    transfer_date DATE NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    supply_number VARCHAR(50),
    notes TEXT,
    signature_means_chief BOOLEAN DEFAULT FALSE,
    signature_sub_director BOOLEAN DEFAULT FALSE,
    signature_warehouse_manager BOOLEAN DEFAULT FALSE,
    signature_branch_director BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_receipt_number (receipt_number),
    INDEX idx_branch_name (branch_name)
);

-- الجدول السادس: الجرد العام
CREATE TABLE general_inventory (
    id INT AUTO_INCREMENT PRIMARY KEY,
    inventory_number VARCHAR(50) UNIQUE NOT NULL,
    registration_date DATE NOT NULL,
    designation VARCHAR(255) NOT NULL,
    source VARCHAR(255),
    value DECIMAL(12,2) NOT NULL,
    currency_type VARCHAR(10) DEFAULT 'DZD',
    allocation VARCHAR(255),
    office_id INT,
    exit_info TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_inventory_number (inventory_number),
    INDEX idx_designation (designation)
);

-- الجدول السابع: المديريات والمصالح والمكاتب
CREATE TABLE departments_services_offices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type ENUM('مديرية', 'مصلحة', 'مكتب', 'خلية') NOT NULL,
    parent_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES departments_services_offices(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_type (type)
);

-- الجدول الثامن: الاستشارات
CREATE TABLE consultations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    consultation_number VARCHAR(50) UNIQUE NOT NULL,
    consultation_date DATE NOT NULL,
    country VARCHAR(50) DEFAULT 'الجزائر',
    custom_message TEXT,
    items JSON, -- لحفظ عناصر الجدول
    total_amount DECIMAL(12,2) DEFAULT 0,
    currency_type VARCHAR(10) DEFAULT 'DZD',
    vat_rate ENUM('15', '19') DEFAULT '19',
    signature_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_consultation_number (consultation_number)
);

-- الجدول التاسع: فروع الديوان الوطني
CREATE TABLE national_office_branches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    branch_name ENUM(
        'فرع الجزائر', 'فرع وهران', 'فرع باتنة', 'فرع بجاية', 
        'فرع البليدة', 'فرع عنابة', 'فرع أم البواقي', 'فرع سعيدة', 'فرع غرداية'
    ) UNIQUE NOT NULL,
    phone VARCHAR(20),
    fax VARCHAR(20),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_branch_name (branch_name)
);

-- جدول إضافي: العملات المدعومة
CREATE TABLE currencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL,
    name_ar VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- جدول إضافي: إعدادات التنبيهات
CREATE TABLE alert_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    item_name VARCHAR(255) NOT NULL,
    minimum_stock DECIMAL(10,2) NOT NULL,
    alert_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_item_name (item_name)
);

-- إدراج البيانات الأساسية للمديريات والمصالح
INSERT INTO departments_services_offices (name, type) VALUES
('مدير الديوان', 'مديرية'),
('الأمانة الخاصة', 'مكتب'),
('الأمين العام', 'مديرية'),
('الأمانة العامة', 'مكتب'),
('المدير الفرعي للادارة العامة', 'مديرية'),
('امانة المدير الفرعي للادارة العامة', 'مكتب'),
('المدير الفرعي للبكالوريا', 'مديرية'),
('امانة المدير الفرعي للبكالوريا', 'مكتب'),
('المدير الفرعي للامتحانات و المسابقات المدرسية', 'مديرية'),
('المدير الفرعي للامتحانات و المسابقات المهنية', 'مديرية'),
('المديرية الفرعية للدراسات و التقويم', 'مديرية'),
('رئيس مصلحة الادارة العامة', 'مصلحة'),
('رئيس مصلحة الموظفين', 'مصلحة'),
('رئيس مصلحة الايرادات و النفقات', 'مصلحة'),
('رئيس مصلحة الوسائل', 'مصلحة'),
('رئيس مصلحة الشهادات', 'مصلحة'),
('مكتب العون المحاسب', 'مكتب'),
('مكتب الوكيل', 'مكتب'),
('مكتب الالتزامات', 'مكتب'),
('مكتب الصفقات', 'مكتب'),
('مكتب دفع الاجور', 'مكتب'),
('مكتب المخزني', 'مكتب'),
('الخلية المركزية', 'خلية'),
('مكتب الامتحانات المدرسية', 'مكتب'),
('مكتب الامتحانات المهنية', 'مكتب'),
('مكتب الاعلام الآلي', 'مكتب'),
('قاعة الاستقبال و التوجيه', 'مكتب');

-- إدراج البيانات الأساسية للفروع
INSERT INTO national_office_branches (branch_name, phone, fax) VALUES
('فرع الجزائر', '', ''),
('فرع وهران', '', ''),
('فرع باتنة', '', ''),
('فرع بجاية', '', ''),
('فرع البليدة', '', ''),
('فرع عنابة', '', ''),
('فرع أم البواقي', '', ''),
('فرع سعيدة', '', ''),
('فرع غرداية', '', '');

-- إدراج العملات المدعومة
INSERT INTO currencies (code, name_ar, name_en, symbol) VALUES
('DZD', 'دينار جزائري', 'Algerian Dinar', 'دج'),
('USD', 'دولار أمريكي', 'US Dollar', '$'),
('EUR', 'يورو', 'Euro', '€'),
('GBP', 'جنيه إسترليني', 'British Pound', '£'),
('JPY', 'ين ياباني', 'Japanese Yen', '¥'),
('RUB', 'روبل روسي', 'Russian Ruble', '₽'),
('SGD', 'دولار سنغافوري', 'Singapore Dollar', 'S$'),
('TRY', 'ليرة تركية', 'Turkish Lira', '₺'),
('LBP', 'ليرة لبنانية', 'Lebanese Pound', 'ل.ل'),
('ATS', 'شيلينغ نمساوي', 'Austrian Schilling', 'S'),
('MZN', 'متيكال موزمبيقي', 'Mozambican Metical', 'MT'),
('ZWL', 'دولار زيمبابوي', 'Zimbabwean Dollar', 'Z$'),
('ZMW', 'كواشا زامبي', 'Zambian Kwacha', 'ZK');

-- إنشاء العلاقات الخارجية الإضافية
ALTER TABLE general_inventory
ADD FOREIGN KEY (office_id) REFERENCES departments_services_offices(id) ON DELETE SET NULL;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_entry_date ON inventory_cards(entry_date);
CREATE INDEX idx_exit_date ON inventory_cards(exit_date);
CREATE INDEX idx_current_stock ON inventory_cards(current_stock);
CREATE INDEX idx_supplier_name ON suppliers(full_name);
CREATE INDEX idx_item_name_goods ON goods_entry_receipts(item_name);
CREATE INDEX idx_item_name_supplies ON office_supplies_requests(item_name);
CREATE INDEX idx_item_name_transfer ON transfer_vouchers(item_name);

-- إنشاء مشاهدات (Views) مفيدة
CREATE VIEW low_stock_items AS
SELECT
    ic.item_name,
    ic.current_stock,
    als.minimum_stock,
    ic.unit_of_measure
FROM inventory_cards ic
JOIN alert_settings als ON ic.item_name = als.item_name
WHERE ic.current_stock <= als.minimum_stock AND als.alert_enabled = TRUE;

CREATE VIEW monthly_inventory_report AS
SELECT
    DATE_FORMAT(entry_date, '%Y-%m') as month_year,
    COUNT(*) as total_entries,
    SUM(entry_quantity) as total_quantity_in,
    SUM(exit_quantity) as total_quantity_out
FROM inventory_cards
WHERE entry_date IS NOT NULL
GROUP BY DATE_FORMAT(entry_date, '%Y-%m')
ORDER BY month_year DESC;
