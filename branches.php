<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'إدارة فروع الديوان الوطني';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// قائمة الفروع المتاحة
$available_branches = [
    'فرع الجزائر', 'فرع وهران', 'فرع باتنة', 'فرع بجاية', 
    'فرع البليدة', 'فرع عنابة', 'فرع أم البواقي', 'فرع سعيدة', 'فرع غرداية'
];

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $branch_name = sanitizeInput($_POST['branch_name']);
    $phone = sanitizeInput($_POST['phone']);
    $fax = sanitizeInput($_POST['fax']);
    $address = sanitizeInput($_POST['address']);
    $is_active = isset($_POST['is_active']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE national_office_branches SET 
                    branch_name = ?, phone = ?, fax = ?, address = ?, is_active = ?
                    WHERE id = ?");
                
                $stmt->execute([$branch_name, $phone, $fax, $address, $is_active, $_POST['id']]);
                
                $success_message = 'تم تحديث بيانات الفرع بنجاح';
            } else {
                // التحقق من عدم وجود الفرع مسبقاً
                $stmt = $db->prepare("SELECT COUNT(*) FROM national_office_branches WHERE branch_name = ?");
                $stmt->execute([$branch_name]);
                
                if ($stmt->fetchColumn() > 0) {
                    $error_message = 'هذا الفرع موجود بالفعل';
                } else {
                    // إضافة جديد
                    $stmt = $db->prepare("INSERT INTO national_office_branches 
                        (branch_name, phone, fax, address, is_active) 
                        VALUES (?, ?, ?, ?, ?)");
                    
                    $stmt->execute([$branch_name, $phone, $fax, $address, $is_active]);
                    
                    $success_message = 'تم إضافة الفرع بنجاح';
                }
            }
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        // التحقق من وجود تحويلات مرتبطة بالفرع
        $stmt = $db->prepare("SELECT COUNT(*) FROM transfer_vouchers WHERE branch_name = (SELECT branch_name FROM national_office_branches WHERE id = ?)");
        $stmt->execute([$id]);
        $transfer_count = $stmt->fetchColumn();
        
        if ($transfer_count > 0) {
            $error_message = 'لا يمكن حذف هذا الفرع لأنه مرتبط بسندات تحويل موجودة';
        } else {
            $stmt = $db->prepare("DELETE FROM national_office_branches WHERE id = ?");
            $stmt->execute([$id]);
            $success_message = 'تم حذف الفرع بنجاح';
        }
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT * FROM national_office_branches WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب البيانات مع البحث
$where_clause = '';
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE branch_name LIKE ? OR phone LIKE ? OR address LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

$stmt = $db->prepare("SELECT nob.*, 
                     (SELECT COUNT(*) FROM transfer_vouchers WHERE branch_name = nob.branch_name) as transfer_count
                     FROM national_office_branches nob 
                     $where_clause 
                     ORDER BY nob.branch_name ASC");
$stmt->execute($params);
$branches = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    إدارة فروع الديوان الوطني للامتحانات والمسابقات
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate>
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="branch_name" class="form-label">اسم الفرع *</label>
                            <select class="form-select" id="branch_name" name="branch_name" required>
                                <option value="">اختر الفرع</option>
                                <?php foreach ($available_branches as $branch): ?>
                                    <option value="<?php echo $branch; ?>" 
                                            <?php echo ($edit_data['branch_name'] ?? '') === $branch ? 'selected' : ''; ?>>
                                        <?php echo $branch; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار اسم الفرع</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo $edit_data['phone'] ?? ''; ?>"
                                   pattern="^(\+213|0)[1-9][0-9]{7,8}$"
                                   title="مثال: 021123456 أو +213123456789">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="fax" class="form-label">رقم الفاكس</label>
                            <input type="tel" class="form-control" id="fax" name="fax" 
                                   value="<?php echo $edit_data['fax'] ?? ''; ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3 d-flex align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?php echo ($edit_data['is_active'] ?? true) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">
                                    الفرع نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?php echo $edit_data['address'] ?? ''; ?></textarea>
                    </div>
                    
                    <!-- الأزرار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="btn-group me-2" role="group">
                                <?php if ($edit_data): ?>
                                    <button type="submit" name="update" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                    <a href="branches.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i> إلغاء
                                    </a>
                                <?php else: ?>
                                    <button type="submit" name="save" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i> حفظ
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i> مسح
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-info" onclick="showBranchesMap()">
                                    <i class="fas fa-map me-1"></i> خريطة الفروع
                                </button>
                                <button type="button" class="btn btn-info" onclick="printPage()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الفروع
                </h5>
                
                <!-- البحث -->
                <form method="GET" class="d-flex">
                    <input type="text" name="search" class="form-control me-2" 
                           placeholder="بحث بالاسم أو الهاتف أو العنوان..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                        <a href="branches.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-body">
                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5><?php echo count($branches); ?></h5>
                                <small>إجمالي الفروع</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5><?php echo count(array_filter($branches, function($b) { return $b['is_active']; })); ?></h5>
                                <small>الفروع النشطة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5><?php echo array_sum(array_column($branches, 'transfer_count')); ?></h5>
                                <small>إجمالي التحويلات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5><?php echo count(array_filter($branches, function($b) { return !empty($b['phone']); })); ?></h5>
                                <small>فروع بأرقام هاتف</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>اسم الفرع</th>
                                <th>رقم الهاتف</th>
                                <th>رقم الفاكس</th>
                                <th>العنوان</th>
                                <th>عدد التحويلات</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($branches)): ?>
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد بيانات
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($branches as $branch): ?>
                                    <tr class="<?php echo !$branch['is_active'] ? 'table-secondary' : ''; ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($branch['branch_name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($branch['phone']): ?>
                                                <a href="tel:<?php echo $branch['phone']; ?>" class="text-decoration-none">
                                                    <i class="fas fa-phone me-1"></i>
                                                    <?php echo htmlspecialchars($branch['phone']); ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($branch['fax']): ?>
                                                <i class="fas fa-fax me-1"></i>
                                                <?php echo htmlspecialchars($branch['fax']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($branch['address']): ?>
                                                <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($branch['address']); ?>">
                                                    <?php echo htmlspecialchars($branch['address']); ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($branch['transfer_count'] > 0): ?>
                                                <span class="badge bg-success"><?php echo $branch['transfer_count']; ?> تحويل</span>
                                            <?php else: ?>
                                                <span class="text-muted">لا توجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($branch['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatArabicDate($branch['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $branch['id']; ?>" 
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($branch['transfer_count'] == 0): ?>
                                                    <button onclick="deleteBranch(<?php echo $branch['id']; ?>)" 
                                                            class="btn btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-outline-secondary" disabled title="لا يمكن الحذف - توجد تحويلات مرتبطة">
                                                        <i class="fas fa-ban"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button onclick="toggleBranchStatus(<?php echo $branch['id']; ?>, <?php echo $branch['is_active'] ? 'false' : 'true'; ?>)" 
                                                        class="btn btn-outline-warning" title="تغيير الحالة">
                                                    <i class="fas fa-toggle-<?php echo $branch['is_active'] ? 'on' : 'off'; ?>"></i>
                                                </button>
                                                <button onclick="contactBranch('<?php echo $branch['phone']; ?>', '<?php echo $branch['fax']; ?>')" 
                                                        class="btn btn-outline-success" title="اتصال">
                                                    <i class="fas fa-phone"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// حذف الفرع
function deleteBranch(id) {
    confirmDelete('هل أنت متأكد من حذف هذا الفرع؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// تغيير حالة الفرع
function toggleBranchStatus(id, newStatus) {
    const statusText = newStatus === 'true' ? 'تفعيل' : 'إلغاء تفعيل';

    confirmAction(`هل أنت متأكد من ${statusText} هذا الفرع؟`).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب AJAX لتغيير الحالة
            fetch('api/toggle_branch_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: id,
                    is_active: newStatus === 'true'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('تم تغيير حالة الفرع بنجاح');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showError(data.message || 'خطأ في تغيير الحالة');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showError('خطأ في الاتصال بالخادم');
            });
        }
    });
}

// الاتصال بالفرع
function contactBranch(phone, fax) {
    let options = [];

    if (phone) {
        options.push({
            text: `الاتصال على ${phone}`,
            value: 'phone',
            data: phone
        });
    }

    if (fax) {
        options.push({
            text: `إرسال فاكس إلى ${fax}`,
            value: 'fax',
            data: fax
        });
    }

    if (options.length === 0) {
        showWarning('لا توجد معلومات اتصال متاحة لهذا الفرع');
        return;
    }

    // عرض خيارات الاتصال
    const optionsHtml = options.map(option =>
        `<button class="btn btn-outline-primary btn-block mb-2" onclick="executeContact('${option.value}', '${option.data}')">${option.text}</button>`
    ).join('');

    Swal.fire({
        title: 'اختر طريقة الاتصال',
        html: optionsHtml,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'إلغاء'
    });
}

// تنفيذ الاتصال
function executeContact(type, data) {
    Swal.close();

    switch(type) {
        case 'phone':
            window.open(`tel:${data}`, '_blank');
            break;
        case 'fax':
            showInfo(`رقم الفاكس: ${data}`);
            break;
    }
}

// عرض خريطة الفروع
function showBranchesMap() {
    const mapHtml = `
        <div class="branches-map">
            <h6 class="mb-3">خريطة فروع الديوان الوطني للامتحانات والمسابقات</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="list-group">
                        <div class="list-group-item active">
                            <strong>الفروع الشرقية</strong>
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            فرع عنابة
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            فرع باتنة
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            فرع أم البواقي
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="list-group">
                        <div class="list-group-item active">
                            <strong>الفروع الغربية</strong>
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-success me-2"></i>
                            فرع وهران
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-success me-2"></i>
                            فرع سعيدة
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="list-group">
                        <div class="list-group-item active">
                            <strong>الفروع الوسطى</strong>
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-warning me-2"></i>
                            فرع الجزائر (المقر الرئيسي)
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-warning me-2"></i>
                            فرع البليدة
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-warning me-2"></i>
                            فرع بجاية
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="list-group">
                        <div class="list-group-item active">
                            <strong>الفروع الجنوبية</strong>
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-map-marker-alt text-info me-2"></i>
                            فرع غرداية
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    Swal.fire({
        title: 'خريطة الفروع',
        html: mapHtml,
        width: '800px',
        confirmButtonText: 'إغلاق',
        customClass: {
            htmlContainer: 'text-start'
        }
    });
}

// إحصائيات متقدمة للفروع
function showBranchStats() {
    fetch('api/get_branch_detailed_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let statsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إحصائيات عامة:</h6>
                            <ul class="list-unstyled">
                                <li><strong>إجمالي الفروع:</strong> ${data.stats.total}</li>
                                <li><strong>الفروع النشطة:</strong> ${data.stats.active}</li>
                                <li><strong>الفروع غير النشطة:</strong> ${data.stats.inactive}</li>
                                <li><strong>إجمالي التحويلات:</strong> ${data.stats.total_transfers}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>أكثر الفروع نشاطاً:</h6>
                            <ul class="list-unstyled">
                `;

                data.stats.top_branches.forEach(branch => {
                    statsHtml += `<li><strong>${branch.branch_name}:</strong> ${branch.transfer_count} تحويل</li>`;
                });

                statsHtml += `
                            </ul>
                        </div>
                    </div>
                `;

                Swal.fire({
                    title: 'إحصائيات مفصلة للفروع',
                    html: statsHtml,
                    icon: 'info',
                    confirmButtonText: 'موافق'
                });
            } else {
                showError('خطأ في جلب الإحصائيات');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showError('خطأ في الاتصال بالخادم');
        });
}

// التحقق من صحة رقم الهاتف الجزائري
document.getElementById('phone').addEventListener('blur', function() {
    const phone = this.value;
    if (phone && !isValidAlgerianPhone(phone)) {
        this.setCustomValidity('رقم الهاتف غير صحيح');
        showWarning('يرجى إدخال رقم هاتف جزائري صحيح');
    } else {
        this.setCustomValidity('');
    }
});

// دالة التحقق من صحة رقم الهاتف الجزائري
function isValidAlgerianPhone(phone) {
    // أرقام الهاتف الثابت: 021, 023, 024, 025, 026, 027, 028, 029, 031, 032, 033, 034, 035, 036, 037, 038, 039, 041, 043, 045, 046, 048, 049
    // أرقام الهاتف المحمول: 05, 06, 07
    const phoneRegex = /^(\+213|0)((2[1-9]|3[1-9]|4[1-9])[0-9]{6}|[567][0-9]{8})$/;
    return phoneRegex.test(phone);
}

// تصدير قائمة الفروع
function exportBranches(format) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `export/branches_export.php?format=${format}`;
    form.style.display = 'none';

    // إضافة فلاتر البحث الحالية
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput && searchInput.value) {
        const searchField = document.createElement('input');
        searchField.type = 'hidden';
        searchField.name = 'search';
        searchField.value = searchInput.value;
        form.appendChild(searchField);
    }

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// إضافة أزرار الإحصائيات والتصدير
document.querySelector('.card-header h5').insertAdjacentHTML('afterend', `
    <div class="mt-2">
        <div class="btn-group btn-group-sm me-2">
            <button type="button" class="btn btn-outline-info" onclick="showBranchStats()">
                <i class="fas fa-chart-pie me-1"></i> إحصائيات مفصلة
            </button>
        </div>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-success" onclick="exportBranches('excel')">
                <i class="fas fa-file-excel me-1"></i> Excel
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="exportBranches('pdf')">
                <i class="fas fa-file-pdf me-1"></i> PDF
            </button>
        </div>
    </div>
`);
</script>

<style>
.branches-map .list-group-item.active {
    background-color: #2c5530;
    border-color: #2c5530;
}

.branches-map .list-group-item {
    border: 1px solid #dee2e6;
    padding: 10px 15px;
}

.branches-map .fas.fa-map-marker-alt {
    font-size: 1.1em;
}
</style>

<?php include 'includes/footer.php'; ?>
