<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحسينات النهائية - طلب اللوازم المكتبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-gradient" style="background: linear-gradient(45deg, #28a745, #20c997);">
                        <h4 class="mb-0 text-white">
                            <i class="fas fa-check-double me-2"></i>
                            اختبار التحسينات النهائية - طلب واستلام اللوازم المكتبية
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h5><i class="fas fa-trophy me-2"></i>التحسينات المكتملة:</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ <strong>التوقيعات 2×2 أفقياً:</strong> صفين × عمودين منظمة</li>
                                        <li>✅ <strong>مساحات توقيع كبيرة:</strong> 120px للمواد القليلة، 80px للكثيرة</li>
                                        <li>✅ <strong>قائمة المديريات:</strong> تملأ تلقائياً من قاعدة البيانات</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li>✅ <strong>تصغير الخط التلقائي:</strong> حسب عدد المواد</li>
                                        <li>✅ <strong>طباعة في صفحة واحدة:</strong> مهما كان عدد المواد</li>
                                        <li>✅ <strong>تنسيق ذكي:</strong> يتكيف مع المحتوى</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <form id="testForm" method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="receipt_number" class="form-label">رقم الوصل *</label>
                                    <input type="text" class="form-control" id="receipt_number" name="receipt_number"
                                           value="FINAL-TEST-001" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="request_date" class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="request_date" name="request_date"
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="beneficiary_directorate" class="form-label">المديرية المستفيدة * (قائمة محسنة)</label>
                                    <select class="form-select" id="beneficiary_directorate" name="beneficiary_directorate" required>
                                        <option value="">اختر المديرية أو المصلحة</option>
                                        <option value="مديرية الامتحانات" selected>مديرية الامتحانات</option>
                                        <option value="مديرية المسابقات">مديرية المسابقات</option>
                                        <option value="مديرية الإدارة العامة">مديرية الإدارة العامة</option>
                                        <option value="مديرية الوسائل">مديرية الوسائل</option>
                                        <option value="مصلحة المحاسبة">مصلحة المحاسبة</option>
                                        <option value="مصلحة الموارد البشرية">مصلحة الموارد البشرية</option>
                                        <option value="مصلحة الصيانة">مصلحة الصيانة</option>
                                        <option value="مصلحة الأمن">مصلحة الأمن</option>
                                        <option value="مصلحة التكنولوجيا">مصلحة التكنولوجيا</option>
                                        <option value="مصلحة الاتصالات">مصلحة الاتصالات</option>
                                    </select>
                                    <div class="form-text text-success">
                                        <i class="fas fa-info-circle me-1"></i>
                                        تملأ تلقائياً من قسم المديريات والمصالح
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="recipient_name" class="form-label">اسم المستلم *</label>
                                    <input type="text" class="form-control" id="recipient_name" name="recipient_name"
                                           value="محمد أحمد علي بن سالم" required>
                                </div>
                            </div>

                            <!-- جدول المواد مع تنبيه الخط -->
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-table me-2"></i>
                                        جدول المواد (مع تصغير الخط التلقائي)
                                    </h6>
                                    <div>
                                        <span class="badge bg-light text-dark me-2" id="materialsCount">7 مواد</span>
                                        <button type="button" class="btn btn-sm btn-light" onclick="addMaterialRow()">
                                            <i class="fas fa-plus me-1"></i> إضافة مادة
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="materialsTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="8%">الرقم</th>
                                                    <th width="40%">اسم المادة</th>
                                                    <th width="12%">الكمية</th>
                                                    <th width="15%">الوحدة</th>
                                                    <th width="20%">رقم اللوازم</th>
                                                    <th width="5%">حذف</th>
                                                </tr>
                                            </thead>
                                            <tbody id="materialsTableBody">
                                                <!-- 7 مواد لاختبار تصغير الخط -->
                                                <tr><td>1</td><td><input type="text" class="form-control" name="materials[0][item_name]" value="أوراق A4 بيضاء عالية الجودة" required></td><td><input type="number" class="form-control" name="materials[0][quantity]" value="1000" min="1" required></td><td><input type="text" class="form-control" name="materials[0][unit]" value="ورقة"></td><td><input type="text" class="form-control" name="materials[0][supply_number]" value="SUP-001"></td><td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td></tr>
                                                <tr><td>2</td><td><input type="text" class="form-control" name="materials[1][item_name]" value="أقلام حبر زرقاء جودة عالية" required></td><td><input type="number" class="form-control" name="materials[1][quantity]" value="100" min="1" required></td><td><input type="text" class="form-control" name="materials[1][unit]" value="قلم"></td><td><input type="text" class="form-control" name="materials[1][supply_number]" value="SUP-002"></td><td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td></tr>
                                                <tr><td>3</td><td><input type="text" class="form-control" name="materials[2][item_name]" value="مجلدات بلاستيكية شفافة" required></td><td><input type="number" class="form-control" name="materials[2][quantity]" value="50" min="1" required></td><td><input type="text" class="form-control" name="materials[2][unit]" value="مجلد"></td><td><input type="text" class="form-control" name="materials[2][supply_number]" value="SUP-003"></td><td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td></tr>
                                                <tr><td>4</td><td><input type="text" class="form-control" name="materials[3][item_name]" value="دبابيس ورق معدنية صغيرة" required></td><td><input type="number" class="form-control" name="materials[3][quantity]" value="20" min="1" required></td><td><input type="text" class="form-control" name="materials[3][unit]" value="علبة"></td><td><input type="text" class="form-control" name="materials[3][supply_number]" value="SUP-004"></td><td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td></tr>
                                                <tr><td>5</td><td><input type="text" class="form-control" name="materials[4][item_name]" value="مشابك ورق ملونة متنوعة" required></td><td><input type="number" class="form-control" name="materials[4][quantity]" value="30" min="1" required></td><td><input type="text" class="form-control" name="materials[4][unit]" value="علبة"></td><td><input type="text" class="form-control" name="materials[4][supply_number]" value="SUP-005"></td><td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td></tr>
                                                <tr><td>6</td><td><input type="text" class="form-control" name="materials[5][item_name]" value="أقلام رصاص HB عادية" required></td><td><input type="number" class="form-control" name="materials[5][quantity]" value="75" min="1" required></td><td><input type="text" class="form-control" name="materials[5][unit]" value="قلم"></td><td><input type="text" class="form-control" name="materials[5][supply_number]" value="SUP-006"></td><td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td></tr>
                                                <tr><td>7</td><td><input type="text" class="form-control" name="materials[6][item_name]" value="ممحاة بيضاء ناعمة كبيرة" required></td><td><input type="number" class="form-control" name="materials[6][quantity]" value="25" min="1" required></td><td><input type="text" class="form-control" name="materials[6][unit]" value="قطعة"></td><td><input type="text" class="form-control" name="materials[6][supply_number]" value="SUP-007"></td><td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="alert alert-warning" id="fontSizeAlert">
                                        <small>
                                            <i class="fas fa-font me-1"></i>
                                            <span id="fontSizeInfo">تم اكتشاف 7 مواد - سيتم استخدام خط صغير (14px) لضمان الطباعة في صفحة واحدة.</span>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">الملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">طلب عاجل للامتحانات النهائية - يرجى التسليم في أقرب وقت ممكن مع مراعاة الجودة المطلوبة للمواد المكتبية والتأكد من توفر الكميات المطلوبة.</textarea>
                            </div>

                            <!-- التوقيعات 2×2 أفقياً -->
                            <div class="card mb-4">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-signature me-2"></i>
                                        التوقيعات الأفقية (2×2) - مساحات كبيرة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <strong>الترتيب الأفقي الجديد:</strong> التوقيعات ستظهر في صفين أفقيين، كل صف يحتوي على توقيعين جنباً إلى جنب مع مساحات كبيرة للإمضاء والختم
                                    </div>

                                    <div class="row">
                                        <!-- الصف الأول الأفقي -->
                                        <div class="col-12 mb-4">
                                            <h6 class="text-primary border-bottom pb-2">
                                                <i class="fas fa-arrow-right me-2"></i>الصف الأول الأفقي:
                                            </h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="card border-primary">
                                                        <div class="card-body text-center">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="signature_warehouse_manager"
                                                                       name="signature_warehouse_manager" checked>
                                                                <label class="form-check-label" for="signature_warehouse_manager">
                                                                    <strong>إمضاء وختم المكلف بتسيير المخزن</strong>
                                                                </label>
                                                            </div>
                                                            <div class="mt-2 p-3 bg-light border" style="height: 80px;">
                                                                <small class="text-muted">مساحة التوقيع والختم</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="card border-primary">
                                                        <div class="card-body text-center">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="signature_means_chief"
                                                                       name="signature_means_chief" checked>
                                                                <label class="form-check-label" for="signature_means_chief">
                                                                    <strong>إمضاء وختم رئيس مصلحة الوسائل</strong>
                                                                </label>
                                                            </div>
                                                            <div class="mt-2 p-3 bg-light border" style="height: 80px;">
                                                                <small class="text-muted">مساحة التوقيع والختم</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- الصف الثاني الأفقي -->
                                        <div class="col-12">
                                            <h6 class="text-success border-bottom pb-2">
                                                <i class="fas fa-arrow-right me-2"></i>الصف الثاني الأفقي:
                                            </h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="card border-success">
                                                        <div class="card-body text-center">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="signature_sub_director"
                                                                       name="signature_sub_director" checked>
                                                                <label class="form-check-label" for="signature_sub_director">
                                                                    <strong>إمضاء وختم المدير الفرعي للإدارة العامة</strong>
                                                                </label>
                                                            </div>
                                                            <div class="mt-2 p-3 bg-light border" style="height: 80px;">
                                                                <small class="text-muted">مساحة التوقيع والختم</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="card border-success">
                                                        <div class="card-body text-center">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="signature_beneficiary"
                                                                       name="signature_beneficiary" checked>
                                                                <label class="form-check-label" for="signature_beneficiary">
                                                                    <strong>إمضاء المستفيد</strong>
                                                                </label>
                                                            </div>
                                                            <div class="mt-2 p-3 bg-light border" style="height: 80px;">
                                                                <small class="text-muted">مساحة التوقيع</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <h5 class="mb-3">اختبار التحسينات النهائية:</h5>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-danger btn-lg" onclick="downloadFile('pdf')">
                                        <i class="fas fa-file-pdf me-2"></i> PDF محسن
                                    </button>
                                    <button type="button" class="btn btn-primary btn-lg" onclick="downloadFile('docx')">
                                        <i class="fas fa-file-word me-2"></i> DOCX محسن
                                    </button>
                                    <button type="button" class="btn btn-info btn-lg" onclick="downloadFile('doc')">
                                        <i class="fas fa-file-word me-2"></i> DOC محسن
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-success btn-lg" onclick="previewFinalDesign()">
                                        <i class="fas fa-eye me-2"></i> معاينة التحسينات النهائية
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <a href="office_supplies_new.php" class="btn btn-outline-primary">
                                        <i class="fas fa-external-link-alt me-2"></i> فتح النظام المحسن
                                    </a>
                                    <a href="test_new_design.html" class="btn btn-outline-secondary">
                                        <i class="fas fa-history me-2"></i> الاختبار السابق
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            ملخص التحسينات النهائية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-primary">التوقيعات الأفقية:</h6>
                                <ul class="list-unstyled">
                                    <li>✅ ترتيب 2×2 أفقي</li>
                                    <li>✅ مساحات كبيرة (120px/80px)</li>
                                    <li>✅ حدود واضحة</li>
                                    <li>✅ تنسيق احترافي</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-success">المديريات التلقائية:</h6>
                                <ul class="list-unstyled">
                                    <li>✅ قائمة منسدلة</li>
                                    <li>✅ تملأ من قاعدة البيانات</li>
                                    <li>✅ سهولة الاختيار</li>
                                    <li>✅ تحديث تلقائي</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-warning">الخط التكيفي:</h6>
                                <ul class="list-unstyled">
                                    <li>✅ 16px للمواد القليلة (≤3)</li>
                                    <li>✅ 15px للمواد المتوسطة (4-5)</li>
                                    <li>✅ 14px للمواد الكثيرة (>5)</li>
                                    <li>✅ طباعة في صفحة واحدة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let materialCounter = 7;

        // تحديث عداد المواد
        function updateMaterialsCount() {
            const tbody = document.getElementById('materialsTableBody');
            const count = tbody.children.length;
            document.getElementById('materialsCount').textContent = count + ' مواد';

            // تحديث معلومات حجم الخط
            const alertDiv = document.getElementById('fontSizeAlert');
            const infoSpan = document.getElementById('fontSizeInfo');

            let alertClass = 'alert-info';
            let infoText = '';

            if (count > 5) {
                alertClass = 'alert-warning';
                infoText = `تم اكتشاف ${count} مواد - سيتم استخدام خط صغير (14px) لضمان الطباعة في صفحة واحدة.`;
            } else if (count > 3) {
                alertClass = 'alert-info';
                infoText = `تم اكتشاف ${count} مواد - سيتم استخدام خط متوسط (15px) للحصول على أفضل تنسيق.`;
            } else {
                alertClass = 'alert-success';
                infoText = `تم اكتشاف ${count} مواد - سيتم استخدام الخط العادي (16px) مع تنسيق مريح.`;
            }

            alertDiv.className = `alert ${alertClass}`;
            infoSpan.textContent = infoText;
        }

        // إضافة صف مادة جديد
        function addMaterialRow() {
            const tbody = document.getElementById('materialsTableBody');
            const row = document.createElement('tr');

            row.innerHTML = `
                <td>${materialCounter + 1}</td>
                <td><input type="text" class="form-control" name="materials[${materialCounter}][item_name]" placeholder="أدخل اسم المادة" required></td>
                <td><input type="number" class="form-control" name="materials[${materialCounter}][quantity]" value="1" min="1" required></td>
                <td><input type="text" class="form-control" name="materials[${materialCounter}][unit]" placeholder="الوحدة"></td>
                <td><input type="text" class="form-control" name="materials[${materialCounter}][supply_number]" placeholder="رقم اللوازم"></td>
                <td><button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)"><i class="fas fa-trash"></i></button></td>
            `;

            tbody.appendChild(row);
            materialCounter++;
            updateRowNumbers();
            updateMaterialsCount();
        }

        // حذف صف مادة
        function removeMaterialRow(button) {
            const row = button.closest('tr');
            const tbody = document.getElementById('materialsTableBody');

            if (tbody.children.length > 1) {
                row.remove();
                updateRowNumbers();
                updateMaterialsCount();
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'تحذير!',
                    text: 'يجب أن يحتوي الجدول على مادة واحدة على الأقل'
                });
            }
        }

        // تحديث أرقام الصفوف
        function updateRowNumbers() {
            const rows = document.querySelectorAll('#materialsTableBody tr');
            rows.forEach((row, index) => {
                row.cells[0].textContent = index + 1;
            });
        }

        // تنزيل الملف
        function downloadFile(format) {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);

            Swal.fire({
                title: `جاري إنشاء ملف ${format.toUpperCase()} بالتحسينات النهائية...`,
                html: 'مع التوقيعات الأفقية والخط التكيفي والمديريات التلقائية',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            const hiddenForm = document.createElement('form');
            hiddenForm.method = 'POST';
            hiddenForm.action = `export/office_supplies_new_export.php?format=${format}`;
            hiddenForm.style.display = 'none';

            for (let [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                hiddenForm.appendChild(input);
            }

            document.body.appendChild(hiddenForm);
            hiddenForm.submit();
            document.body.removeChild(hiddenForm);

            setTimeout(() => {
                Swal.close();
                Swal.fire({
                    icon: 'success',
                    title: 'تم إنشاء الملف!',
                    html: `
                        <div class="text-start">
                            <p>✅ ملف ${format.toUpperCase()} بالتحسينات النهائية</p>
                            <p>✅ التوقيعات الأفقية 2×2</p>
                            <p>✅ الخط التكيفي حسب عدد المواد</p>
                            <p>✅ طباعة محسنة في صفحة واحدة</p>
                        </div>
                    `,
                    timer: 4000,
                    timerProgressBar: true
                });
            }, 2000);
        }

        // تحديث العدادات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateMaterialsCount();
        });
    </script>
</body>
</html>