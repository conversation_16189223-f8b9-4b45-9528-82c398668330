<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$message = '';
$test_id = null;

// معالجة إنشاء طلب اختبار
if ($_POST['action'] ?? '' === 'create_test') {
    try {
        // التحقق من وجود أعمدة التوقيعات
        $stmt = $db->query("SHOW COLUMNS FROM office_supplies_requests LIKE 'signature_%'");
        $signature_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($signature_columns)) {
            throw new Exception("أعمدة التوقيعات غير موجودة! يرجى تشغيل أداة الإصلاح أولاً.");
        }
        
        // إنشاء طلب اختبار
        $receipt_number = 'TEST-' . date('YmdHis');
        $materials = [
            ['item_name' => 'أقلام حبر جاف', 'quantity' => 10, 'supply_number' => 'SUP-001', 'notes' => 'اختبار'],
            ['item_name' => 'ورق A4', 'quantity' => 5, 'supply_number' => 'SUP-002', 'notes' => 'للطباعة']
        ];
        
        $sql = "INSERT INTO office_supplies_requests (
            receipt_number, beneficiary_directorate, recipient_name, request_date, 
            materials, notes, signature_means_chief, signature_sub_director, 
            signature_warehouse_manager, signature_beneficiary
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $receipt_number,
            'مكتب الاختبار',
            'مختبر النظام',
            date('Y-m-d'),
            json_encode($materials, JSON_UNESCAPED_UNICODE),
            'طلب اختبار للتوقيعات',
            $_POST['sig1'] ?? 0,  // رئيس مصلحة الوسائل
            $_POST['sig2'] ?? 0,  // المدير الفرعي
            $_POST['sig3'] ?? 0,  // المكلف بتسيير المخزن
            $_POST['sig4'] ?? 0   // المستفيد
        ]);
        
        $test_id = $db->lastInsertId();
        $message = "تم إنشاء طلب اختبار بنجاح! ID: $test_id";
        
    } catch (Exception $e) {
        $message = "خطأ: " . $e->getMessage();
    }
}

// جلب آخر طلب للاختبار
$last_request = null;
try {
    $stmt = $db->query("SELECT * FROM office_supplies_requests ORDER BY id DESC LIMIT 1");
    $last_request = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // تجاهل الخطأ
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التوقيعات البسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; }
        .signature-card { border: 2px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 8px; }
        .signature-card.selected { border-color: #28a745; background-color: #d4edda; }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-signature me-2"></i>
                            اختبار التوقيعات البسيط
                        </h3>
                    </div>
                    <div class="card-body">
                        
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo strpos($message, 'خطأ') === 0 ? 'danger' : 'success'; ?>">
                                <?php echo $message; ?>
                            </div>
                        <?php endif; ?>

                        <!-- نموذج إنشاء طلب اختبار -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>إنشاء طلب اختبار للتوقيعات</h5>
                                <form method="POST">
                                    <input type="hidden" name="action" value="create_test">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="signature-card">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="sig1" value="1" id="sig1" checked>
                                                    <label class="form-check-label fw-bold" for="sig1">
                                                        رئيس مصلحة الوسائل
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="signature-card">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="sig2" value="1" id="sig2">
                                                    <label class="form-check-label fw-bold" for="sig2">
                                                        المدير الفرعي للإدارة العامة
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="signature-card">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="sig3" value="1" id="sig3" checked>
                                                    <label class="form-check-label fw-bold" for="sig3">
                                                        المكلف بتسيير المخزن
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="signature-card">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="sig4" value="1" id="sig4" checked>
                                                    <label class="form-check-label fw-bold" for="sig4">
                                                        المستفيد
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center mt-3">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-plus me-2"></i>
                                            إنشاء طلب اختبار
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- عرض آخر طلب -->
                        <?php if ($last_request): ?>
                            <div class="row">
                                <div class="col-12">
                                    <h5>آخر طلب في قاعدة البيانات</h5>
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>ID:</strong> <?php echo $last_request['id']; ?></p>
                                                    <p><strong>رقم الوصل:</strong> <?php echo $last_request['receipt_number']; ?></p>
                                                    <p><strong>المديرية:</strong> <?php echo $last_request['beneficiary_directorate']; ?></p>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>حالة التوقيعات:</h6>
                                                    <?php
                                                    $signatures = [
                                                        'signature_means_chief' => 'رئيس مصلحة الوسائل',
                                                        'signature_sub_director' => 'المدير الفرعي للإدارة العامة',
                                                        'signature_warehouse_manager' => 'المكلف بتسيير المخزن',
                                                        'signature_beneficiary' => 'المستفيد'
                                                    ];
                                                    
                                                    foreach ($signatures as $key => $name) {
                                                        $value = isset($last_request[$key]) ? $last_request[$key] : 'غير موجود';
                                                        $icon = $value === '1' ? 'check-circle text-success' : ($value === '0' ? 'times-circle text-danger' : 'question-circle text-warning');
                                                        echo "<p><i class='fas fa-$icon me-2'></i>$name: $value</p>";
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                            
                                            <div class="text-center mt-3">
                                                <a href="print_office_supplies_enhanced.php?id=<?php echo $last_request['id']; ?>" 
                                                   target="_blank" class="btn btn-primary">
                                                    <i class="fas fa-print me-2"></i>
                                                    اختبار طباعة هذا الطلب
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- أزرار التشخيص -->
                        <div class="text-center mt-4">
                            <div class="btn-group-vertical">
                                <a href="diagnose_signatures.php" class="btn btn-info btn-lg mb-2">
                                    <i class="fas fa-search me-2"></i>
                                    تشخيص شامل للمشكلة
                                </a>
                                <a href="fix_signatures_database.php" class="btn btn-warning btn-lg mb-2">
                                    <i class="fas fa-tools me-2"></i>
                                    إصلاح قاعدة البيانات
                                </a>
                                <a href="office_supplies.php" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    العودة للنظام الأصلي
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث مظهر الكروت عند التحديد
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const card = this.closest('.signature-card');
                if (this.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
            
            // تطبيق الحالة الأولية
            const card = checkbox.closest('.signature-card');
            if (checkbox.checked) {
                card.classList.add('selected');
            }
        });
    </script>
</body>
</html>
