<?php
// ملف تصدير مبسط وموثوق
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تضمين الإعدادات
require_once '../config/config.php';

// التحقق من الطريقة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die('يجب استخدام POST method');
}

// التحقق من نوع التصدير
$format = $_GET['format'] ?? 'pdf';
if (!in_array($format, ['pdf', 'docx', 'doc'])) {
    die('نوع ملف غير مدعوم');
}

// جلب البيانات
$data = [
    'receipt_number' => sanitizeInput($_POST['receipt_number'] ?? ''),
    'request_date' => $_POST['request_date'] ?? date('Y-m-d'),
    'beneficiary_directorate' => sanitizeInput($_POST['beneficiary_directorate'] ?? ''),
    'recipient_name' => sanitizeInput($_POST['recipient_name'] ?? ''),
    'item_name' => sanitizeInput($_POST['item_name'] ?? ''),
    'quantity' => floatval($_POST['quantity'] ?? 0),
    'supply_number' => sanitizeInput($_POST['supply_number'] ?? ''),
    'notes' => sanitizeInput($_POST['notes'] ?? ''),
    'signature_warehouse_manager' => isset($_POST['signature_warehouse_manager']),
    'signature_means_chief' => isset($_POST['signature_means_chief']),
    'signature_sub_director' => isset($_POST['signature_sub_director']),
    'signature_beneficiary' => isset($_POST['signature_beneficiary'])
];

// التحقق من البيانات الأساسية
if (empty($data['receipt_number'])) {
    die('رقم الوصل مطلوب');
}

if (empty($data['item_name'])) {
    die('اسم المادة مطلوب');
}

// تنسيق التاريخ
$data['formatted_date'] = date('d/m/Y', strtotime($data['request_date']));

// إنشاء المحتوى
function createContent($data) {
    // قسم التوقيعات
    $signatures = [];
    if ($data['signature_warehouse_manager']) {
        $signatures[] = 'إمضاء وختم المكلف بتسيير المخزن';
    }
    if ($data['signature_means_chief']) {
        $signatures[] = 'إمضاء وختم رئيس مصلحة الوسائل';
    }
    if ($data['signature_sub_director']) {
        $signatures[] = 'إمضاء وختم المدير الفرعي للإدارة العامة';
    }
    if ($data['signature_beneficiary']) {
        $signatures[] = 'إمضاء المستفيد';
    }
    
    $signaturesHtml = '';
    if (!empty($signatures)) {
        $signaturesHtml = '<div style="margin-top: 80px; page-break-inside: avoid;">';
        $signaturesHtml .= '<table width="100%" style="border-collapse: collapse; margin-top: 30px;">';

        // الصف الأول - 3 توقيعات
        if (count($signatures) >= 3) {
            $signaturesHtml .= '<tr>';
            for ($i = 0; $i < min(3, count($signatures)); $i++) {
                $signaturesHtml .= '<td style="text-align: center; padding: 40px 20px; border: 3px solid #000; width: 33%; vertical-align: top;">';
                $signaturesHtml .= '<div class="signature-space"></div>';
                $signaturesHtml .= '<div class="signature-label">' . $signatures[$i] . '</div>';
                $signaturesHtml .= '</td>';
            }
            $signaturesHtml .= '</tr>';

            // الصف الثاني - التوقيعات المتبقية
            if (count($signatures) > 3) {
                $signaturesHtml .= '<tr>';
                for ($i = 3; $i < count($signatures); $i++) {
                    $signaturesHtml .= '<td colspan="3" style="text-align: center; padding: 40px 20px; border: 3px solid #000; vertical-align: top;">';
                    $signaturesHtml .= '<div class="signature-space"></div>';
                    $signaturesHtml .= '<div class="signature-label">' . $signatures[$i] . '</div>';
                    $signaturesHtml .= '</td>';
                }
                $signaturesHtml .= '</tr>';
            }
        } else {
            // أقل من 3 توقيعات
            $signaturesHtml .= '<tr>';
            foreach ($signatures as $signature) {
                $width = count($signatures) == 1 ? '100%' : (count($signatures) == 2 ? '50%' : '33%');
                $signaturesHtml .= '<td style="text-align: center; padding: 40px 20px; border: 3px solid #000; width: ' . $width . '; vertical-align: top;">';
                $signaturesHtml .= '<div class="signature-space"></div>';
                $signaturesHtml .= '<div class="signature-label">' . $signature . '</div>';
                $signaturesHtml .= '</td>';
            }
            $signaturesHtml .= '</tr>';
        }

        $signaturesHtml .= '</table>';
        $signaturesHtml .= '</div>';
    }
    
    return '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>طلب واستلام اللوازم المكتبية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px 60px;
            line-height: 1.8;
            direction: rtl;
            font-size: 16px;
        }
        .header {
            text-align: center;
            margin-bottom: 50px;
            border-bottom: 3px solid #2c5530;
            padding-bottom: 30px;
        }
        .header h1 {
            color: #2c5530;
            margin: 8px 0;
            font-size: 26px;
            font-weight: bold;
        }
        .header h2 {
            color: #4a7c59;
            margin: 8px 0;
            font-size: 22px;
            font-weight: 600;
        }
        .header h3 {
            color: #666;
            margin: 8px 0;
            font-size: 18px;
            font-weight: 500;
        }
        .title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            margin: 40px 0;
            color: #2c5530;
            border: 3px solid #2c5530;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .content {
            margin: 30px 0;
            font-size: 18px;
            background-color: #fafafa;
            padding: 30px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .field {
            margin: 20px 0;
            padding: 15px 0;
            border-bottom: 1px dotted #999;
            min-height: 25px;
        }
        .field strong {
            display: inline-block;
            width: 280px;
            color: #2c5530;
            font-weight: bold;
            font-size: 18px;
        }
        .field-value {
            font-size: 18px;
            color: #333;
            font-weight: 500;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 40px;
        }
        td {
            padding: 40px 20px;
            border: 3px solid #000;
            text-align: center;
            vertical-align: top;
            min-height: 150px;
        }
        .signature-space {
            height: 120px;
            margin-bottom: 25px;
            border: 1px dashed #ccc;
            background-color: #fafafa;
        }
        .signature-label {
            border-top: 3px solid #000;
            padding-top: 20px;
            font-weight: bold;
            font-size: 16px;
            color: #2c5530;
        }
        @media print {
            body {
                margin: 20mm 25mm;
                font-size: 14px;
            }
            .no-print { display: none; }
            .content {
                background-color: white;
                border: none;
                padding: 20px 0;
            }
            .field {
                font-size: 16px;
            }
            .signature-space {
                background-color: white;
                border: 1px dashed #999;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>' . INSTITUTION_NAME_AR . '</h1>
        <h2>' . MINISTRY_NAME_AR . '</h2>
        <h3>' . OFFICE_NAME_AR . '</h3>
    </div>
    
    <div class="title">طلب واستلام اللوازم المكتبية</div>
    
    <div class="content">
        <div class="field">
            <strong>رقم الوصل:</strong>
            <span class="field-value">' . htmlspecialchars($data['receipt_number']) . '</span>
        </div>
        <div class="field">
            <strong>التاريخ:</strong>
            <span class="field-value">' . $data['formatted_date'] . '</span>
        </div>
        <div class="field">
            <strong>المديرية الفرعية أو المصلحة المستفيدة:</strong>
            <span class="field-value">' . htmlspecialchars($data['beneficiary_directorate']) . '</span>
        </div>
        <div class="field">
            <strong>اسم ولقب المستلم:</strong>
            <span class="field-value">' . htmlspecialchars($data['recipient_name']) . '</span>
        </div>
        <div class="field">
            <strong>اسم المادة:</strong>
            <span class="field-value">' . htmlspecialchars($data['item_name']) . '</span>
        </div>
        <div class="field">
            <strong>الكمية:</strong>
            <span class="field-value">' . intval($data['quantity']) . '</span>
        </div>
        ' . ($data['supply_number'] ? '<div class="field"><strong>رقم اللوازم:</strong> <span class="field-value">' . htmlspecialchars($data['supply_number']) . '</span></div>' : '') . '
        ' . ($data['notes'] ? '<div class="field"><strong>الملاحظات:</strong> <span class="field-value">' . htmlspecialchars($data['notes']) . '</span></div>' : '') . '
    </div>
    
    ' . $signaturesHtml . '
    
    <div class="no-print" style="margin-top: 30px; text-align: center; border-top: 1px solid #ccc; padding-top: 20px;">
        <p style="color: #666; font-size: 14px;">تم إنشاء هذا المستند تلقائياً من نظام تسيير المخزن - ' . date('Y-m-d H:i:s') . '</p>
    </div>
</body>
</html>';
}

// إنشاء المحتوى
$content = createContent($data);

// تحديد نوع الملف والتنزيل
$filename = 'طلب_لوازم_مكتبية_' . $data['receipt_number'] . '_' . date('Y-m-d');

// تنظيف المخرجات
while (ob_get_level()) {
    ob_end_clean();
}

// تحديد headers حسب نوع الملف
switch ($format) {
    case 'pdf':
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.html"');
        break;
        
    case 'docx':
        header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        header('Content-Disposition: attachment; filename="' . $filename . '.docx"');
        break;
        
    case 'doc':
        header('Content-Type: application/msword');
        header('Content-Disposition: attachment; filename="' . $filename . '.doc"');
        break;
}

header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');
header('Expires: 0');

// إرسال المحتوى
echo $content;
exit;
?>
