<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنسيق رأس المؤسسة وأدوات التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* تنسيق رأس المؤسسة المحسن */
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 3px solid #000000 !important;
            padding-bottom: 15px;
            background-color: white !important;
        }
        .header h1 {
            color: #000000 !important;
            margin: 8px 0;
            font-size: 24px;
            font-weight: 900 !important; /* أقصى درجة سُمك */
            font-family: 'Arial Black', Arial, sans-serif !important;
            text-shadow: 1px 1px 0px #000000 !important;
            letter-spacing: 1px;
        }
        .header h2 {
            color: #000000 !important;
            margin: 8px 0;
            font-size: 20px;
            font-weight: 900 !important; /* أقصى درجة سُمك */
            font-family: 'Arial Black', Arial, sans-serif !important;
            text-shadow: 1px 1px 0px #000000 !important;
            letter-spacing: 0.5px;
        }
        .header h3 {
            color: #000000 !important;
            margin: 8px 0;
            font-size: 16px;
            font-weight: 900 !important; /* أقصى درجة سُمك */
            font-family: 'Arial Black', Arial, sans-serif !important;
            text-shadow: 1px 1px 0px #000000 !important;
            letter-spacing: 0.5px;
        }

        .form-title {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            margin: 15px 0;
            color: #2c5530;
            border: 2px solid #2c5530;
            padding: 10px;
            background-color: #f8f9fa;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .demo-table th, .demo-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        .demo-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .signatures-section {
            margin-top: 15px; /* تقليل المسافة بين الجدول والتوقيعات */
            page-break-inside: avoid;
        }
        .signatures-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 25px;
            font-size: 18px;
            color: #2c5530;
        }
        .signature-row {
            display: flex;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 100px;
            padding: 40px 0;
        }
        .signature-box {
            width: 30%;
            padding: 80px 20px;
            font-weight: bold;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            min-height: 150px;
            border: 1px solid #ddd;
            background-color: #fafafa;
        }
        .signature-box.right-align {
            text-align: right;
            background-color: #e8f5e8;
        }
        .signature-box.left-align {
            text-align: left;
            background-color: #e8e8f5;
        }
        .signature-line {
            border-bottom: 2px solid #000;
            margin-top: 30px;
            width: 80%;
        }
        .space-indicator {
            width: 40%;
            background-color: #fff3cd;
            border: 1px dashed #ffc107;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #856404;
        }

        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        @media print {
            body { margin: 5mm; }
            .no-print { display: none; }
            .header h1, .header h2, .header h3 {
                text-shadow: none !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- أدوات التحكم -->
        <div class="no-print card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-tools me-2"></i>🎛️ أدوات التحكم في التنسيق</h5>
            </div>
            <div class="card-body">
                <!-- تباعد الأسطر -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label fw-bold">📏 تباعد الأسطر:</label>
                    </div>
                    <div class="col-md-9">
                        <div class="btn-group" role="group">
                            <button onclick="changeLineSpacing(1.0)" class="btn btn-outline-secondary btn-sm">1.0</button>
                            <button onclick="changeLineSpacing(1.15)" class="btn btn-outline-secondary btn-sm">1.15</button>
                            <button onclick="changeLineSpacing(1.5)" class="btn btn-outline-secondary btn-sm active">1.5</button>
                            <button onclick="changeLineSpacing(2.0)" class="btn btn-outline-secondary btn-sm">2.0</button>
                        </div>
                    </div>
                </div>
                
                <!-- المسافة بين الجدول والتوقيعات -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label fw-bold">📐 المسافة بين الجدول والتوقيعات:</label>
                    </div>
                    <div class="col-md-9">
                        <div class="btn-group" role="group">
                            <button onclick="changeTableSignatureSpacing(5)" class="btn btn-outline-info btn-sm">قريب جداً (5px)</button>
                            <button onclick="changeTableSignatureSpacing(15)" class="btn btn-outline-info btn-sm active">قريب (15px)</button>
                            <button onclick="changeTableSignatureSpacing(30)" class="btn btn-outline-info btn-sm">متوسط (30px)</button>
                            <button onclick="changeTableSignatureSpacing(50)" class="btn btn-outline-info btn-sm">بعيد (50px)</button>
                        </div>
                    </div>
                </div>
                
                <!-- هوامش الصفحة -->
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label fw-bold">📄 هوامش الصفحة:</label>
                    </div>
                    <div class="col-md-9">
                        <div class="btn-group" role="group">
                            <button onclick="changePageMargins('10mm')" class="btn btn-outline-warning btn-sm">ضيق (10mm)</button>
                            <button onclick="changePageMargins('15mm')" class="btn btn-outline-warning btn-sm">متوسط (15mm)</button>
                            <button onclick="changePageMargins('20mm')" class="btn btn-outline-warning btn-sm active">عادي (20mm)</button>
                            <button onclick="changePageMargins('25mm')" class="btn btn-outline-warning btn-sm">واسع (25mm)</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- رأس المؤسسة المحسن -->
        <div class="header">
            <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
            <h2>وزارة التربية الوطنية</h2>
            <h3>الديوان الوطني للامتحانات والمسابقات</h3>
        </div>

        <div class="form-title">طلب واستلام اللوازم المكتبية</div>

        <!-- جدول تجريبي -->
        <table class="demo-table">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>اسم المادة</th>
                    <th>الكمية المطلوبة</th>
                    <th>الكمية المستلمة</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>أقلام حبر جاف</td>
                    <td>50</td>
                    <td>50</td>
                    <td>مستلم بالكامل</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>دفاتر مدرسية</td>
                    <td>100</td>
                    <td>100</td>
                    <td>مستلم بالكامل</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>أوراق A4</td>
                    <td>20</td>
                    <td>20</td>
                    <td>مستلم بالكامل</td>
                </tr>
            </tbody>
        </table>

        <!-- قسم التوقيعات -->
        <div class="signatures-section">

            
            <!-- الصف الأول -->
            <div class="signature-row">
                <div class="signature-box right-align">
                    إمضاء وختم رئيس مصلحة الوسائل
                    <div class="signature-line"></div>
                </div>
                <div class="space-indicator">
                    مسافة كبيرة<br>40% عرض
                </div>
                <div class="signature-box left-align">
                    إمضاء وختم المدير الفرعي للإدارة العامة
                    <div class="signature-line"></div>
                </div>
            </div>

            <!-- الصف الثاني -->
            <div class="signature-row">
                <div class="signature-box right-align">
                    إمضاء وختم المكلف بتسيير المخزن
                    <div class="signature-line"></div>
                </div>
                <div class="space-indicator">
                    مسافة كبيرة<br>40% عرض
                </div>
                <div class="signature-box left-align">
                    إمضاء وختم المستفيد
                    <div class="signature-line"></div>
                </div>
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="no-print text-center mt-4">
            <button onclick="window.print()" class="btn btn-primary btn-lg me-2">
                <i class="fas fa-print me-2"></i>طباعة للاختبار
            </button>
            <button onclick="resetFormatting()" class="btn btn-secondary btn-lg">
                <i class="fas fa-undo me-2"></i>إعادة تعيين
            </button>
        </div>

        <div class="info mt-4">
            <strong>✅ التحسينات المطبقة:</strong><br>
            🔸 رأس المؤسسة بخط غامق جداً (font-weight: 900)<br>
            🔸 خط Arial Black للوضوح الأقصى<br>
            🔸 ظل نصي لتحسين الوضوح على الشاشة<br>
            🔸 تباعد أحرف محسن للقراءة<br>
            🔸 حدود سوداء سميكة (3px)<br>
            🔸 أدوات تحكم تفاعلية في التنسيق<br>
            🔸 مسافات توقيعات محسنة (30% + 40% فراغ)
        </div>
    </div>

    <script>
        // تغيير تباعد الأسطر
        function changeLineSpacing(spacing) {
            document.body.style.lineHeight = spacing;
            const textElements = document.querySelectorAll('p, div, td, th, span');
            textElements.forEach(element => {
                element.style.lineHeight = spacing;
            });
            updateActiveButton('.btn-outline-secondary', spacing);
            showNotification('تم تغيير تباعد الأسطر إلى ' + spacing);
        }

        // تغيير المسافة بين الجدول والتوقيعات
        function changeTableSignatureSpacing(spacing) {
            const signaturesSection = document.querySelector('.signatures-section');
            if (signaturesSection) {
                signaturesSection.style.marginTop = spacing + 'px';
            }
            updateActiveButton('.btn-outline-info', spacing + 'px');
            showNotification('تم تغيير المسافة بين الجدول والتوقيعات إلى ' + spacing + 'px');
        }

        // تغيير هوامش الصفحة
        function changePageMargins(margin) {
            document.body.style.margin = margin;
            document.body.style.padding = margin;
            updateActiveButton('.btn-outline-warning', margin);
            showNotification('تم تغيير هوامش الصفحة إلى ' + margin);
        }

        // تحديث الزر النشط
        function updateActiveButton(selector, value) {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(button => {
                button.classList.remove('active');
                if (button.textContent.includes(value)) {
                    button.classList.add('active');
                }
            });
        }

        // إعادة تعيين التنسيق
        function resetFormatting() {
            document.body.style.lineHeight = '1.5';
            document.body.style.margin = '20px';
            document.body.style.padding = '';
            const signaturesSection = document.querySelector('.signatures-section');
            if (signaturesSection) {
                signaturesSection.style.marginTop = '15px';
            }
            
            // إعادة تعيين الأزرار النشطة
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector('.btn-outline-secondary:nth-child(3)').classList.add('active');
            document.querySelector('.btn-outline-info:nth-child(2)').classList.add('active');
            document.querySelector('.btn-outline-warning:nth-child(3)').classList.add('active');
            
            showNotification('تم إعادة تعيين جميع الإعدادات');
        }

        // إظهار رسالة تأكيد
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div class="alert alert-success alert-dismissible fade show position-fixed" 
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
