<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$font_size = $_GET['font_size'] ?? 'medium';

if (empty($id)) {
    die('معرف الطلب مطلوب');
}

// جلب بيانات الطلب
$stmt = $db->prepare("SELECT * FROM office_supplies_requests WHERE id = ?");
$stmt->execute([$id]);
$request = $stmt->fetch();

if (!$request) {
    die('الطلب غير موجود');
}

// فك تشفير المواد
$materials = json_decode($request['materials'], true) ?? [];

// تحديد أحجام الخط
$font_sizes = [
    'x12BOLD' => [
        'base' => '12px',
        'header' => '16px',
        'title' => '18px',
        'table' => '11px',
        'padding' => '6px',
        'bold' => true
    ],
    'x14BOLD' => [
        'base' => '14px',
        'header' => '18px',
        'title' => '20px',
        'table' => '13px',
        'padding' => '7px',
        'bold' => true
    ],
    'x16BOLD' => [
        'base' => '16px',
        'header' => '20px',
        'title' => '22px',
        'table' => '15px',
        'padding' => '8px',
        'bold' => true
    ],
    'x18BOLD' => [
        'base' => '18px',
        'header' => '22px',
        'title' => '24px',
        'table' => '17px',
        'padding' => '9px',
        'bold' => true
    ],
    'x20BOLD' => [
        'base' => '20px',
        'header' => '24px',
        'title' => '26px',
        'table' => '19px',
        'padding' => '10px',
        'bold' => true
    ],
    'medium' => [
        'base' => '12px',
        'header' => '16px',
        'title' => '18px',
        'table' => '11px',
        'padding' => '6px',
        'bold' => false
    ],
    'small' => [
        'base' => '10px',
        'header' => '14px',
        'title' => '16px',
        'table' => '9px',
        'padding' => '4px',
        'bold' => false
    ],
];

$current_size = $font_sizes[$font_size] ?? $font_sizes['medium'];

// تحديد حجم الخط تلقائ적으로 حسب عدد المواد
$materials_count = count($materials);
if ($materials_count > 15) {
    $current_size = $font_sizes['small'];
} elseif ($materials_count > 8) {
    $current_size = $font_sizes['medium'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة طلب اللوازم المكتبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 15px;
            direction: rtl;
            font-size: <?php echo $current_size['base']; ?>;
            line-height: 1.4;
            <?php if ($current_size['bold']): ?>
            font-weight: bold;
            <?php endif; ?>
        }
        .header { 
            text-align: center; 
            margin-bottom: 20px; 
            border-bottom: 2px solid #2c5530;
            padding-bottom: 15px;
        }
        .header h1 { 
            color: #2c5530; 
            margin: 3px 0; 
            font-size: <?php echo $current_size['header']; ?>;
        }
        .header h2 { 
            color: #4a7c59; 
            margin: 3px 0; 
            font-size: calc(<?php echo $current_size['header']; ?> - 2px);
        }
        .header h3 { 
            color: #666; 
            margin: 3px 0; 
            font-size: calc(<?php echo $current_size['header']; ?> - 4px);
        }
        .form-title { 
            text-align: center; 
            font-size: <?php echo $current_size['title']; ?>; 
            font-weight: bold; 
            margin: 15px 0; 
            color: #2c5530;
            border: 2px solid #2c5530;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .info-section {
            margin: 10px 0;
            padding: <?php echo $current_size['padding']; ?>;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .info-row {
            display: flex;
            margin: 5px 0;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
            color: #2c5530;
            font-size: <?php echo $current_size['base']; ?>;
        }
        .info-value {
            flex: 1;
            padding: 4px;
            border-bottom: 1px solid #ddd;
            font-size: <?php echo $current_size['base']; ?>;
        }
        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .materials-table th,
        .materials-table td {
            border: 1px solid #000;
            padding: <?php echo $current_size['padding']; ?>;
            text-align: center;
            font-size: <?php echo $current_size['table']; ?>;
        }
        .materials-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .signatures-section {
            margin-top: 40px;
            page-break-inside: avoid;
        }
        .signature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 30px;
            margin-bottom: 40px;
        }
        .signature-box {
            border-bottom: 1px solid #000;
            padding: 20px 10px;
            text-align: center;
            min-height: 100px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            font-size: <?php echo $current_size['base']; ?>;
            font-weight: bold;
        }
        @media print {
            body { margin: 5mm; font-size: <?php echo $current_size['table']; ?>; }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            border-top: 1px solid #ccc;
            padding-top: 10px;
            font-size: calc(<?php echo $current_size['table']; ?> - 1px);
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><?php echo INSTITUTION_NAME_AR; ?></h1>
        <h2><?php echo MINISTRY_NAME_AR; ?></h2>
        <h3><?php echo OFFICE_NAME_AR; ?></h3>
    </div>
    
    <div class="form-title">
        طلب واستلام اللوازم المكتبية
    </div>
    
    <!-- معلومات الطلب -->
    <div class="info-section">
        <div class="row">
            <div class="col-md-6">
                <div class="info-row">
                    <div class="info-label">رقم الوصل:</div>
                    <div class="info-value"><?php echo htmlspecialchars($request['receipt_number']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">التاريخ:</div>
                    <div class="info-value"><?php echo formatArabicDate($request['request_date']); ?></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-row">
                    <div class="info-label">المديرية المستفيدة:</div>
                    <div class="info-value"><?php echo htmlspecialchars($request['beneficiary_directorate']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">اسم المستلم:</div>
                    <div class="info-value"><?php echo htmlspecialchars($request['recipient_name']); ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- جدول المواد -->
    <table class="materials-table">
        <thead>
            <tr>
                <th width="8%">الرقم</th>
                <th width="50%">اسم المادة</th>
                <th width="15%">الكمية</th>
                <th width="12%">رقم اللوازم</th>
                <th width="15%">ملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($materials)): ?>
                <tr>
                    <td colspan="5">لا توجد مواد مسجلة</td>
                </tr>
            <?php else: ?>
                <?php foreach ($materials as $index => $material): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td style="text-align: right; font-weight: bold;">
                            <?php echo htmlspecialchars($material['item_name']); ?>
                        </td>
                        <td>
                            <strong><?php echo number_format($material['quantity'], 0); ?></strong>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($material['supply_number'] ?: '-'); ?>
                        </td>
                        <td>
                            <?php echo htmlspecialchars($material['notes'] ?? '-'); ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    
    <!-- الملاحظات -->
    <?php if ($request['notes']): ?>
        <div class="info-section">
            <div class="info-row">
                <div class="info-label">الملاحظات:</div>
                <div class="info-value"><?php echo nl2br(htmlspecialchars($request['notes'])); ?></div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- التوقيعات -->
    <div class="signatures-section">
        <h6 style="text-align: center; margin-bottom: 30px; font-size: <?php echo $current_size['base']; ?>;">
            التوقيعات والأختام
        </h6>
        
        <!-- الصف الأول -->
        <div class="signature-grid">
            <?php if ($request['signature_means_chief']): ?>
                <div class="signature-box">
                    إمضاء وختم رئيس مصلحة الوسائل
                </div>
            <?php else: ?>
                <div class="signature-box" style="color: #999; border-bottom: 1px dashed #999;">
                    رئيس مصلحة الوسائل<br><small>(غير مطلوب)</small>
                </div>
            <?php endif; ?>
            
            <?php if ($request['signature_sub_director']): ?>
                <div class="signature-box">
                    إمضاء وختم المدير الفرعي للإدارة العامة
                </div>
            <?php else: ?>
                <div class="signature-box" style="color: #999; border-bottom: 1px dashed #999;">
                    المدير الفرعي للإدارة العامة<br><small>(غير مطلوب)</small>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- الصف الثاني -->
        <div class="signature-grid">
            <?php if ($request['signature_warehouse_manager']): ?>
                <div class="signature-box">
                    إمضاء وختم المكلف بتسيير المخزن
                </div>
            <?php else: ?>
                <div class="signature-box" style="color: #999; border-bottom: 1px dashed #999;">
                    المكلف بتسيير المخزن<br><small>(غير مطلوب)</small>
                </div>
            <?php endif; ?>
            
            <?php if ($request['signature_beneficiary']): ?>
                <div class="signature-box">
                    إمضاء وختم المستفيد
                </div>
            <?php else: ?>
                <div class="signature-box" style="color: #999; border-bottom: 1px dashed #999;">
                    المستفيد<br><small>(غير مطلوب)</small>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- تذييل -->
    <div class="footer">
        تم إنشاء هذا المستند تلقائically من نظام إدارة اللوازم المكتبية - <?php echo date('Y-m-d H:i:s'); ?>
        <br>عدد المواد: <?php echo count($materials); ?> | حجم الخط: <?php echo $font_size; ?>
    </div>
    
    <!-- أزرار التحكم -->
    <div class="text-center mt-4 no-print">
        <div class="btn-group mb-3">
            <a href="?id=<?php echo $id; ?>&font_size=x12BOLD" class="btn btn-outline-secondary <?php echo $font_size == 'x12BOLD' ? 'active' : ''; ?>">
                <i class="fas fa-font me-2"></i>12 عريض
            </a>
            <a href="?id=<?php echo $id; ?>&font_size=x14BOLD" class="btn btn-outline-secondary <?php echo $font_size == 'x14BOLD' ? 'active' : ''; ?>">
                <i class="fas fa-font me-2"></i>14 عريض
            </a>
            <a href="?id=<?php echo $id; ?>&font_size=x16BOLD" class="btn btn-outline-secondary <?php echo $font_size == 'x16BOLD' ? 'active' : ''; ?>">
                <i class="fas fa-font me-2"></i>16 عريض
            </a>
            <a href="?id=<?php echo $id; ?>&font_size=x18BOLD" class="btn btn-outline-secondary <?php echo $font_size == 'x18BOLD' ? 'active' : ''; ?>">
                <i class="fas fa-font me-2"></i>18 عريض
            </a>
            <a href="?id=<?php echo $id; ?>&font_size=x20BOLD" class="btn btn-outline-secondary <?php echo $font_size == 'x20BOLD' ? 'active' : ''; ?>">
                <i class="fas fa-font me-2"></i>20 عريض
            </a>
        </div>
        <br>
        <button onclick="window.print()" class="btn btn-primary btn-lg me-2">
            <i class="fas fa-print me-2"></i>طباعة
        </button>
        <button onclick="window.close()" class="btn btn-secondary btn-lg me-2">
            <i class="fas fa-times me-2"></i>إغلاق
        </button>
        <a href="office_supplies.php" class="btn btn-info btn-lg">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <script>
        // طباعة تلقائية عند اختيار حجم خط جديد
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto_print') === '1') {
            window.onload = function() { 
                setTimeout(() => window.print(), 500); 
            }
        }
    </script>
</body>
</html>


