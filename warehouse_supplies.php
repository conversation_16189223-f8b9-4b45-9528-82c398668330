<?php
require_once 'config/config.php';
require_once 'config/database.php';

$page_title = 'اللوازم الموجودة في المخزن';
$database = new Database();
$db = $database->getConnection();

// معالجة العمليات
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';

// رسائل النظام
$success_message = '';
$error_message = '';

// إنشاء الجدول إذا لم يكن موجوداً
try {
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS warehouse_supplies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        item_name VARCHAR(255) NOT NULL,
        item_number VARCHAR(100) NOT NULL UNIQUE,
        old_item_number VARCHAR(100) DEFAULT NULL,
        category ENUM(
            'لوازم الورق',
            'مستهلكات الطبع والنسخ',
            'اللوازم المكتبية',
            'اللوازم الصيدلانية والوقائية',
            'لوازم السيارات ولواحقها',
            'الأثاث',
            'خاص بمتفوقي الامتحانات المدرسية'
        ) NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $db->exec($create_table_sql);
} catch (PDOException $e) {
    $error_message = 'خطأ في إنشاء الجدول: ' . $e->getMessage();
}

// معالجة الحفظ والتعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $item_name = sanitizeInput($_POST['item_name']);
    $item_number = sanitizeInput($_POST['item_number']);
    $old_item_number = sanitizeInput($_POST['old_item_number']);
    $category = sanitizeInput($_POST['category']);
    $notes = sanitizeInput($_POST['notes']);
    
    try {
        if (isset($_POST['save']) || isset($_POST['update'])) {
            if (isset($_POST['update']) && !empty($_POST['id'])) {
                // تحديث
                $stmt = $db->prepare("UPDATE warehouse_supplies SET 
                    item_name = ?, item_number = ?, old_item_number = ?, 
                    category = ?, notes = ?
                    WHERE id = ?");
                
                $stmt->execute([
                    $item_name, $item_number, $old_item_number,
                    $category, $notes, $_POST['id']
                ]);
                
                $success_message = 'تم تحديث بيانات المادة بنجاح';
            } else {
                // إضافة جديد
                $stmt = $db->prepare("INSERT INTO warehouse_supplies 
                    (item_name, item_number, old_item_number, category, notes) 
                    VALUES (?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $item_name, $item_number, $old_item_number,
                    $category, $notes
                ]);
                
                $success_message = 'تم إضافة المادة بنجاح';
            }
        }
    } catch (PDOException $e) {
        if ($e->getCode() == 23000) {
            $error_message = 'رقم المادة موجود مسبقاً، يرجى استخدام رقم آخر';
        } else {
            $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}

// معالجة الحذف
if ($action === 'delete' && !empty($id)) {
    try {
        $stmt = $db->prepare("DELETE FROM warehouse_supplies WHERE id = ?");
        $stmt->execute([$id]);
        $success_message = 'تم حذف المادة بنجاح';
    } catch (PDOException $e) {
        $error_message = 'خطأ في حذف البيانات: ' . $e->getMessage();
    }
}

// جلب البيانات للتعديل
$edit_data = null;
if ($action === 'edit' && !empty($id)) {
    $stmt = $db->prepare("SELECT * FROM warehouse_supplies WHERE id = ?");
    $stmt->execute([$id]);
    $edit_data = $stmt->fetch();
}

// جلب البيانات مع البحث والتصفية
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(item_name LIKE ? OR item_number LIKE ? OR old_item_number LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category_filter)) {
    $where_conditions[] = "category = ?";
    $params[] = $category_filter;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

$stmt = $db->prepare("SELECT * FROM warehouse_supplies $where_clause ORDER BY category, item_name ASC");
$stmt->execute($params);
$supplies = $stmt->fetchAll();

// إحصائيات سريعة
$stats_stmt = $db->query("SELECT category, COUNT(*) as count FROM warehouse_supplies GROUP BY category");
$category_stats = $stats_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// فئات اللوازم
$categories = [
    'لوازم الورق',
    'مستهلكات الطبع والنسخ',
    'اللوازم المكتبية',
    'اللوازم الصيدلانية والوقائية',
    'لوازم السيارات ولواحقها',
    'الأثاث',
    'خاص بمتفوقي الامتحانات المدرسية'
];

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-warehouse me-2"></i>
                    اللوازم الموجودة في المخزن
                </h4>
            </div>
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج الإدخال -->
                <form method="POST" class="needs-validation" novalidate id="suppliesForm">
                    <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-plus-circle me-2"></i>
                                <?php echo $edit_data ? 'تعديل المادة' : 'إضافة مادة جديدة'; ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="item_name" class="form-label">اسم المادة *</label>
                                    <input type="text" class="form-control" id="item_name" name="item_name" 
                                           value="<?php echo $edit_data['item_name'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">يرجى إدخال اسم المادة</div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="item_number" class="form-label">رقم المادة *</label>
                                    <input type="text" class="form-control" id="item_number" name="item_number" 
                                           value="<?php echo $edit_data['item_number'] ?? generateReferenceNumber('SUP-'); ?>" required>
                                    <div class="invalid-feedback">يرجى إدخال رقم المادة</div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="old_item_number" class="form-label">الرقم القديم للمادة</label>
                                    <input type="text" class="form-control" id="old_item_number" name="old_item_number" 
                                           value="<?php echo $edit_data['old_item_number'] ?? ''; ?>" 
                                           placeholder="اختياري">
                                </div>
                                
                                <div class="col-md-2 mb-3">
                                    <label for="category" class="form-label">الفئة *</label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="">اختر الفئة</option>
                                        <?php foreach ($categories as $cat): ?>
                                            <option value="<?php echo $cat; ?>" 
                                                    <?php echo ($edit_data['category'] ?? '') == $cat ? 'selected' : ''; ?>>
                                                <?php echo $cat; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار الفئة</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="notes" class="form-label">الملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="2"><?php echo $edit_data['notes'] ?? ''; ?></textarea>
                                </div>
                                
                                <div class="col-md-4 mb-3 d-flex align-items-end">
                                    <div class="w-100">
                                        <?php if ($edit_data): ?>
                                            <button type="submit" name="update" class="btn btn-primary btn-lg w-100 mb-2">
                                                <i class="fas fa-save me-2"></i> تحديث
                                            </button>
                                            <a href="warehouse_supplies.php" class="btn btn-secondary w-100">
                                                <i class="fas fa-times me-2"></i> إلغاء
                                            </a>
                                        <?php else: ?>
                                            <button type="submit" name="save" class="btn btn-success btn-lg w-100 mb-2">
                                                <i class="fas fa-save me-2"></i> حفظ
                                            </button>
                                            <button type="reset" class="btn btn-secondary w-100" onclick="clearForm()">
                                                <i class="fas fa-undo me-2"></i> مسح
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول البيانات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    قائمة اللوازم الموجودة في المخزن
                </h5>

                <div class="d-flex gap-2 flex-wrap">
                    <!-- البحث -->
                    <form method="GET" class="d-flex">
                        <input type="text" name="search" class="form-control me-2"
                               placeholder="بحث بالاسم أو الرقم..."
                               value="<?php echo htmlspecialchars($search); ?>" style="width: 200px;">

                        <select name="category" class="form-select me-2" style="width: 200px;">
                            <option value="">جميع الفئات</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat; ?>" <?php echo $category_filter == $cat ? 'selected' : ''; ?>>
                                    <?php echo $cat; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search"></i>
                        </button>

                        <?php if ($search || $category_filter): ?>
                            <a href="warehouse_supplies.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </form>

                    <!-- أزرار الطباعة -->
                    <?php if (!empty($supplies)): ?>
                        <div class="btn-group">
                            <button type="button" class="btn btn-warning dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="print_warehouse_supplies.php?type=all" target="_blank">
                                    <i class="fas fa-file-pdf me-2"></i>جميع الصفحات
                                </a></li>
                                <li><a class="dropdown-item" href="print_warehouse_supplies.php?type=current&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>" target="_blank">
                                    <i class="fas fa-file me-2"></i>الصفحة الحالية
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="showCustomPrintDialog()">
                                    <i class="fas fa-cog me-2"></i>اختيار مخصص
                                </a></li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th width="5%">#</th>
                                <th width="25%">اسم المادة</th>
                                <th width="12%">رقم المادة</th>
                                <th width="12%">الرقم القديم</th>
                                <th width="18%">الفئة</th>
                                <th width="15%">الملاحظات</th>
                                <th width="13%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($supplies)): ?>
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد لوازم مسجلة
                                        <?php if ($search || $category_filter): ?>
                                            <br><small>جرب تغيير معايير البحث</small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php $counter = 1; ?>
                                <?php foreach ($supplies as $supply): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo $counter++; ?></span>
                                        </td>
                                        <td>
                                            <div class="fw-bold text-primary"><?php echo htmlspecialchars($supply['item_name']); ?></div>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo formatArabicDate(date('Y-m-d', strtotime($supply['created_at']))); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info text-dark"><?php echo htmlspecialchars($supply['item_number']); ?></span>
                                        </td>
                                        <td>
                                            <?php if ($supply['old_item_number']): ?>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($supply['old_item_number']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $category_colors = [
                                                'لوازم الورق' => 'bg-primary',
                                                'مستهلكات الطبع والنسخ' => 'bg-success',
                                                'اللوازم المكتبية' => 'bg-info',
                                                'اللوازم الصيدلانية والوقائية' => 'bg-warning text-dark',
                                                'لوازم السيارات ولواحقها' => 'bg-danger',
                                                'الأثاث' => 'bg-dark',
                                                'خاص بمتفوقي الامتحانات المدرسية' => 'bg-purple text-white'
                                            ];
                                            $color_class = $category_colors[$supply['category']] ?? 'bg-secondary';
                                            ?>
                                            <span class="badge <?php echo $color_class; ?> text-wrap">
                                                <?php echo htmlspecialchars($supply['category']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($supply['notes']): ?>
                                                <small><?php echo htmlspecialchars(substr($supply['notes'], 0, 50)) . (strlen($supply['notes']) > 50 ? '...' : ''); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $supply['id']; ?>"
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="deleteSupply(<?php echo $supply['id']; ?>)"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <button onclick="viewSupply(<?php echo $supply['id']; ?>)"
                                                        class="btn btn-outline-info" title="معاينة">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button onclick="printSupply(<?php echo $supply['id']; ?>)"
                                                        class="btn btn-outline-secondary" title="طباعة">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة -->
                <?php if (!empty($supplies)): ?>
                    <div class="row mt-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h5><?php echo count($supplies); ?></h5>
                                    <small>إجمالي اللوازم</small>
                                </div>
                            </div>
                        </div>
                        <?php foreach (array_slice($categories, 0, 5) as $cat): ?>
                            <div class="col-md-2">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6><?php echo $category_stats[$cat] ?? 0; ?></h6>
                                        <small class="text-muted"><?php echo $cat; ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- مودال الطباعة المخصصة -->
<div class="modal fade" id="customPrintModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-print me-2"></i>
                    طباعة مخصصة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customPrintForm">
                    <div class="mb-3">
                        <label class="form-label">اختر الفئات للطباعة:</label>
                        <div class="row">
                            <?php foreach ($categories as $cat): ?>
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="<?php echo $cat; ?>"
                                               id="cat_<?php echo md5($cat); ?>" name="categories[]">
                                        <label class="form-check-label" for="cat_<?php echo md5($cat); ?>">
                                            <?php echo $cat; ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="print_range" class="form-label">نطاق الطباعة:</label>
                        <select class="form-select" id="print_range" name="print_range">
                            <option value="all">جميع العناصر</option>
                            <option value="range">نطاق محدد</option>
                        </select>
                    </div>

                    <div id="range_inputs" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="start_page" class="form-label">من صفحة:</label>
                                <input type="number" class="form-control" id="start_page" name="start_page" min="1" value="1">
                            </div>
                            <div class="col-md-6">
                                <label for="end_page" class="form-label">إلى صفحة:</label>
                                <input type="number" class="form-control" id="end_page" name="end_page" min="1" value="1">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="executeCustomPrint()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- مودال المعاينة -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>
                    معاينة المادة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printPreview()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// مسح النموذج
function clearForm() {
    document.getElementById('suppliesForm').reset();
    document.getElementById('item_number').value = '<?php echo generateReferenceNumber('SUP-'); ?>';
}

// حذف المادة
function deleteSupply(id) {
    confirmDelete('هل أنت متأكد من حذف هذه المادة؟').then((result) => {
        if (result.isConfirmed) {
            window.location.href = `?action=delete&id=${id}`;
        }
    });
}

// معاينة المادة
function viewSupply(id) {
    fetch(`get_supply_details.php?id=${id}`)
        .then(response => response.text())
        .then(data => {
            document.getElementById('previewContent').innerHTML = data;
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في تحميل البيانات', 'error');
        });
}

// طباعة مادة واحدة
function printSupply(id) {
    window.open(`print_warehouse_supplies.php?type=single&id=${id}`, '_blank');
}

// إظهار مودال الطباعة المخصصة
function showCustomPrintDialog() {
    new bootstrap.Modal(document.getElementById('customPrintModal')).show();
}

// تنفيذ الطباعة المخصصة
function executeCustomPrint() {
    const form = document.getElementById('customPrintForm');
    const formData = new FormData(form);

    const categories = [];
    document.querySelectorAll('input[name="categories[]"]:checked').forEach(cb => {
        categories.push(cb.value);
    });

    if (categories.length === 0) {
        showAlert('يرجى اختيار فئة واحدة على الأقل', 'warning');
        return;
    }

    const printRange = document.getElementById('print_range').value;
    const startPage = document.getElementById('start_page').value;
    const endPage = document.getElementById('end_page').value;

    let url = `print_warehouse_supplies.php?type=custom&categories=${encodeURIComponent(categories.join(','))}`;

    if (printRange === 'range') {
        url += `&start_page=${startPage}&end_page=${endPage}`;
    }

    window.open(url, '_blank');
    bootstrap.Modal.getInstance(document.getElementById('customPrintModal')).hide();
}

// طباعة المعاينة
function printPreview() {
    const content = document.getElementById('previewContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>طباعة المادة</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                ${content}
                <script>window.print();</script>
            </body>
        </html>
    `);
}

// إظهار/إخفاء حقول النطاق
document.addEventListener('DOMContentLoaded', function() {
    const printRange = document.getElementById('print_range');
    if (printRange) {
        printRange.addEventListener('change', function() {
            const rangeInputs = document.getElementById('range_inputs');
            if (rangeInputs) {
                if (this.value === 'range') {
                    rangeInputs.style.display = 'block';
                } else {
                    rangeInputs.style.display = 'none';
                }
            }
        });
    }
});

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include 'includes/footer.php'; ?>
