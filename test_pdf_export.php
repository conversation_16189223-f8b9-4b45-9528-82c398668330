<?php
// اختبار سريع لملف تصدير PDF
echo "اختبار ملف تصدير PDF...<br>";

// محاكاة البيانات
$request = [
    'id' => 999,
    'receipt_number' => 'TEST-PDF-001',
    'request_date' => date('Y-m-d'),
    'beneficiary_directorate' => 'مديرية الاختبارات',
    'recipient_name' => 'أحمد محمد',
    'notes' => 'اختبار تصدير PDF'
];

$materials = [
    [
        'item_name' => 'أقلام حبر',
        'quantity' => 10,
        'supply_number' => 'PEN-001',
        'notes' => 'للاختبار'
    ]
];

$font_size = '14BOLD';
$numeric_size = 14;

echo "البيانات جاهزة...<br>";
echo "رقم الوصل: " . $request['receipt_number'] . "<br>";
echo "عدد المواد: " . count($materials) . "<br>";

// اختبار تضمين الملف
try {
    echo "<br><a href='export_office_supplies_pdf.php?id=999&font_size=14BOLD' target='_blank'>اختبار تصدير PDF</a><br>";
    echo "✅ الملف جاهز للاختبار!";
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>
