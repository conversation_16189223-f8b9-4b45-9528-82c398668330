<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$id = $_GET['id'] ?? '';
$font_size = $_GET['font_size'] ?? '14BOLD';

if (empty($id)) {
    die('معرف الطلب مطلوب');
}

// جلب بيانات الطلب
$stmt = $db->prepare("SELECT * FROM office_supplies_requests WHERE id = ?");
$stmt->execute([$id]);
$request = $stmt->fetch();

if (!$request) {
    die('الطلب غير موجود');
}

// فك تشفير المواد
$materials = json_decode($request['materials'], true) ?? [];

// استخراج الحجم الرقمي
$numeric_size = (int) str_replace('BOLD', '', $font_size);

// إعداد headers للـ DOC
header('Content-Type: application/msword; charset=UTF-8');
header('Content-Disposition: attachment; filename="طلب_اللوازم_المكتبية_' . $request['receipt_number'] . '.doc"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');

// بدء المستند
echo "\xEF\xBB\xBF"; // UTF-8 BOM
?>
<html xmlns:o="urn:schemas-microsoft-com:office:office" 
      xmlns:w="urn:schemas-microsoft-com:office:word" 
      xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta name="ProgId" content="Word.Document">
    <meta name="Generator" content="Microsoft Word">
    <meta name="Originator" content="Microsoft Word">
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowInsertionsAndDeletions/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        @page {
            size: A4;
            margin: 2cm;
            mso-page-orientation: portrait;
        }
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            font-size: <?php echo $numeric_size; ?>pt;
            line-height: 1.4;
            color: #000000;
            font-weight: bold;
            margin: 0;
            padding: 0;
            mso-fareast-font-family: "Times New Roman";
            mso-ansi-language: AR-SA;
            mso-fareast-language: EN-US;
            mso-bidi-language: AR-SA;
        }
        .header {
            text-align: center;
            margin-bottom: 20pt;
            border-bottom: 2pt solid #000000;
            padding-bottom: 15pt;
        }
        .header h1, .header h2, .header h3 {
            color: #000000;
            font-weight: bold;
            margin: 5pt 0;
            mso-outline-level: 1;
        }
        .header h1 { 
            font-size: <?php echo ($numeric_size + 4); ?>pt; 
            mso-style-name: "Heading 1";
        }
        .header h2 { 
            font-size: <?php echo $numeric_size; ?>pt; 
            mso-style-name: "Heading 2";
        }
        .header h3 { 
            font-size: <?php echo ($numeric_size - 2); ?>pt; 
            mso-style-name: "Heading 3";
        }
        
        .form-title {
            text-align: center;
            font-size: <?php echo ($numeric_size + 6); ?>pt;
            font-weight: bold;
            margin: 15pt 0;
            color: #2c5530;
            border: 2pt solid #2c5530;
            padding: 10pt;
            background-color: #f8f9fa;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15pt 0;
            mso-table-layout-alt: fixed;
            mso-padding-alt: 0cm 5.4pt 0cm 5.4pt;
        }
        .info-table td {
            border: 1pt solid #000000;
            padding: 8pt;
            font-weight: bold;
            mso-border-alt: solid black .5pt;
        }
        .info-label {
            background-color: #f8f9fa;
            width: 25%;
            mso-shading: #f8f9fa;
        }
        
        .materials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15pt 0;
            mso-table-layout-alt: fixed;
            mso-padding-alt: 0cm 5.4pt 0cm 5.4pt;
        }
        .materials-table th,
        .materials-table td {
            border: 1pt solid #000000;
            padding: 6pt;
            text-align: center;
            font-weight: bold;
            mso-border-alt: solid black .5pt;
        }
        .materials-table th {
            background-color: #f8f9fa;
            mso-shading: #f8f9fa;
        }
        .materials-table td.item-name,
        .materials-table td.notes {
            text-align: right;
            mso-text-align-alt: right;
        }
        
        .signatures-section {
            margin-top: 40pt;
            page-break-inside: avoid;
        }
        .signatures-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 25pt;
            font-size: <?php echo ($numeric_size + 2); ?>pt;
        }
        .signature-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 200pt 0; /* زيادة المسافة الأفقية بين الخلايا بشكل كبير */
            margin-bottom: 100pt; /* زيادة المسافة بين الجداول */
        }
        .signature-box {
            width: 30%; /* تقليل العرض لزيادة المسافة */
            padding: 80pt 20pt; /* زيادة المسافة للإمضاءات بشكل كبير */
            font-weight: bold;
            vertical-align: bottom;
            border: none;
            min-height: 150pt; /* زيادة الارتفاع */
        }
        .signature-box.right-align {
            text-align: right;
            mso-text-align-alt: right;
        }
        .signature-box.left-align {
            text-align: left;
            mso-text-align-alt: left;
        }
        .signature-line {
            border-bottom: 1pt solid #000000;
            margin-top: 30pt;
            height: 1pt;
        }
        .separator {
            height: 40pt;
            border-bottom: 1pt dotted #cccccc;
            margin: 30pt 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
        <h2>وزارة التربية الوطنية</h2>
        <h3>الديوان الوطني للامتحانات والمسابقات</h3>
    </div>

    <div class="form-title">
        طلب واستلام اللوازم المكتبية
    </div>
    
    <table class="info-table">
        <tr>
            <td class="info-label">رقم الوصل:</td>
            <td><?php echo htmlspecialchars($request['receipt_number']); ?></td>
            <td class="info-label">التاريخ:</td>
            <td><?php echo date('Y/m/d', strtotime($request['request_date'])); ?></td>
        </tr>
        <tr>
            <td class="info-label">المديرية أو المصلحة:</td>
            <td><?php echo htmlspecialchars($request['beneficiary_directorate']); ?></td>
            <td class="info-label">اسم المستلم:</td>
            <td><?php echo htmlspecialchars($request['recipient_name']); ?></td>
        </tr>
    </table>
    
    <table class="materials-table">
        <thead>
            <tr>
                <th width="6%">الرقم</th>
                <th width="40%">اسم المادة</th>
                <th width="10%">الكمية</th>
                <th width="12%">رقم اللوازم</th>
                <th width="32%">الملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($materials)): ?>
                <tr>
                    <td colspan="5">لا توجد مواد مسجلة</td>
                </tr>
            <?php else: ?>
                <?php foreach ($materials as $index => $material): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td class="item-name"><?php echo htmlspecialchars($material['item_name']); ?></td>
                        <td><?php echo number_format($material['quantity'], 0); ?></td>
                        <td><?php echo htmlspecialchars($material['supply_number'] ?: '-'); ?></td>
                        <td class="notes"><?php echo htmlspecialchars($material['notes'] ?? '-'); ?></td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    
    <?php if ($request['notes']): ?>
        <table class="info-table">
            <tr>
                <td class="info-label" width="20%">الملاحظات العامة:</td>
                <td><?php echo nl2br(htmlspecialchars($request['notes'])); ?></td>
            </tr>
        </table>
    <?php endif; ?>
    
    <div class="signatures-section">
        <div class="signatures-title">التوقيعات والأختام</div>
        
        <table class="signature-table">
            <tr>
                <td class="signature-box right-align">
                    إمضاء وختم رئيس مصلحة الوسائل
                    <div class="signature-line"></div>
                </td>
                <!-- خلية فارغة لزيادة المسافة -->
                <td style="width: 40%; min-width: 300pt; border: none;"></td>
                <td class="signature-box left-align">
                    إمضاء وختم المدير الفرعي للإدارة العامة
                    <div class="signature-line"></div>
                </td>
            </tr>
        </table>

        <div class="separator" style="height: 80pt; margin: 60pt 0;"></div>

        <table class="signature-table">
            <tr>
                <td class="signature-box right-align">
                    إمضاء وختم المكلف بتسيير المخزن
                    <div class="signature-line"></div>
                </td>
                <!-- خلية فارغة لزيادة المسافة -->
                <td style="width: 40%; min-width: 300pt; border: none;"></td>
                <td class="signature-box left-align">
                    إمضاء وختم المستفيد
                    <div class="signature-line"></div>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
