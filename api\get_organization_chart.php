<?php
require_once '../config/config.php';
require_once '../config/database.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب جميع الوحدات
    $stmt = $db->query("SELECT id, name, type, parent_id, is_active 
                       FROM departments_services_offices 
                       ORDER BY type, name ASC");
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // بناء الهيكل الهرمي
    function buildHierarchy($departments, $parentId = null) {
        $result = [];
        
        foreach ($departments as $dept) {
            if ($dept['parent_id'] == $parentId) {
                $dept['children'] = buildHierarchy($departments, $dept['id']);
                $result[] = $dept;
            }
        }
        
        return $result;
    }
    
    $hierarchy = buildHierarchy($departments);
    
    echo json_encode([
        'success' => true,
        'chart' => $hierarchy
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم'
    ], JSON_UNESCAPED_UNICODE);
}
?>
