<?php
// ملف فحص حالة قاعدة البيانات
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$status = [];

try {
    // فحص جدول office_supplies_requests
    $stmt = $db->query("DESCRIBE office_supplies_requests");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $status['table_exists'] = true;
    $status['materials_column'] = in_array('materials', $columns);
    $status['unit_column'] = in_array('unit', $columns);
    $status['columns'] = $columns;
    
    // فحص جدول directorates_services
    try {
        $stmt = $db->query("SELECT COUNT(*) FROM directorates_services");
        $status['directorates_table'] = true;
        $status['directorates_count'] = $stmt->fetchColumn();
    } catch (PDOException $e) {
        $status['directorates_table'] = false;
        $status['directorates_count'] = 0;
    }
    
    // فحص البيانات الموجودة
    $stmt = $db->query("SELECT COUNT(*) FROM office_supplies_requests");
    $status['total_records'] = $stmt->fetchColumn();
    
    if ($status['materials_column']) {
        $stmt = $db->query("SELECT COUNT(*) FROM office_supplies_requests WHERE materials IS NOT NULL");
        $status['records_with_materials'] = $stmt->fetchColumn();
    } else {
        $status['records_with_materials'] = 0;
    }
    
} catch (PDOException $e) {
    $status['error'] = $e->getMessage();
    $status['table_exists'] = false;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص حالة قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            فحص حالة قاعدة البيانات
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($status['error'])): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الاتصال:</h5>
                                <p><?php echo $status['error']; ?></p>
                            </div>
                        <?php else: ?>
                            
                            <!-- حالة الجدول الرئيسي -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">جدول طلبات اللوازم المكتبية</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li>
                                                    <?php if ($status['table_exists']): ?>
                                                        <i class="fas fa-check-circle text-success me-2"></i>الجدول موجود
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle text-danger me-2"></i>الجدول غير موجود
                                                    <?php endif; ?>
                                                </li>
                                                <li>
                                                    <?php if ($status['materials_column']): ?>
                                                        <i class="fas fa-check-circle text-success me-2"></i>عمود materials موجود
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle text-danger me-2"></i>عمود materials غير موجود
                                                    <?php endif; ?>
                                                </li>
                                                <li>
                                                    <?php if ($status['unit_column']): ?>
                                                        <i class="fas fa-check-circle text-success me-2"></i>عمود unit موجود
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle text-warning me-2"></i>عمود unit غير موجود
                                                    <?php endif; ?>
                                                </li>
                                                <li>
                                                    <i class="fas fa-database me-2 text-info"></i>
                                                    إجمالي السجلات: <?php echo $status['total_records']; ?>
                                                </li>
                                                <li>
                                                    <i class="fas fa-list me-2 text-info"></i>
                                                    سجلات بمواد JSON: <?php echo $status['records_with_materials']; ?>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">جدول المديريات والمصالح</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li>
                                                    <?php if ($status['directorates_table']): ?>
                                                        <i class="fas fa-check-circle text-success me-2"></i>الجدول موجود
                                                    <?php else: ?>
                                                        <i class="fas fa-times-circle text-danger me-2"></i>الجدول غير موجود
                                                    <?php endif; ?>
                                                </li>
                                                <li>
                                                    <i class="fas fa-building me-2 text-info"></i>
                                                    عدد المديريات/المصالح: <?php echo $status['directorates_count']; ?>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- أعمدة الجدول -->
                            <?php if ($status['table_exists']): ?>
                                <div class="card mb-3">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">أعمدة الجدول الحالية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <?php foreach (array_chunk($status['columns'], ceil(count($status['columns'])/3)) as $chunk): ?>
                                                <div class="col-md-4">
                                                    <ul class="list-unstyled">
                                                        <?php foreach ($chunk as $column): ?>
                                                            <li>
                                                                <?php if (in_array($column, ['materials', 'unit'])): ?>
                                                                    <i class="fas fa-star text-warning me-2"></i>
                                                                <?php else: ?>
                                                                    <i class="fas fa-circle text-muted me-2" style="font-size: 8px;"></i>
                                                                <?php endif; ?>
                                                                <code><?php echo $column; ?></code>
                                                            </li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- التوصيات -->
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-lightbulb me-2"></i>التوصيات:</h6>
                                <ul class="mb-0">
                                    <?php if (!$status['materials_column']): ?>
                                        <li><strong>مطلوب:</strong> تشغيل تحديث قاعدة البيانات لإضافة عمود materials</li>
                                    <?php endif; ?>
                                    <?php if (!$status['directorates_table']): ?>
                                        <li><strong>مطلوب:</strong> إنشاء جدول المديريات والمصالح</li>
                                    <?php endif; ?>
                                    <?php if ($status['materials_column'] && $status['directorates_table']): ?>
                                        <li><strong>ممتاز:</strong> قاعدة البيانات جاهزة للنظام الجديد!</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            
                        <?php endif; ?>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="text-center">
                            <?php if (!$status['materials_column'] || !$status['directorates_table']): ?>
                                <a href="database/update_database.php" class="btn btn-warning btn-lg me-2">
                                    <i class="fas fa-database me-2"></i>
                                    تحديث قاعدة البيانات
                                </a>
                            <?php endif; ?>
                            
                            <a href="office_supplies_new.php" class="btn btn-success btn-lg me-2">
                                <i class="fas fa-play me-2"></i>
                                تشغيل النظام الجديد
                            </a>
                            
                            <a href="test_final_improvements.html" class="btn btn-info btn-lg">
                                <i class="fas fa-test-tube me-2"></i>
                                اختبار النظام
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
